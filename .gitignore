# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore




server/logs/
server/output
similarity_results.csv
client_report_SPCS.csv

output/

# Node artifact files
node_modules/
dist/


# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

*.env
.env.prod
docker-compose.test.yml
sample_data

.venv/
review.md
server/output/client_report_SPCS.csv
server/output/client_report_SPCS.csv
