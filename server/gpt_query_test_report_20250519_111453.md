# GPT-Reformulated Query Test Report

## Executive Summary

**Test Date:** 2025-05-19 11:14:53

**Results Overview:**

| Metric | Value |
|--------|-------|
| Total Tests | 20 |
| Successful Tests | 12 |
| Success Rate | 60.0% |

---

## Test Configuration

- **API Endpoint:** http://localhost:8000/api/knowledge/query
- **GPT Model:** gpt-4.1-mini
- **Default Door Model:** Door-N1441
- **Sample Size:** 20

---

## Observation Type Analysis

### Distribution of Observation Types

| Observation Type | Count | Percentage |
|------------------|-------|------------|
| Visual | 20 | 100.0% |
| auditory | 5 | 25.0% |
| Positional | 11 | 55.0% |
| Error_code | 18 | 90.0% |
| Related_parts | 20 | 100.0% |

### Success Rates by Observation Type

| Observation Type | Success Rate | Successful Tests | Total Tests |
|------------------|--------------|------------------|-------------|
| Visual | 60.0% | 12 | 20 |
| auditory | 80.0% | 4 | 5 |
| Positional | 72.7% | 8 | 11 |
| Error_code | 55.6% | 10 | 18 |
| Related_parts | 60.0% | 12 | 20 |

### Success Rates by Number of Observation Types

| Observation Types | Success Rate | Successful Tests | Total Tests |
|-------------------|--------------|------------------|-------------|
| 2 | 100.0% | 1 | 1 |
| 3 | 42.9% | 3 | 7 |
| 4 | 55.6% | 5 | 9 |
| 5 | 100.0% | 3 | 3 |

---

### Rank Distribution for Successful Queries

| Rank | Count | Percentage |
|------|-------|------------|
| 1 | 11 | 91.7% |
| 3 | 1 | 8.3% |

### Success Rate by Door Model

| Door Model | Success Rate | Successful Tests | Total Tests |
|------------|--------------|------------------|-------------|
| Door-N464 | 50.0% | 2 | 4 |
| Door-N702 | 75.0% | 3 | 4 |
| Door-N0 | 100.0% | 1 | 1 |
| Door-N394 | 100.0% | 1 | 1 |
| Door-N119 | 0.0% | 0 | 1 |
| Door-N1823 | 100.0% | 2 | 2 |
| Door-N1784 | 100.0% | 1 | 1 |
| Door-N835 | 50.0% | 1 | 2 |
| Door-N1598 | 0.0% | 0 | 1 |
| Door-N1730 | 50.0% | 1 | 2 |
| Door-N971 | 0.0% | 0 | 1 |

---

## Successful Tests

### Test #1: Node ID N517 (Rank 1)

**Door Model:** Door-N464

**Original Node Name:** De deur beweegt niet bij poging tot openen en lijkt volledig stil te staan.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur beweegt niet bij poging tot openen en lijkt volledig stil te staan.
```

⚠️ **Error Code:** `ABE`

🔧 **Related Parts:**

**Generated Query:**
```
De draaideur beweegt niet en blijft volledig stil staan wanneer ik probeer deze te openen, en er verschijnt foutcode ABE. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De draaideur beweegt niet bij poging tot openen en geeft foutcode ABE, waarbij de deur volledig stil blijft staan.

Oorzaak: De mechanische koppeling tussen de motor en de deur is gebroken, waardoor de motor wel draait maar de deur niet beweegt.

Oplossing:
1. Schakel de stroom uit om veilig te kunnen werken.
2. Controleer de mechanische koppeling tussen motor en deur op beschadigingen of breuk.
3. Verwijder de defecte koppeling.
4. Plaats een nieuwe mechanische koppeling en zorg dat deze correct aansluit op zowel motor als deur.
5. Lijn de koppeling nauwkeurig uit om een goede verbinding te garanderen.
6. Zet de stroom weer aan en test de deur op correcte beweging zonder foutcode.
```

---

### Test #2: Node ID N783 (Rank 1)

**Door Model:** Door-N702

**Original Node Name:** De muntsleuf lijkt verstopt en er zit zichtbaar vuil rond de coin module.

**Observation Details:**

🔍 **Visual Observation:**
```
De muntsleuf lijkt verstopt en er zit zichtbaar vuil rond de coin module.
```

⚠️ **Error Code:** `CEF`

🔧 **Related Parts:** Coin module, Sensor unit

**Generated Query:**
```
Bij de draaideur met foutcode CEF zie ik dat de muntsleuf verstopt lijkt en er zichtbaar vuil rond de coin module zit. De coin module en de sensor unit zijn hierbij betrokken. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De muntsleuf is verstopt en er zit zichtbaar vuil rond de coin module van de Door-N702 draaideur met foutcode CEF.

Oorzaak: Statische elektriciteit trekt stofdeeltjes aan, waardoor de muntinvoer geblokkeerd raakt.

Oplossing:
1. Schakel de draaideur uit en zorg dat de coin module toegankelijk is.
2. Reinig de muntsleuf en de coin module grondig met een antistatisch spray om het vuil en stof te verwijderen.
3. Zorg dat de spray goed intrekt en verwijder eventueel losgekomen vuil met een zachte doek.
4. Controleer of de muntinvoer vrij is en test de coin module op correcte werking.
5. Zet de draaideur weer aan en controleer of foutcode CEF is verdwenen.
```

---

### Test #3: Node ID N86 (Rank 1)

**Door Model:** Door-N0

**Original Node Name:** De deur hapert tijdens het draaien en stopt onregelmatig.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur hapert tijdens het draaien en stopt onregelmatig.
```

📍 **Positional Observation:**
```
In de sensorbehuizing aan de deurzijde.
```

⚠️ **Error Code:** `ACDEF`

🔧 **Related Parts:**

**Generated Query:**
```
De draaideur hapert tijdens het draaien en stopt onregelmatig, waarbij de foutcode ACDEF wordt weergegeven en dit lijkt te gebeuren bij de sensorbehuizing aan de deurzijde. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De draaideur hapert tijdens het draaien en stopt onregelmatig, waarbij foutcode ACDEF wordt weergegeven bij de sensorbehuizing aan de deurzijde.

Oorzaak: De sensor in de behuizing geeft onjuiste signalen door stofophoping, wat leidt tot onregelmatig stoppen van de deur.

Oplossing:
1. Schakel de draaideur uit en zorg voor veilige toegang tot de sensorbehuizing aan de deurzijde.
2. Open de sensorbehuizing voorzichtig.
3. Reinig de sensor en de behuizing grondig, verwijder alle stofdeeltjes en vuil.
4. Controleer of de sensor vrij en schoon is, zodat deze correcte signalen kan afgeven.
5. Sluit de behuizing en zet de draaideur weer aan.
6. Test de deur om te bevestigen dat het haperen en onregelmatig stoppen is verholpen.
```

---

### Test #4: Node ID N455 (Rank 1)

**Door Model:** Door-N394

**Original Node Name:** De deur blijft gesloten ondanks een activatiepoging en toont geen knipperende indicatoren.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur blijft gesloten ondanks een activatiepoging en toont geen knipperende indicatoren.
```

👂 **auditory Observation:**
```
Een licht zoemend geluid is hoorbaar bij het bedieningspaneel, zonder mechanische beweging.
```

📍 **Positional Observation:**
```
Het probleem lijkt te zitten in het sensorboard achter het bedieningspaneel.
```

⚠️ **Error Code:** `ABCEF`

🔧 **Related Parts:**

**Generated Query:**
```
De draaideur blijft gesloten ondanks dat ik hem probeer te activeren en er knipperen geen indicatoren. Bij het bedieningspaneel hoor ik een licht zoemend geluid, maar er is geen mechanische beweging. Het lijkt erop dat het probleem zit in het sensorboard achter het bedieningspaneel en de foutcode die wordt weergegeven is ABCEF. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De draaideur blijft gesloten, er knipperen geen indicatoren, er is een zoemend geluid bij het bedieningspaneel en de foutcode ABCEF wordt weergegeven.

Oorzaak: De voeding van het sensorboard is onderbroken door een losse connector.

Oplossing:
1. Schakel de stroom uit om veilig te werken.
2. Controleer de connector van het sensorboard op losse verbindingen.
3. Reinig de contacten van de connector grondig om eventuele oxidatie of vuil te verwijderen.
4. Sluit de connector stevig aan en controleer de verbinding op stevigheid.
5. Breng een beschermkap aan over de connector om te voorkomen dat deze losraakt in de toekomst.
6. Schakel de stroom weer in en test de draaideur op correcte werking.
```

---

### Test #5: Node ID N1978 (Rank 1)

**Door Model:** Door-N1823

**Original Node Name:** De deur hapert duidelijk tijdens het openen en sluiten.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur hapert duidelijk tijdens het openen en sluiten.
```

👂 **auditory Observation:**
```
Er is een schurend geluid hoorbaar bij het bewegen van de deur.
```

📍 **Positional Observation:**
```
Het probleem bevindt zich bij het elektromechanische slot van de schuifdeur.
```

⚠️ **Error Code:** `F`

🔧 **Related Parts:**

**Generated Query:**
```
Bij het openen en sluiten van de draaideur hapert deze duidelijk en is er een schurend geluid hoorbaar, waarbij het probleem zich lijkt te bevinden bij het elektromechanische slot van de schuifdeur en foutcode F wordt weergegeven. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De schuifdeur hapert en maakt een schurend geluid tijdens het openen en sluiten.

Oorzaak: Dit wordt veroorzaakt door een verkeerd uitgelijnde tandriem in het elektromechanische slot, wat het haperen en geluid veroorzaakt.

Oplossing:
1. Demonteer het elektromechanische slot van de Door-N1823 schuifdeur.
2. Controleer de tandriemgeleiders op slijtage of beschadiging.
3. Vervang versleten of beschadigde tandriemgeleiders.
4. Monteer het elektromechanische slot weer correct terug.
5. Test de deur om te bevestigen dat het haperen en schurende geluid zijn verdwenen.
```

---

### Test #6: Node ID N1948 (Rank 3)

**Door Model:** Door-N1823

**Original Node Name:** De deur draait continu en lijkt niet te stoppen.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur draait continu en lijkt niet te stoppen.
```

👂 **auditory Observation:**
```
Een constant zoemend geluid is hoorbaar tijdens het draaien.
```

📍 **Positional Observation:**
```
Het probleem zit in het mechanisme dat de deur continu laat draaien.
```

⚠️ **Error Code:** `C`

🔧 **Related Parts:** Motor, Luchtgordijn sensor, Besturingsmodule

**Generated Query:**
```
De draaideur draait continu en lijkt niet te stoppen, terwijl er een constant zoemend geluid hoorbaar is tijdens het draaien. Het probleem lijkt te zitten in het mechanisme dat de deur continu laat draaien en er is foutcode C aanwezig. Kunnen de motor, de luchtgordijn sensor of de besturingsmodule hiermee te maken hebben? Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De draaideur draait continu en geeft foutcode C, terwijl het bedieningspaneel niet meer reageert bij het openen.

Oorzaak: De bedrading tussen het bedieningspaneel en de motorcontroller is losgeraakt, waardoor de communicatie en besturing uitvallen.

Oplossing:
1. Controleer de kabelverbindingen tussen het bedieningspaneel en de motorcontroller.
2. Identificeer eventuele beschadigde of losgeraakte kabels.
3. Vervang de beschadigde kabels door nieuwe, geschikte kabels.
4. Zorg dat alle verbindingen stevig en correct zijn aangesloten.
5. Test de draaideur opnieuw om te bevestigen dat het bedieningspaneel reageert en de deur normaal functioneert.
```

---

### Test #7: Node ID N778 (Rank 1)

**Door Model:** Door-N702

**Original Node Name:** De deur staat stil en beweegt niet bij bediening.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur staat stil en beweegt niet bij bediening.
```

📍 **Positional Observation:**
```
Het draaimechanisme van de deur zelf.
```

⚠️ **Error Code:** `AF`

🔧 **Related Parts:** Motor, Draaibaar scharnier

**Generated Query:**
```
De draaideur staat stil en beweegt niet bij bediening, terwijl ik het draaimechanisme van de deur zelf heb gecontroleerd. Er verschijnt foutcode AF en de motor en het draaibaar scharnier lijken betrokken te zijn. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De draaideur staat stil en reageert niet op bediening, met foutcode AF zichtbaar.

Oorzaak: De tandwielen in het draaimechanisme zijn versleten, waardoor de motor de deur niet kan laten bewegen.

Oplossing:
1. Schakel de stroom naar de draaideur uit om veilig te kunnen werken.
2. Open het draaimechanisme om toegang te krijgen tot de tandwielen.
3. Reinig de tandwielen indien nodig om vuil en oude vetresten te verwijderen.
4. Breng een hoogwaardig tandwielvet aan op alle tandwielen in het draaimechanisme.
5. Sluit het mechanisme en zet de stroom weer aan.
6. Test de deur op correcte beweging en controleer of de foutcode AF verdwijnt.

Deze procedure vermindert slijtage en verbetert de beweging van de deur.
```

---

### Test #8: Node ID N739 (Rank 1)

**Door Model:** Door-N702

**Original Node Name:** De deur beweegt niet bij het bedienen, lijkt vast te zitten.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur beweegt niet bij het bedienen, lijkt vast te zitten.
```

📍 **Positional Observation:**
```
Probleem zit bij het scharnier en het slotmechanisme van de deur.
```

⚠️ **Error Code:** `AB`

🔧 **Related Parts:**

**Generated Query:**
```
De draaideur beweegt niet wanneer ik hem bedien en lijkt vast te zitten. Het probleem lijkt te zitten bij het scharnier en het slotmechanisme van de deur, en er wordt foutcode AB weergegeven. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De draaideur beweegt niet bij bediening en zit vast bij het scharnier en slotmechanisme, met foutcode AB.

Oorzaak: De vergrendelingspen is verbogen, waardoor het slotmechanisme vastloopt en de deur niet kan bewegen.

Oplossing:
1. Open het slotmechanisme en lokaliseer de vergrendelingspen.
2. Gebruik een tang om de vergrendelingspen voorzichtig recht te buigen.
3. Smeer het slotmechanisme grondig om de soepelheid te herstellen.
4. Test de deurbediening om te controleren of de deur weer vrij beweegt.
```

---

### Test #9: Node ID N1788 (Rank 1)

**Door Model:** Door-N1784

**Original Node Name:** De schuifdeur blijft half open staan en sluit niet volledig.

**Observation Details:**

🔍 **Visual Observation:**
```
De schuifdeur blijft half open staan en sluit niet volledig.
```

📍 **Positional Observation:**
```
Het probleem zit in het sluitmechanisme van de schuifdeur.
```

⚠️ **Error Code:** `ABDEF`

🔧 **Related Parts:**

**Generated Query:**
```
De schuifdeur blijft half open staan en sluit niet volledig, waarbij het probleem lijkt te zitten in het sluitmechanisme van de schuifdeur en er foutcode ABDEF wordt weergegeven. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De schuifdeur (type Door-N1784) blijft half open staan en sluit niet volledig, met foutcode ABDEF.

Oorzaak: De elektronische besturingseenheid ontvangt onjuiste signalen door beschadigde bedrading tussen de besturingseenheid en het sluitmechanisme.

Oplossing:
1. Schakel de stroomtoevoer naar de deur uit om veilig te werken.
2. Inspecteer de bedrading tussen de besturingseenheid en het sluitmechanisme op beschadigingen of losse verbindingen.
3. Vervang alle beschadigde bedrading die onjuiste signalen kan veroorzaken.
4. Controleer na vervanging of de verbindingen stevig en correct zijn aangesloten.
5. Schakel de stroom weer in en test of de deur nu volledig sluit zonder foutcode.
```

---

### Test #10: Node ID N866 (Rank 1)

**Door Model:** Door-N835

**Original Node Name:** De deur sluit niet volledig en blijft net iets open staan.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur sluit niet volledig en blijft net iets open staan.
```

📍 **Positional Observation:**
```
Het slotmechanisme aan de rechter buitendeur
```

🔧 **Related Parts:** Slotmechanisme, Scharnier

**Generated Query:**
```
Bij de rechter buitendeur sluit de draaideur niet volledig en blijft net iets open staan. Dit lijkt te maken te hebben met het slotmechanisme en het scharnier. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De rechter buitendeur van de draaideur sluit niet volledig en blijft net iets open staan.

Oorzaak: Het deurslot is verkeerd afgesteld, waardoor de deur niet correct in het kozijn valt.

Oplossing:
1. Controleer de positie van de slotpen op het deurslot.
2. Stel de slotpen opnieuw uit zodat ze precies in de sluitplaat in het kozijn vallen.
3. Test de deur na afstelling om te bevestigen dat deze volledig sluit.
4. Herhaal indien nodig de uitlijning totdat de deur correct sluit zonder open te blijven staan.
```

---

### Test #11: Node ID N584 (Rank 1)

**Door Model:** Door-N464

**Original Node Name:** De draaideur staat stil en beweegt niet automatisch bij inschakeling.

**Observation Details:**

🔍 **Visual Observation:**
```
De draaideur staat stil en beweegt niet automatisch bij inschakeling.
```

🔧 **Related Parts:**

**Generated Query:**
```
De draaideur staat stil en beweegt niet automatisch wanneer ik hem inschakel. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: De draaideur (Door-N464) staat stil en beweegt niet automatisch bij inschakeling.

Oorzaak: De bewegingssensor detecteert geen aanwezigheid, waardoor de deur niet opent.

Oplossing:
1. Schakel de draaideur uit.
2. Voer een reset uit van de sensorsoftware om detectiefouten te corrigeren.
3. Schakel de draaideur weer in en controleer of de sensor nu beweging registreert en de deur automatisch opent.
```

---

### Test #12: Node ID N1752 (Rank 1)

**Door Model:** Door-N1730

**Original Node Name:** Het zijpaneel van de deur zit zichtbaar scheef en staat niet recht ten opzichte van het deurframe.

**Observation Details:**

🔍 **Visual Observation:**
```
Het zijpaneel van de deur zit zichtbaar scheef en staat niet recht ten opzichte van het deurframe.
```

👂 **auditory Observation:**
```
Er is een licht knerpend geluid hoorbaar bij het openen en sluiten van de deur.
```

⚠️ **Error Code:** `ABCEF`

🔧 **Related Parts:**

**Generated Query:**
```
Bij de draaideur valt op dat het zijpaneel scheef staat en niet recht ten opzichte van het deurframe, en er is een licht knerpend geluid te horen bij het openen en sluiten van de deur. Daarnaast verschijnt foutcode ABCEF. Hoe kan ik dit probleem oplossen?
```

**Generated Answer:**
```
Probleem: Het zijpaneel van de draaideur staat scheef en er is een licht knerpend geluid bij het openen en sluiten. Daarnaast verschijnt foutcode ABCEF.

Oorzaak: De bevestigingsschroeven van het zijpaneel zijn losgeraakt, waardoor het paneel scheef hangt en mogelijk geluid veroorzaakt.

Oplossing:
1. Controleer de bevestigingsschroeven van het zijpaneel op loszitten.
2. Draai alle bevestigingsschroeven stevig aan.
3. Zet de schroeven vast met een borgmiddel om hernieuwd losraken te voorkomen.
4. Controleer na het aandraaien of het zijpaneel recht staat en het knerpend geluid is verdwenen.
5. Reset de foutcode ABCEF volgens de standaardprocedure van deurtype Door-N1730.
```

---

## Failed Tests

### Failed Test #1: Node ID N482

**Door Model:** Door-N464

**Original Node Name:** De deur hapert en blijft halverwege de rotatie steken.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur hapert en blijft halverwege de rotatie steken.
```

📍 **Positional Observation:**
```
Probleem bevindt zich bij de aandrijving van de draaideur.
```

⚠️ **Error Code:** `F`

🔧 **Related Parts:** Motor, Kliklijst

**Generated Query:**
```
De draaideur hapert en blijft halverwege de rotatie steken, waarbij het probleem zich bevindt bij de aandrijving van de draaideur en foutcode F wordt weergegeven. De motor en de kliklijst lijken hierbij betrokken te zijn. Hoe kan ik dit probleem oplossen?
```

**Error:** Error sending API request:

---

### Failed Test #2: Node ID N287

**Door Model:** Door-N119

**Original Node Name:** Het bedieningspaneel toont geen enkele reactie bij aanraking.

**Observation Details:**

🔍 **Visual Observation:**
```
Het bedieningspaneel toont geen enkele reactie bij aanraking.
```

⚠️ **Error Code:** `CF`

🔧 **Related Parts:** Bedieningspaneel, Besturingsmodule

**Generated Query:**
```
Bij een draaideur met foutcode CF reageert het bedieningspaneel helemaal niet bij aanraking. De besturingsmodule lijkt hierbij betrokken te zijn. Hoe kan ik dit probleem oplossen?
```

**Error:** API request failed with status 500: {"detail":"Failed to process query"}

---

### Failed Test #3: Node ID N1678

**Door Model:** Door-N1598

**Original Node Name:** De deur hapert tijdens het draaien en stopt plotseling.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur hapert tijdens het draaien en stopt plotseling.
```

⚠️ **Error Code:** `EF`

🔧 **Related Parts:** Relais K1, Relais K4

**Generated Query:**
```
De draaideur hapert tijdens het draaien en stopt plotseling, waarbij foutcode EF verschijnt en de relais K1 en K4 betrokken zijn. Hoe kan ik dit probleem oplossen?
```

**Error:** Error sending API request:

---

### Failed Test #4: Node ID N880

**Door Model:** Door-N835

**Original Node Name:** De schuifdeur beweegt zichtbaar traag en stotterend tijdens het openen en sluiten.

**Observation Details:**

🔍 **Visual Observation:**
```
De schuifdeur beweegt zichtbaar traag en stotterend tijdens het openen en sluiten.
```

📍 **Positional Observation:**
```
Probleem zit bij de aandrijving van de schuifdeur onderaan.
```

⚠️ **Error Code:** `ABE`

🔧 **Related Parts:**

**Generated Query:**
```
De schuifdeur beweegt zichtbaar traag en stotterend tijdens het openen en sluiten, en het probleem lijkt te zitten bij de aandrijving van de schuifdeur onderaan. Daarbij verschijnt foutcode ABE. Hoe kan ik dit probleem oplossen?
```

**Error:** Error sending API request:

---

### Failed Test #5: Node ID N1769

**Door Model:** Door-N1730

**Original Node Name:** De deur hapert en stopt onregelmatig tijdens het openen en sluiten.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur hapert en stopt onregelmatig tijdens het openen en sluiten.
```

📍 **Positional Observation:**
```
Het probleem bevindt zich bij het scharniermechanisme van de deur.
```

⚠️ **Error Code:** `ABE`

🔧 **Related Parts:** Scharnier, Motormodule

**Generated Query:**
```
Bij een draaideur met foutcode ABE hapert en stopt de deur onregelmatig tijdens het openen en sluiten. Het probleem lijkt zich te bevinden bij het scharniermechanisme van de deur en betreft de scharnier en motormodule. Hoe kan ik dit probleem oplossen?
```

**Error:** Error sending API request:

---

### Failed Test #6: Node ID N470

**Door Model:** Door-N464

**Original Node Name:** De deur staat volledig open en beweegt niet terug naar de gesloten positie.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur staat volledig open en beweegt niet terug naar de gesloten positie.
```

⚠️ **Error Code:** `B`

🔧 **Related Parts:** Voedingskabel, UPS

**Generated Query:**
```
De draaideur staat volledig open en beweegt niet terug naar de gesloten positie, en er verschijnt foutcode B. Hierbij zijn de voedingskabel en de UPS betrokken. Hoe kan ik dit probleem oplossen?
```

**Error:** Error sending API request:

---

### Failed Test #7: Node ID N703

**Door Model:** Door-N702

**Original Node Name:** De deur hapert bij het draaien en stopt soms abrupt.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur hapert bij het draaien en stopt soms abrupt.
```

⚠️ **Error Code:** `ABDF`

🔧 **Related Parts:**

**Generated Query:**
```
De draaideur hapert tijdens het draaien en stopt soms abrupt, waarbij foutcode ABDF wordt weergegeven. Hoe kan ik dit probleem oplossen?
```

**Error:** Error sending API request:

---

### Failed Test #8: Node ID N1027

**Door Model:** Door-N971

**Original Node Name:** De deur beweegt niet bij het activeren van de bediening.

**Observation Details:**

🔍 **Visual Observation:**
```
De deur beweegt niet bij het activeren van de bediening.
```

👂 **auditory Observation:**
```
Er is geen motorgeluid hoorbaar bij poging tot openen.
```

⚠️ **Error Code:** `ACDEF`

🔧 **Related Parts:** Motor, EBS sensor

**Generated Query:**
```
Bij het activeren van de bediening beweegt de draaideur niet en er is geen motorgeluid hoorbaar. De foutcode ACDEF verschijnt en de motor en EBS sensor zijn betrokken. Hoe kan ik dit probleem oplossen?
```

**Error:** Error sending API request:

---

## Conclusion

The GPT query reformulation shows **poor performance**. Significant improvements are required.

For successful queries, the average rank of the correct observation was **1.17**.

The 'auditory' observation type showed the highest success rate at **80.0%**.

### Recommendations

1. **Improve GPT Prompt Engineering**: Refine the prompt to better capture the essence of various observation types.
2. **Enhance Vector Search**: Review vector search implementation to improve retrieval accuracy.
4. **Review Door Model Data**: Door model 'Door-N119' showed particularly poor performance.

### Test Generated: 2025-05-19 11:14:53
