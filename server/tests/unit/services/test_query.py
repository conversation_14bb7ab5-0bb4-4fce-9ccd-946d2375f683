import asyncio
from datetime import datetime

# Adjust these imports based on your actual module structure
from app.services.nlp.query.search_service import SearchService
from app.services.database.repositories.knowledge_repo import KnowledgeRepository
from app.services.embeddings.embedding import EmbeddingService
from app.services.nlp.query.path_processor import PathProcessor
from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgeType,
)


async def main():
    # Instantiate the required services
    knowledge_repo = KnowledgeRepository()
    print("Knowledge repository initialized.")
    embedding_service = EmbeddingService()
    print("Embedding service initialized.")
    path_processor = PathProcessor(
        knowledge_repo=knowledge_repo,
        mechanic_repo=None,  # Replace with mock or real MechanicRepository
        embedding_service=embedding_service,
    )
    print("Path processor initialized.")

    # Instantiate the search service with all dependencies
    search_service = SearchService(
        knowledge_repo=knowledge_repo,
        embedding_service=embedding_service,
        path_processor=path_processor,
    )
    print("Search service initialized.")

    # Create a test observation node
    observation_node = KnowledgeNode(
        id="2079",  # Replace with a real or mocked node ID
        type=KnowledgeType.OBSERVATION,
        name="Test Observation",
        description="Testing path lookup",
        created_at=datetime.now().isoformat(),
        updated_at=datetime.now().isoformat(),
        created_by="tester",
    )

    # Run the internal function (you might want to make it public for testing)
    paths = await search_service._get_paths_from_observation(
        observation_node=observation_node, limit=2
    )

    # Print the results
    for path in paths:
        print("---- PATH ----")
        for node in path.nodes:
            print(f"Node: {node.type} - {node.name} (ID: {node.id})")
        for rel in path.relationships:
            print(
                f"Rel: {rel.type} from {rel.source_id} to {rel.target_id} (trust: {rel.trust_score})"
            )
        print(f"Path trust score: {path.path_trust}")


if __name__ == "__main__":
    asyncio.run(main())
