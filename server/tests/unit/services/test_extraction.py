import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import json

from app.services.nlp.extraction import KnowledgeExtractionService
from app.models.domain.knowledge import KnowledgeType, RelationshipType
from app.core.exceptions import KnowledgeExtractionError


@pytest.fixture
def mock_openai_response():
    """Mock OpenAI API response."""

    class MockChoice:
        def __init__(self, content):
            self.message = MagicMock()
            self.message.content = content

    class MockResponse:
        def __init__(self, content):
            self.choices = [MockChoice(content)]

    extracted_data = {
        "spcs": {
            "symptom": {
                "name": "grinding noise while rotating",
                "description": "Door makes a grinding noise during rotation",
            },
            "problem": {
                "name": "rotation issue",
                "description": "Door has difficulty rotating smoothly",
            },
            "cause": {
                "name": "worn bearings in carrier assembly",
                "description": ("The bearings in the carrier assembly are worn out"),
            },
            "solution": {
                "name": "replace carrier bearings",
                "description": (
                    "Disassemble floor mounting, remove carrier, replace bearings with part RTX-4452"
                ),
                "parts_involved": [
                    "floor mounting",
                    "carrier assembly",
                    "bearings",
                ],
                "part_numbers": ["RTX-4452"],
            },
        },
        "door_model": "X3000",
        "environment": {
            "condition": "high traffic",
            "description": (
                "The door is in a high traffic area which accelerates wear"
            ),
        },
        "tacit_knowledge": [
            {
                "type": "TECHNIQUE",
                "name": "carrier removal trick",
                "description": (
                    "When removing the carrier, slightly loosen the top screws first to reduce tension"
                ),
            }
        ],
        "trust": {
            "symptom_identification": 0.9,
            "problem_identification": 0.85,
            "cause_identification": 0.95,
            "solution_efficacy": 0.8,
        },
    }

    return MockResponse(json.dumps(extracted_data))


@pytest.mark.asyncio
async def test_extract_knowledge(mock_openai_response):
    """Test knowledge extraction from text."""
    # Arrange
    extraction_service = KnowledgeExtractionService()

    # Mock dependencies
    extraction_service._init_openai = MagicMock()
    extraction_service.openai_client = MagicMock()
    extraction_service.openai_client.chat.completions.create.return_value = (
        mock_openai_response
    )
    extraction_service._get_experience_years = AsyncMock(return_value=0.8)
    extraction_service.mechanic_repo = MagicMock()
    extraction_service.door_repo = MagicMock()

    # Sample input
    text = """
    I was working on an X3000 door that had a grinding noise while rotating. The customer said
    it started about a week ago and was getting worse. When I examined it, I found that the
    bearings in the carrier assembly were completely worn out. I had to disassemble the floor
    mounting, remove the carrier, and replace the bearings with part RTX-4452. The door is in
    a high traffic area which is why the bearings wore out faster than normal. One trick I've
    found is to slightly loosen the top screws first when removing the carrier to reduce tension.
    """

    # Act
    nodes, relationships = await extraction_service.extract_knowledge(
        text, "mechanic-123"
    )

    # Assert
    # Check that we have the expected number of nodes
    assert (
        len(nodes) == 7
    )  # Symptom, Problem, Cause, Solution, Door Type, Environment, Tacit Knowledge

    # Check that we have the expected number of relationships
    assert (
        len(relationships) >= 4
    )  # SPCS relationships + door type + environment + tacit

    # Check specific node types
    node_types = [node.type for node in nodes]
    assert KnowledgeType.SYMPTOM in node_types
    assert KnowledgeType.PROBLEM in node_types
    assert KnowledgeType.CAUSE in node_types
    assert KnowledgeType.SOLUTION in node_types
    assert KnowledgeType.TECHNIQUE in node_types

    # Check specific relationship types
    rel_types = [rel.type for rel in relationships]
    assert RelationshipType.INDICATES in rel_types
    assert RelationshipType.CAUSED_BY in rel_types
    assert RelationshipType.RESOLVED_BY in rel_types
    assert RelationshipType.APPLIES_TO in rel_types


@pytest.mark.asyncio
async def test_extract_knowledge_missing_door_model():
    """Test knowledge extraction with missing door model."""
    # Arrange
    extraction_service = KnowledgeExtractionService()

    # Mock dependencies - similar to first test
    extraction_service._init_openai = MagicMock()
    extraction_service.openai_client = MagicMock()
    extraction_service._get_experience_years = AsyncMock(return_value=0.8)
    extraction_service.mechanic_repo = MagicMock()
    extraction_service.door_repo = MagicMock()

    # Mock the _extract_door_model method to return None
    extraction_service._extract_door_model = MagicMock(return_value=None)

    # Sample input without door model
    text = """
    I was working on a door that had a grinding noise while rotating. The bearings were worn out.
    I replaced the bearings and now it works fine.
    """

    # Act & Assert
    with pytest.raises(KnowledgeExtractionError) as excinfo:
        await extraction_service.extract_knowledge(text, "mechanic-123")

    assert "Door model" in str(excinfo.value)
