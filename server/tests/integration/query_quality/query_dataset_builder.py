# query_dataset_builder.py

import asyncio
import csv
import os
from typing import List, Dict, Any

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

load_dotenv()

NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USERNAME = os.getenv("NEO4J_USERNAME", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")
CSV_FILENAME = "manual_query_dataset.csv"


class ObservationSampler:
    def __init__(self):
        self.driver = AsyncGraphDatabase.driver(
            NEO4J_URI, auth=(NEO4J_USERNAME, NEO4J_PASSWORD)
        )

    async def sample_observations(self, limit=50) -> List[Dict[str, Any]]:
        query = """
        MATCH (o:OBSERVATION)
        WHERE o.visual_observation IS NOT NULL AND o.visual_observation <> ''
        RETURN o.id as id,
               o.name as name,
               o.visual_observation as visual_observation,
               o.auditory_observation as auditory_observation,
               o.positional_observation as positional_observation,
               o.error_codes as error_codes,
               o.related_parts as related_parts
        ORDER BY rand()
        LIMIT $limit
        """
        async with self.driver.session() as session:
            result = await session.run(query, {"limit": limit})
            return [record.data() async for record in result]

    async def get_door_model(self, observation_id: str) -> str:
        query = """
        MATCH (o:OBSERVATION {id: $observation_id})-[:OBSERVED_WITH]->(c:CAUSE)-[:RESOLVED_BY]->(s:SOLUTION)-[:APPLIES_TO]->(d:DOOR_TYPE)
        RETURN d.model as door_model
        LIMIT 1
        """
        try:
            async with self.driver.session() as session:
                result = await session.run(query, {"observation_id": observation_id})
                record = await result.single()
                return (
                    record["door_model"]
                    if record and record["door_model"]
                    else "Door-N1441"
                )
        except:
            return "Door-N1441"

    async def close(self):
        await self.driver.close()


async def build_query_dataset():
    sampler = ObservationSampler()
    samples = await sampler.sample_observations(limit=50)

    if not samples:
        print("No observation nodes found.")
        return

    existing_ids = set()
    if os.path.exists(CSV_FILENAME):
        with open(CSV_FILENAME, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            existing_ids = {row["id"] for row in reader}

    with open(CSV_FILENAME, "a", encoding="utf-8", newline="") as csvfile:
        fieldnames = [
            "id",
            "name",
            "visual_observation",
            "auditory_observation",
            "positional_observation",
            "error_codes",
            "related_parts",
            "door_model",
            "custom_query",
            "comment",
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        if csvfile.tell() == 0:
            writer.writeheader()

        for obs in samples:
            if obs["id"] in existing_ids:
                continue

            print("\n" + "=" * 60)
            print(f"🆔 Observation ID: {obs['id']}")
            print(f"🧠 Name: {obs.get('name', '')}")
            print("🔎 Observations:")

            if obs.get("visual_observation"):
                print(f"👀 Visual: {obs['visual_observation']}")
            if obs.get("auditory_observation"):
                print(f"👂 Auditieve: {obs['auditory_observation']}")
            if obs.get("positional_observation"):
                print(f"📍 Positional: {obs['positional_observation']}")
            if obs.get("error_codes"):
                print(f"⚠️ Foutcode: {obs['error_codes']}")
            if obs.get("related_parts"):
                print(f"🔧 Onderdelen: {obs['related_parts']}")

            door_model = await sampler.get_door_model(obs["id"])
            print(f"🚪 Door Model: {door_model}")
            print("=" * 60)

            user_input = input("✏️  Type your query or press ENTER to skip: ").strip()

            if not user_input:
                print("⏭️  Skipped.")
                continue

            comment = input(
                "💬 (Optional) Type a comment or hit ENTER to skip: "
            ).strip()

            writer.writerow(
                {
                    "id": obs["id"],
                    "name": obs.get("name", ""),
                    "visual_observation": obs.get("visual_observation", ""),
                    "auditory_observation": obs.get("auditory_observation", ""),
                    "positional_observation": obs.get("positional_observation", ""),
                    "error_codes": obs.get("error_codes", ""),
                    "related_parts": obs.get("related_parts", ""),
                    "door_model": door_model,
                    "custom_query": user_input,
                    "comment": comment,
                }
            )
            csvfile.flush()
            print("✅ Saved.")

    await sampler.close()


if __name__ == "__main__":
    asyncio.run(build_query_dataset())
