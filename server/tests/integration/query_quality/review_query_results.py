import streamlit as st
import pandas as pd
import json

st.set_page_config(page_title="Query Results Review", layout="centered")

st.title("🔎 GPT Query Results Review")

# File selector
csv_file = st.file_uploader("Upload results CSV file", type=["csv"])

if csv_file is not None:
    df = pd.read_csv(csv_file)
    total = len(df)

    if total == 0:
        st.warning("No data found in CSV.")
        st.stop()

    # Streamlit state for index
    if "index" not in st.session_state:
        st.session_state.index = 0

    # Navigation
    col1, col2, col3 = st.columns([1, 2, 1])
    with col1:
        if st.button("⬅️ Previous", disabled=st.session_state.index <= 0):
            st.session_state.index = max(st.session_state.index - 1, 0)
    with col3:
        if st.button("Next ➡️", disabled=st.session_state.index >= total - 1):
            st.session_state.index = min(st.session_state.index + 1, total - 1)

    idx = st.session_state.index
    st.markdown(
        f"<p style='text-align:center'><b>Query {idx+1} / {total}</b></p>",
        unsafe_allow_html=True,
    )

    row = df.iloc[idx]

    # Main Card
    st.markdown("---")
    st.subheader(f"🔢 Node ID: {row.get('node_id', 'N/A')}")
    st.markdown(f"**Door Model:** `{row.get('door_model', 'N/A')}`")
    st.markdown(
        f"**Rank in Top 5:** <span style='color: {'#059669' if str(row.get('rank_in_top5','')) in '12345' else '#ef4444'}'><b>{row.get('rank_in_top5','')}</b></span>",
        unsafe_allow_html=True,
    )
    st.markdown("---")

    # ── Show Query ──────────────────────────────────────────────────────────
    st.markdown("### 📝 Generated Query")

    query_html = f"""
    <div style="
        background:#f5f5fa;
        padding:12px 16px;
        border-radius:6px;
        border:1px solid #e4e4ec;
        font-family:Menlo,Consolas,monospace;
        white-space:pre-wrap;          /* keeps newlines, allows wrapping */
        word-break:break-word;         /* breaks long words if needed     */
    ">
    {row.get("query", "")}
    </div>
    """

    st.markdown(query_html, unsafe_allow_html=True)

    # Show Original Observations (grouped and with emoji)
    st.markdown("### 🧾 Original Observations")
    obs_fields = [
        ("visual_observation", "🔍 Visueel"),
        ("auditory_observation", "👂 Auditief"),
        ("positional_observation", "📍 Positioneel"),
        ("error_codes", "⚠️ Foutcode"),
        ("related_parts", "🔧 Onderdelen"),
    ]
    for key, label in obs_fields:
        val = str(row.get(key, ""))
        if pd.notna(val) and val.strip():
            st.markdown(f"**{label}:**")
            if key == "related_parts" and val.startswith("["):
                try:
                    parsed = json.loads(val)
                    val = (
                        ", ".join(map(str, parsed))
                        if isinstance(parsed, list)
                        else str(parsed)
                    )
                except Exception:
                    pass
            st.code(val, language="markdown")

    st.markdown("---")
    st.markdown("### ⭐ Top 5 Results")
    for i in range(1, 6):
        col = f"top{i}_obs"
        result = row.get(col, "")
        if pd.notna(result) and result.strip():
            try:
                data = json.loads(result)
                with st.expander(
                    f"Top {i} Observation (ID: {data.get('id','?')})", expanded=(i == 1)
                ):
                    for f, label in obs_fields:
                        val = data.get(f, "")
                        if val:
                            st.markdown(f"- **{label}:** `{val}`")
                    st.markdown(f"**Name:** {data.get('name','')}")
                    st.markdown(f"**Description:** {data.get('description','')}")
                    st.markdown(f"**Observation ID:** `{data.get('id','')}`")
            except Exception:
                st.markdown(f"Top {i}: Could not parse JSON")
        else:
            st.markdown(f"*No Top {i} result*")

    st.markdown("---")
    st.markdown("### 📦 Raw Row Data (Debug)")
    st.json(row.to_dict())

else:
    st.info("Please upload a CSV file with your query results to begin.")
