import asyncio
import logging
import os
from dotenv import load_dotenv
import httpx

from app.models.schemas.knowledge import (
    QueryObservationRequest,
)


# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class SimpleQueryTester:
    """
    Simple tester that runs a single hardcoded query and returns top 5 results.
    """

    def __init__(self, api_url: str = "http://localhost:8000"):
        self.api_url = api_url
        self.query_endpoint = f"{api_url}/api/knowledge/query-threshold-test"
        self.auth_token = None
        self.default_door_model = "Door-N1441"

    def load_env(self):
        """Load environment variables from .env file."""
        load_dotenv()

    async def login(self, username: str, password: str) -> bool:
        """Login to the API and store the authentication token."""
        login_url = f"{self.api_url}/api/mechanics/token"
        data = {"username": username, "password": password}

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(login_url, data=data)

                if response.status_code == 200:
                    result = response.json()
                    self.auth_token = result.get("access_token")
                    logger.info("Successfully logged in")
                    return True
                else:
                    logger.error(
                        f"Login failed: {response.status_code} - {response.text}"
                    )
                    return False
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return False

    async def run_query(self, query_observation: dict) -> dict:
        """Run a query via the API endpoint."""

        payload = query_observation.model_dump()
        logger.info(f"Sending query for door model")

        try:
            async with httpx.AsyncClient() as client:
                headers = {}
                if self.auth_token:
                    headers["Authorization"] = f"Bearer {self.auth_token}"

                response = await client.post(
                    self.query_endpoint,
                    json=payload,
                    headers=headers,
                    timeout=30.0,
                )

                if response.status_code == 200:
                    result = response.json()
                    logger.info(
                        f"Query successful, received {len(result.get('paths', []))} paths"
                    )
                    return result
                else:
                    error_msg = f"API request failed with status {response.status_code}: {response.text}"
                    logger.error(error_msg)
                    return {"error": error_msg}

        except Exception as e:
            error_msg = f"Error sending API request: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def get_top_5_observations(self, response: dict) -> list:
        """Extract top 5 observation nodes from the response."""
        if "error" in response or not response.get("paths"):
            return []

        observations = []
        paths = response.get("paths", [])[:5]  # Get top 5 paths

        for i, path in enumerate(paths):
            for node in path.get("nodes", []):
                if node.get("type") == "OBSERVATION":
                    # Flatten properties into the node dict
                    flattened = dict(node)
                    props = flattened.pop("properties", {})
                    flattened.update(props)
                    observations.append({"rank": i + 1, "observation": flattened})
                    break  # Only take first observation from each path

        return observations

    async def test_hardcoded_query(self):
        """Test with a hardcoded observation query."""

        # Hardcoded observation and query
        hardcoded_query = {
            "query_intent": "observation_analysis",  # One of: "observation_analysis", "cause_identification", "solution_finding", "general_info"
            "language": "nl",
            "observation": "",
            "visual_observation": "De deur stopt onregelmatig en blijft soms halverwege openstaan",
            "auditory_observation": "Een herhaaldelijk tik- of klikgeluid is hoorbaar bij het bedienen van de deur.",
            "positional_observation": "",
            "error_codes": "E",
            "related_parts": [],
            "door_model": "",
            "context_info": {},
        }
        hardcoded_query = QueryObservationRequest(**hardcoded_query)
        logger.info(f"Hardcoded query: {hardcoded_query}")

        logger.info("Testing hardcoded query...")
        logger.info(f"Query: {hardcoded_query}")

        # Run the query
        response = await self.run_query(hardcoded_query)

        if "error" in response:
            logger.error(f"Query failed: {response['error']}")
            return

        # Get top 5 observations
        top_observations = self.get_top_5_observations(response)

        # Display results
        logger.info(f"\n--- TOP 5 OBSERVATION RESULTS ---")
        logger.info(f"Total paths found: {len(response.get('paths', []))}")
        logger.info(f"Observations in top 5: {len(top_observations)}")

        for obs_result in top_observations:

            rank = obs_result["rank"]
            obs = obs_result["observation"]
            if (
                obs.get("auditory_observation")
                != "Een herhaaldelijk tik- of klikgeluid is hoorbaar bij het bedienen van de deur."
            ):
                continue

            logger.info(f"\n--- RANK {rank} ---")
            logger.info(f"ID: {obs.get('id', 'N/A')}")
            logger.info(f"Name: {obs.get('name', 'N/A')}")
            logger.info(f"Tier: {obs.get('tier', 'N/A')}")
            logger.info(f"Source: {obs.get('source', 'N/A')}")
            logger.info(f"Scope: {obs.get('scope', 'N/A')}")

            if obs.get("visual_observation"):
                logger.info(
                    f"Visual: {obs['visual_observation']}, Similarity: {obs.get('visual_score', 'N/A')}"
                )
            if obs.get("auditory_observation"):
                logger.info(
                    f"Auditory: {obs['auditory_observation']}, Similarity: {obs.get('auditory_score', 'N/A')}"
                )
            if obs.get("positional_observation"):
                logger.info(
                    f"Positional: {obs['positional_observation']}, Similarity: {obs.get('positional_score', 'N/A')}"
                )
            if obs.get("error_codes"):
                logger.info(f"Error Code: {obs['error_codes']}")
            if obs.get("related_parts"):
                logger.info(f"Related Parts: {obs['related_parts']}")

        # Show answer if available
        if response.get("answer"):
            logger.info(f"\n--- GENERATED ANSWER ---")
            logger.info(response["answer"])


async def main():
    """Run the simplified test."""
    logger.info("Starting simplified query test")

    # Login credentials
    username = "<EMAIL>"
    password = "admin123"

    tester = SimpleQueryTester(api_url="http://localhost:8000")

    try:
        # Load environment variables
        tester.load_env()

        # Login
        if not await tester.login(username, password):
            logger.error("Failed to authenticate with API")
            return

        # Run the test
        await tester.test_hardcoded_query()

    except Exception as e:
        logger.error(f"Test failed: {e}")

    logger.info("Test complete")


if __name__ == "__main__":
    asyncio.run(main())
