import asyncio
import logging
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv
from openai import OpenAI
import random
import httpx
from neo4j import AsyncGraphDatabase
import numpy as np
import asyncio
import csv
from typing import Dict, Any
from pathlib import Path
import argparse
import pandas as pd

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("combined_query_test.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)
# logging.disable(logging.CRITICAL)


def generate_sync_openai_query(prompt: str, api_key: str) -> Optional[str]:
    api_key = os.getenv("OPENAI_API_KEY")
    client = OpenAI(api_key=api_key)
    try:
        response = client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        logger.error(f"Error generating query with OpenAI: {e}")
        return None


class CombinedQueryTester:
    """
    Tester that connects to Neo4j to sample nodes but uses the API for queries.
    """

    def __init__(
        self,
        api_url: str = "http://localhost:8000",
        neo4j_uri: str = "bolt://localhost:7687",
        neo4j_username: str = "neo4j",
        neo4j_password: str = "password",
    ):
        self.api_url = api_url
        self.query_endpoint = f"{api_url}/api/knowledge/query-test"
        self.neo4j_uri = neo4j_uri
        self.neo4j_username = neo4j_username
        self.neo4j_password = neo4j_password
        self.driver = None
        self.results = []
        self.default_door_model = "Door-N1441"  # Fallback door model
        self.auth_token = None

    def load_env(self):
        """Load environment variables from .env file."""
        load_dotenv()
        return os.getenv("OPENAI_API_KEY")

    async def login(self, username: str, password: str) -> bool:
        """
        Login to the API and store the authentication token.
        """
        login_url = f"{self.api_url}/api/mechanics/token"

        # Form data (not JSON!)
        data = {"username": username, "password": password}

        try:
            async with httpx.AsyncClient() as client:
                # Send as form data, not JSON
                response = await client.post(login_url, data=data)

                if response.status_code == 200:
                    result = response.json()
                    self.auth_token = result.get("access_token")
                    logger.info("Successfully logged in")
                    return True
                else:
                    logger.error(
                        f"Login failed: {response.status_code} - {response.text}"
                    )
                    return False
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return False

    async def connect_to_neo4j(self):
        """Connect to Neo4j database."""
        logger.info(f"Connecting to Neo4j at {self.neo4j_uri}")
        try:
            self.driver = AsyncGraphDatabase.driver(
                self.neo4j_uri, auth=(self.neo4j_username, self.neo4j_password)
            )
            # Test connection
            await self.driver.verify_connectivity()
            logger.info("Successfully connected to Neo4j")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {str(e)}")
            return False

    async def close_neo4j(self):
        """Close Neo4j connection."""
        if self.driver:
            await self.driver.close()
            logger.info("Neo4j connection closed")

    async def sample_observation_nodes(self, limit: int = 20) -> List[Dict[str, Any]]:
        """
        Sample observation nodes with visual observation data, with put-back (replacement).
        """
        if not self.driver:
            if not await self.connect_to_neo4j():
                return []

        logger.info(
            f"Sampling {limit} observation nodes (with replacement) from all available nodes with visual data"
        )

        # First, get all candidates (NO LIMIT)
        query = """
        MATCH (o:OBSERVATION)
        WHERE o.visual_observation IS NOT NULL AND o.visual_observation <> ''
        RETURN o.id as id, o.name as name, o.description as description,
            o.visual_observation as visual_observation,
            o.auditory_observation as auditory_observation,
            o.positional_observation as positional_observation,
            o.error_codes as error_codes,
            o.related_parts as related_parts
        """

        try:
            async with self.driver.session() as session:
                result = await session.run(query)
                all_nodes = []
                async for record in result:
                    node = {
                        "id": record["id"],
                        "name": record["name"],
                        "description": record["description"],
                        "visual_observation": record["visual_observation"],
                        "auditory_observation": record["auditory_observation"],
                        "positional_observation": record["positional_observation"],
                        "error_codes": record["error_codes"],
                        "related_parts": record["related_parts"],
                    }
                    all_nodes.append(node)

            if not all_nodes:
                logger.warning("No nodes found in database for sampling")
                return []

            # Now sample with put-back (replacement)
            sampled = random.choices(all_nodes, k=limit)
            logger.info(
                f"Sampled {len(sampled)} nodes with put-back from {len(all_nodes)} candidates."
            )
            return sampled

        except Exception as e:
            logger.error(f"Error sampling nodes: {str(e)}")
            return []

    async def get_door_model_for_observation(
        self, observation_id: str
    ) -> Optional[str]:
        """
        Find the door model associated with an observation by traversing
        the path OBSERVATION -> CAUSE -> SOLUTION -> DOOR_TYPE.

        Args:
            observation_id: The ID of the observation node

        Returns:
            Door model string or None if not found
        """
        if not self.driver:
            if not await self.connect_to_neo4j():
                return None

        logger.info(f"Finding door model for observation {observation_id}")

        # Query to find door models connected to this observation
        query = """
        MATCH (o:OBSERVATION {id: $observation_id})-[:OBSERVED_WITH]->(c:CAUSE)-[:RESOLVED_BY]->(s:SOLUTION)-[:APPLIES_TO]->(d:DOOR_TYPE)
        RETURN d.model as door_model
        LIMIT 1
        """

        try:
            async with self.driver.session() as session:
                result = await session.run(query, {"observation_id": observation_id})
                record = await result.single()

                if record and record["door_model"]:
                    door_model = record["door_model"]
                    logger.info(
                        f"Found door model {door_model} for observation {observation_id}"
                    )
                    return door_model
                else:
                    logger.warning(
                        f"No door model found for observation {observation_id}, using default"
                    )
                    return self.default_door_model

        except Exception as e:
            logger.error(
                f"Error finding door model for observation {observation_id}: {str(e)}"
            )
            return self.default_door_model

    async def generate_query_from_observation(self, node: Dict[str, Any]) -> str:
        """
        Generate a natural language query from a node's observations using GPT.
        Uses only the available observation data without adding invented details.

        Args:
            node: The observation node

        Returns:
            Generated query string in Dutch
        """

        # Check if node has any observation data we can use
        has_observation_data = any(
            [
                node.get("visual_observation"),
                node.get("auditory_observation"),
                node.get("positional_observation"),
                node.get("error_codes"),
                node.get("related_parts")
                and node.get("related_parts") != [],  # Check if not empty list
            ]
        )

        if not has_observation_data:
            logger.error(f"Node {node.get('id')} has no usable observation data")
            return None

        api_key = self.load_env()
        if not api_key:
            logger.error("OpenAI API key not found in .env file")
            return None

        client = OpenAI(api_key=api_key)

        odds_of_missing_visual = 0.5
        odds_of_missing_auditory = 0.5
        odds_of_missing_positional = 0.5
        odds_of_missing_error_code = 0.5
        odds_of_missing_any_related_parts = 0.8
        similarity_score_visual = random.uniform(0, 1)
        similarity_score_audiorial = random.uniform(0, 1)
        similarity_score_positional = random.uniform(0, 1)
        similarity_score_related_parts = np.random.beta(10, 2)

        missing_visual = random.random() < odds_of_missing_visual
        missing_auditory = random.random() < odds_of_missing_auditory
        missing_positional = random.random() < odds_of_missing_positional
        missing_error_code = random.random() < odds_of_missing_error_code
        missing_any_related_parts = random.random() < odds_of_missing_any_related_parts

        # Construct observation description using only available data
        observation_description = []

        if node.get("visual_observation"):
            if not missing_visual:
                observation_description.append(
                    f"Visuele observatie: {node['visual_observation']}"
                )
                observation_description.append(
                    f"Visuele observatie herformuleren score: {similarity_score_visual:.2f}"
                )

        if node.get("auditory_observation"):
            if not missing_auditory:
                observation_description.append(
                    f"Auditieve observatie: {node['auditory_observation']}"
                )
                observation_description.append(
                    f"Auditieve observatie herformuleren score: {similarity_score_audiorial:.2f}"
                )

        if node.get("positional_observation"):
            if not missing_positional:
                observation_description.append(
                    f"Positionele observatie: {node['positional_observation']}"
                )
                observation_description.append(
                    f"Positionele observatie herformuleren score: {similarity_score_positional:.2f}"
                )

        error_code_text = ""
        if node.get("error_codes"):
            if not missing_error_code:
                error_code_text = f" met foutcode {node['error_codes']}"
                observation_description.append(f"Foutcode: {node['error_codes']}")

        # Assume these are already defined
        parts = node.get("related_parts", [])

        # Initialize parts_text as None
        parts_text = None

        # Step 1: Parse related_parts
        if parts and parts != []:
            if isinstance(parts, str):
                try:
                    parts_list = json.loads(parts)
                    if isinstance(parts_list, list):
                        pass  # use as is
                    else:
                        parts_list = []
                except:
                    # If not parseable, treat it as a single part string
                    parts_list = [parts]
            elif isinstance(parts, list):
                parts_list = parts
            else:
                parts_list = [str(parts)]  # wrap single item in list
        else:
            parts_list = []

        # Step 2: Randomly drop items if needed
        number_of_parts_missing = 0
        if missing_any_related_parts and parts_list:
            num_to_keep = max(0, len(parts_list) - random.randint(0, len(parts_list)))
            number_of_parts_missing = len(parts_list) - num_to_keep
            parts_list = (
                random.sample(parts_list, num_to_keep) if num_to_keep > 0 else []
            )

        related_parts_text = ""
        # Step 3: Generate text
        if parts_list:
            parts_text = ", ".join(parts_list)
            related_parts_text = (
                f" waarbij de volgende onderdelen betrokken zijn: {parts_text}"
            )
            observation_description.append(f"Gerelateerde onderdelen: {parts_text}")
            observation_description.append(
                f"Gerelateerd onderdelen herformuleren score: {similarity_score_related_parts:.2f}"
            )

        # Join all observations
        observations_text = "\n".join(observation_description)

        prompt = f"""
        Je bent een monteur die een probleem beschrijft met een draaideur.

        Formuleer op een natuurlijke manier in het Nederlands een vraag over het volgende probleem met een draaideur{error_code_text}{related_parts_text}:

        {observations_text}

        Voor het genereren van de vraag krijg je de volgende extra instructie:

        Bij elke observatievariabele (zoals visueel, auditief, positioneel, foutcode, onderdelen) hoort een cijfer tussen 0 en 1. Dit cijfer bepaalt hoe sterk je je moet houden aan de originele tekst van die variabele. Hieronder zie je hoe je je gedrag aanpast op basis van de waarde:

        - **Score = 0** → Volledige herformulering, met informatieverlies toegestaan
        - Gebruik GEEN ENKEL woord of zinsdeel uit de originele input.
        - Je mag stukken informatie WEGLATEN als dat helpt om het anders te formuleren.
        - Gebruik compleet andere woorden voor dezelfde betekenis.
        - Voorbeeld (visueel):
            Origineel: *"De deur hapert tijdens het sluiten en stopt halverwege de beweging."*
            Herformuleerd: *"Deur roteert maar tot de helft."*

        - **Score = 0.25** → Sterk herformuleerd, met een paar woorden hetzelfde
        - De zinsstructuur moet totaal anders zijn.
        - Je mag informatie gedeeltelijk weglaten.
        - Enkele losse woorden mogen hetzelfde blijven.
        - Voorbeeld: *"Het geluid stopt abrupt."* → *"Er is plots niets meer hoorbaar."*

        - **Score = 0.5** → Matige herformulering met behoud van betekenis
        - Houd alle informatie bij, maar herschrijf het in andere woorden.
        - Enkele kernwoorden mogen hetzelfde blijven.
        - Voorbeeld (auditief):
            Origineel: *"Er is geen geluid van beweging te horen, alleen een lichte zoem van de motor."*
            Herformuleerd: *"Ik hoor een soort gezoem bij de motor."*

        - **Score = 0.75** → Lichte herformulering met synoniemen of minimale aanpassing
        - Behoud alle informatie.
        - Alleen kleine woordkeuzeveranderingen (zoals synoniemen).
        - Voorbeeld (positioneel):
            Origineel: *"Bovenin bij de sleepringen en de wielen."*
            Herformuleerd: *"Het zit bij de sleepringen en banden."*

        - **Score = 1** → Exacte overname van originele tekst
        - Gebruik de originele tekst letterlijk, zonder enige verandering.
        - De formulering en woordkeus moeten 100% identiek zijn.

        Let dus goed op welk getal er gegeven is bij elke variabele in de observatie en de mate van herschrijven die daarbij hoort. Wees dus creatief!

        BELANGRIJKE INSTRUCTIES:
        1. Gebruik ALLEEN de bovenstaande informatie. Voeg GEEN extra informatie toe die niet in de observatie staat.
        2. Verzin GEEN symptomen, onderdelen of details die niet expliciet genoemd worden in de observaties.
        3. Als er alleen een foutcode is, vraag dan alleen naar die foutcode zonder extra details te verzinnen.
        4. Vermijd aannames over sensors, mechanismen of gedrag van de deur tenzij deze expliciet in de observatie staan.
        5. Maak GEEN suggesties over de mogelijk oorzaken/betrokken onderdelen

        Eindig je vraag met "Hoe kan ik dit probleem oplossen?" of een vergelijkbare vraag om hulp.
        Zorg ervoor dat je vraag alle gegeven informatie gebruikt zonder extra details toe te voegen.
        """

        in_original = {
            "visual_observation": 1 if node.get("visual_observation") else 0,
            "auditory_observation": 1 if node.get("auditory_observation") else 0,
            "positional_observation": 1 if node.get("positional_observation") else 0,
            "error_codes": 1 if node.get("error_codes") else 0,
            "related_parts": (
                1
                if (node.get("related_parts") and node.get("related_parts") != [])
                else 0
            ),
        }

        in_query = {
            "visual_observation": int(not missing_visual),
            "auditory_observation": int(not missing_auditory),
            "positional_observation": int(not missing_positional),
            "error_codes": int(not missing_error_code),
            "related_parts": int(not missing_any_related_parts),
        }

        # logger.info(f"in_original: {in_original}")
        # logger.info(f"in_query: {in_query}")
        loop = asyncio.get_running_loop()

        try:
            query = await loop.run_in_executor(
                None, generate_sync_openai_query, prompt, api_key
            )
            if not query:
                return None, None, None

            return query, in_original, in_query

        # try:
        #     # logger.info(f"Generating prompt for OpenAI: {prompt}")
        #     response = client.chat.completions.create(
        #         model="gpt-4.1-mini",
        #         messages=[{"role": "user", "content": prompt}],
        #         temperature=0.3,  # Lower temperature for more conservative outputs
        #     )

        #     query = response.choices[0].message.content.strip()
        #     logger.info(f"Generated query: {query}")
        #     return query, in_original, in_query

        except Exception as e:
            logger.error(f"Error generating query with OpenAI: {str(e)}")
            return None, None, None

    async def generate_openai_completion(prompt: str, api_key: str) -> Optional[str]:
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

        payload = {
            "model": "gpt-4.1-mini",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.3,
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers=headers,
                json=payload,
            )

            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"].strip()
            else:
                logger.error(
                    f"OpenAI API error {response.status_code}: {response.text}"
                )
                return None

    async def run_query(
        self, query_text: str, door_model: str = None
    ) -> Dict[str, Any]:
        """
        Run a query via the API endpoint.

        Args:
            query_text: The query text
            door_model: The door model to use (optional, falls back to default)

        Returns:
            API response as dictionary
        """
        # Use provided door model or fall back to default
        if not door_model:
            door_model = self.default_door_model

        payload = {"text": query_text, "door_model": door_model}

        logger.info(
            f"Sending query to API for door model {door_model}: {query_text}..."
        )

        try:
            async with httpx.AsyncClient() as client:
                headers = {}
                if self.auth_token:
                    headers["Authorization"] = (
                        f"Bearer {self.auth_token}"  # Adjust format based on your API
                    )

                response = await client.post(
                    self.query_endpoint,
                    json=payload,
                    headers=headers,
                    timeout=30.0,
                )

                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"Response keys: {list(response.headers.keys())}")
                    logger.info(
                        f"Query successful, received {len(result.get('paths', []))} paths"
                    )
                    return result
                else:
                    error_msg = f"API request failed with status {response.status_code}: {response.text}"
                    logger.error(error_msg)
                    return {"error": error_msg}

        except Exception as e:
            error_msg = f"Error sending API request: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    async def test_gpt_reformulated_queries(
        self, sample_size: int = 20
    ) -> Dict[str, Any]:
        """
        Test query functionality using GPT-reformulated observations.

        Args:
            sample_size: Number of nodes to sample and test

        Returns:
            Dictionary with test results
        """
        # Sample nodes with any observation data from Neo4j
        logger.info(f"Sampling {sample_size} observation nodes from Neo4j...")
        nodes = await self.sample_observation_nodes(sample_size)

        if not nodes:
            logger.error("No nodes with observation data sampled from database")
            return {
                "success": False,
                "error": "No nodes with observation data sampled",
            }

        logger.info(f"Sampled {len(nodes)} nodes with observation data")

        results = []

        for node in nodes:
            logger.info(f"Processing node {node['id']} - {node['name']}")
            # Generate a query using GPT based on any available observation data
            query = await self.generate_query_from_observation(node)

            if not query:
                logger.error(f"Failed to generate query for node {node['id']}")
                results.append(
                    {
                        "node_id": node["id"],
                        "node_name": node["name"],
                        "visual_observation": node.get("visual_observation", ""),
                        "auditory_observation": node.get("auditory_observation", ""),
                        "positional_observation": node.get(
                            "positional_observation", ""
                        ),
                        "error_codes": node.get("error_codes", ""),
                        "related_parts": node.get("related_parts", ""),
                        "query": None,
                        "door_model": None,
                        "success": False,
                        "error": "Failed to generate query",
                    }
                )
                continue

            # Get the appropriate door model for this observation
            door_model = await self.get_door_model_for_observation(node["id"])

            # Run the generated query with the appropriate door model
            response = await self.run_query(query, door_model)

            # Check for errors
            if "error" in response:
                logger.info("There is an error in the response")
                results.append(
                    {
                        "node_id": node["id"],
                        "node_name": node["name"],
                        "visual_observation": node.get("visual_observation", ""),
                        "auditory_observation": node.get("auditory_observation", ""),
                        "positional_observation": node.get(
                            "positional_observation", ""
                        ),
                        "error_codes": node.get("error_codes", ""),
                        "related_parts": node.get("related_parts", ""),
                        "query": query,
                        "door_model": door_model,
                        "success": False,
                        "error": response["error"],
                    }
                )
                continue

            logger.info("Checking paths")
            # Check if original node is in results
            found = False
            rank = None

            logger.info(f"Keys of response: {list(response.keys())}")
            logger.info(
                f"Number of paths in response: {len(response.get('paths', []))}"
            )

            for i, path in enumerate(response.get("paths", [])):
                logger.info(f"Path {i}: {path}")
                for path_node in path.get("nodes", []):
                    logger.info(
                        f"Comparing node {node['id']} with path node {path_node.get('id')}"
                    )
                    if path_node.get("id") == node["id"]:
                        found = True
                        rank = i + 1  # 1-based ranking
                        break
                if found:
                    break

            result = {
                "node_id": node["id"],
                "node_name": node["name"],
                "visual_observation": node.get("visual_observation", ""),
                "auditory_observation": node.get("auditory_observation", ""),
                "positional_observation": node.get("positional_observation", ""),
                "error_codes": node.get("error_codes", ""),
                "related_parts": node.get("related_parts", ""),
                "query": query,
                "door_model": door_model,
                "success": found,
                "rank": rank,
                "total_paths": len(response.get("paths", [])),
                "answer": response.get("answer", ""),  # Store the complete answer
            }

            results.append(result)

            found_text = "found" if found else "not found"
            rank_text = f" at rank {rank}" if rank else ""
            logger.info(
                f"Original node {node['id']} {found_text}{rank_text} in results for door model {door_model}"
            )

        # Calculate success rate
        success_count = sum(1 for r in results if r.get("success", False))
        success_rate = success_count / len(results) if results else 0

        # Create summary
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": len(results),
            "success_count": success_count,
            "success_rate": success_rate,
            "results": results,
        }

        logger.info(
            f"GPT query test complete: {success_count}/{len(results)} successful ({success_rate:.1%})"
        )

        # Save results to file
        with open("gpt_query_test_results.json", "w") as f:
            json.dump(summary, f, default=str, indent=2)

        return summary

    def generate_markdown_report(self, summary: Dict[str, Any]) -> str:
        """
        Generate a professional markdown report from test results.

        Args:
            summary: Test results summary

        Returns:
            Markdown formatted report
        """
        timestamp = datetime.fromisoformat(summary["timestamp"]).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        success_rate = summary["success_rate"] * 100

        # Initialize markdown report
        report = f"""# GPT-Reformulated Query Test Report

## Executive Summary

**Test Date:** {timestamp}

**Results Overview:**

| Metric | Value |
|--------|-------|
| Total Tests | {summary["total_tests"]} |
| Successful Tests | {summary["success_count"]} |
| Success Rate | {success_rate:.1f}% |

---

## Test Configuration

- **API Endpoint:** {self.api_url}/api/knowledge/query
- **GPT Model:** gpt-4.1-mini
- **Default Door Model:** {self.default_door_model}
- **Sample Size:** {summary["total_tests"]}

---

## Observation Type Analysis

"""

        # Analyze observation types
        observation_counts = {
            "visual": sum(1 for r in summary["results"] if r.get("visual_observation")),
            "auditory": sum(
                1 for r in summary["results"] if r.get("auditory_observation")
            ),
            "positional": sum(
                1 for r in summary["results"] if r.get("positional_observation")
            ),
            "error_codes": sum(1 for r in summary["results"] if r.get("error_codes")),
            "related_parts": sum(
                1 for r in summary["results"] if r.get("related_parts")
            ),
        }

        report += "### Distribution of Observation Types\n\n"
        report += "| Observation Type | Count | Percentage |\n"
        report += "|------------------|-------|------------|\n"

        for obs_type, count in observation_counts.items():
            percentage = (count / summary["total_tests"]) * 100
            report += f"| {obs_type.capitalize()} | {count} | {percentage:.1f}% |\n"

        report += "\n"

        # Analyze success rates by observation type
        report += "### Success Rates by Observation Type\n\n"
        report += (
            "| Observation Type | Success Rate | Successful Tests | Total Tests |\n"
        )
        report += (
            "|------------------|--------------|------------------|-------------|\n"
        )

        for obs_type in observation_counts.keys():
            attr_name = (
                f"{obs_type}_observation"
                if obs_type not in ["error_codes", "related_parts"]
                else obs_type
            )
            tests_with_type = [r for r in summary["results"] if r.get(attr_name)]

            if tests_with_type:
                successful_tests = [
                    t for t in tests_with_type if t.get("success", False)
                ]
                success_rate = (len(successful_tests) / len(tests_with_type)) * 100
                report += f"| {obs_type.capitalize()} | {success_rate:.1f}% | {len(successful_tests)} | {len(tests_with_type)} |\n"
            else:
                report += f"| {obs_type.capitalize()} | N/A | 0 | 0 |\n"

        report += "\n"

        # Analyze multiple observation types
        tests_by_obs_count = {}
        for r in summary["results"]:
            # Count how many observation types this node has
            obs_count = sum(
                [
                    1 if r.get("visual_observation") else 0,
                    1 if r.get("auditory_observation") else 0,
                    1 if r.get("positional_observation") else 0,
                    1 if r.get("error_codes") else 0,
                    1 if r.get("related_parts") else 0,
                ]
            )

            if obs_count not in tests_by_obs_count:
                tests_by_obs_count[obs_count] = {"total": 0, "success": 0}

            tests_by_obs_count[obs_count]["total"] += 1
            if r.get("success", False):
                tests_by_obs_count[obs_count]["success"] += 1

        report += "### Success Rates by Number of Observation Types\n\n"
        report += (
            "| Observation Types | Success Rate | Successful Tests | Total Tests |\n"
        )
        report += (
            "|-------------------|--------------|------------------|-------------|\n"
        )

        for count in sorted(tests_by_obs_count.keys()):
            stats = tests_by_obs_count[count]
            success_rate = (
                (stats["success"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            )
            report += f"| {count} | {success_rate:.1f}% | {stats['success']} | {stats['total']} |\n"

        report += "\n---\n\n"

        # Organize results by success/failure
        successful_tests = [r for r in summary["results"] if r.get("success", False)]
        failed_tests = [r for r in summary["results"] if not r.get("success", False)]

        # Add rank distribution for successful tests
        if successful_tests:
            rank_distribution = {}
            for test in successful_tests:
                rank = test.get("rank", 0)
                rank_distribution[rank] = rank_distribution.get(rank, 0) + 1

            report += "### Rank Distribution for Successful Queries\n\n"
            report += "| Rank | Count | Percentage |\n"
            report += "|------|-------|------------|\n"

            for rank in sorted(rank_distribution.keys()):
                count = rank_distribution[rank]
                percentage = (count / len(successful_tests)) * 100
                report += f"| {rank} | {count} | {percentage:.1f}% |\n"

            report += "\n"

        # Add success rate by door model
        door_models = {}
        for test in summary["results"]:
            door_model = test.get("door_model", self.default_door_model)
            if door_model not in door_models:
                door_models[door_model] = {"total": 0, "success": 0}

            door_models[door_model]["total"] += 1
            if test.get("success", False):
                door_models[door_model]["success"] += 1

        if door_models:
            report += "### Success Rate by Door Model\n\n"
            report += "| Door Model | Success Rate | Successful Tests | Total Tests |\n"
            report += "|------------|--------------|------------------|-------------|\n"

            for model, stats in door_models.items():
                success_rate = (
                    (stats["success"] / stats["total"]) * 100
                    if stats["total"] > 0
                    else 0
                )
                report += f"| {model} | {success_rate:.1f}% | {stats['success']} | {stats['total']} |\n"

            report += "\n---\n\n"

        # List of successful tests
        if successful_tests:
            report += "## Successful Tests\n\n"

            for i, test in enumerate(successful_tests):
                node_id = test.get("node_id", "")
                node_name = test.get("node_name", "")
                door_model = test.get("door_model", "")
                rank = test.get("rank", "N/A")

                report += f"### Test #{i+1}: Node ID {node_id} (Rank {rank})\n\n"
                report += f"**Door Model:** {door_model}\n\n"
                report += f"**Original Node Name:** {node_name}\n\n"

                # Add observation details with better handling of missing data
                report += "**Observation Details:**\n\n"

                if test.get("visual_observation"):
                    report += "🔍 **Visual Observation:**\n"
                    report += f"```\n{test.get('visual_observation', '')}\n```\n\n"

                if test.get("auditory_observation"):
                    report += "👂 **auditory Observation:**\n"
                    report += f"```\n{test.get('auditory_observation', '')}\n```\n\n"

                if test.get("positional_observation"):
                    report += "📍 **Positional Observation:**\n"
                    report += f"```\n{test.get('positional_observation', '')}\n```\n\n"

                if test.get("error_codes"):
                    report += (
                        "⚠️ **Error Code:** `" + test.get("error_codes", "") + "`\n\n"
                    )

                if test.get("related_parts"):
                    parts = test.get("related_parts")
                    if isinstance(parts, str):
                        try:
                            parts_list = json.loads(parts)
                            if isinstance(parts_list, list):
                                parts_text = ", ".join(parts_list)
                            else:
                                parts_text = parts
                        except:
                            parts_text = parts
                    else:
                        parts_text = str(parts)

                    report += "🔧 **Related Parts:** " + parts_text + "\n\n"

                report += "**Generated Query:**\n"
                report += f"```\n{test.get('query', '')}\n```\n\n"

                # Only show answer if available
                if test.get("answer"):
                    report += "**Generated Answer:**\n"
                    report += f"```\n{test.get('answer', '')}\n```\n\n"

                # Add separator between tests
                report += "---\n\n"

        # List of failed tests
        if failed_tests:
            report += "## Failed Tests\n\n"

            for i, test in enumerate(failed_tests):
                node_id = test.get("node_id", "")
                node_name = test.get("node_name", "")
                door_model = test.get("door_model", "")
                error = test.get("error", "Unknown error")

                report += f"### Failed Test #{i+1}: Node ID {node_id}\n\n"
                report += f"**Door Model:** {door_model}\n\n"
                report += f"**Original Node Name:** {node_name}\n\n"

                # Add observation details with better handling of missing data
                report += "**Observation Details:**\n\n"

                if test.get("visual_observation"):
                    report += "🔍 **Visual Observation:**\n"
                    report += f"```\n{test.get('visual_observation', '')}\n```\n\n"

                if test.get("auditory_observation"):
                    report += "👂 **auditory Observation:**\n"
                    report += f"```\n{test.get('auditory_observation', '')}\n```\n\n"

                if test.get("positional_observation"):
                    report += "📍 **Positional Observation:**\n"
                    report += f"```\n{test.get('positional_observation', '')}\n```\n\n"

                if test.get("error_codes"):
                    report += (
                        "⚠️ **Error Code:** `" + test.get("error_codes", "") + "`\n\n"
                    )

                if test.get("related_parts"):
                    parts = test.get("related_parts")
                    if isinstance(parts, str):
                        try:
                            parts_list = json.loads(parts)
                            if isinstance(parts_list, list):
                                parts_text = ", ".join(parts_list)
                            else:
                                parts_text = parts
                        except:
                            parts_text = parts
                    else:
                        parts_text = str(parts)

                    report += "🔧 **Related Parts:** " + parts_text + "\n\n"

                if test.get("query"):
                    report += "**Generated Query:**\n"
                    report += f"```\n{test.get('query', '')}\n```\n\n"

                report += f"**Error:** {error}\n\n"

                # Add separator between tests
                report += "---\n\n"

        # Add conclusion
        report += "## Conclusion\n\n"

        if success_rate >= 80:
            conclusion = "The GPT query reformulation shows **excellent performance** in retrieving the correct observation nodes."
        elif success_rate >= 60:
            conclusion = "The GPT query reformulation shows **good performance** but has room for improvement."
        elif success_rate >= 40:
            conclusion = "The GPT query reformulation shows **moderate performance**. Further refinement is needed."
        else:
            conclusion = "The GPT query reformulation shows **poor performance**. Significant improvements are required."

        report += f"{conclusion}\n\n"

        if successful_tests:
            avg_rank = sum(t.get("rank", 0) for t in successful_tests) / len(
                successful_tests
            )
            report += f"For successful queries, the average rank of the correct observation was **{avg_rank:.2f}**.\n\n"

        # Observation type insights
        most_successful_type = max(
            [
                (obs_type, count)
                for obs_type, count in observation_counts.items()
                if count > 0
            ],
            key=lambda x: sum(
                1
                for r in summary["results"]
                if r.get(
                    f"{x[0]}_observation"
                    if x[0] not in ["error_codes", "related_parts"]
                    else x[0]
                )
                and r.get("success", False)
            )
            / (x[1] if x[1] > 0 else 1),
            default=("none", 0),
        )

        if most_successful_type[0] != "none":
            attr_name = (
                f"{most_successful_type[0]}_observation"
                if most_successful_type[0] not in ["error_codes", "related_parts"]
                else most_successful_type[0]
            )
            tests_with_type = [r for r in summary["results"] if r.get(attr_name)]

            if tests_with_type:
                successful_tests_with_type = [
                    t for t in tests_with_type if t.get("success", False)
                ]
                success_rate_for_type = (
                    len(successful_tests_with_type) / len(tests_with_type)
                ) * 100

                report += f"The '{most_successful_type[0]}' observation type showed the highest success rate at **{success_rate_for_type:.1f}%**.\n\n"

        # Add recommendations based on results
        report += "### Recommendations\n\n"

        if success_rate < 70:
            report += "1. **Improve GPT Prompt Engineering**: Refine the prompt to better capture the essence of various observation types.\n"
            report += "2. **Enhance Vector Search**: Review vector search implementation to improve retrieval accuracy.\n"

        if successful_tests and avg_rank > 2:
            report += f"3. **Ranking Algorithm Refinement**: The average rank of {avg_rank:.2f} suggests that the ranking algorithm could be improved.\n"

        if len(door_models) > 1:
            worst_model = min(
                door_models.items(),
                key=lambda x: (
                    x[1]["success"] / x[1]["total"] if x[1]["total"] > 0 else 0
                ),
            )
            if (
                worst_model[1]["total"] > 0
                and worst_model[1]["success"] / worst_model[1]["total"] < 0.5
            ):
                report += f"4. **Review Door Model Data**: Door model '{worst_model[0]}' showed particularly poor performance.\n"

        # Analyze observation types that may need improvement
        low_performing_types = []
        for obs_type in observation_counts.keys():
            attr_name = (
                f"{obs_type}_observation"
                if obs_type not in ["error_codes", "related_parts"]
                else obs_type
            )
            tests_with_type = [r for r in summary["results"] if r.get(attr_name)]

            if tests_with_type:
                successful_tests = [
                    t for t in tests_with_type if t.get("success", False)
                ]
                success_rate = (len(successful_tests) / len(tests_with_type)) * 100

                if success_rate < 40 and len(tests_with_type) >= 3:
                    low_performing_types.append((obs_type, success_rate))

        if low_performing_types:
            report += "5. **Improve Specific Observation Types**: The following observation types showed poor performance and may need special attention:\n"
            for obs_type, rate in low_performing_types:
                report += (
                    f"   - **{obs_type.capitalize()}**: {rate:.1f}% success rate\n"
                )

        report += (
            "\n### Test Generated: "
            + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            + "\n"
        )

        return report

    async def generate_gpt_queries(self, sample_size: int = 1000) -> Dict[str, Any]:
        nodes = await self.sample_observation_nodes(sample_size)

        if not nodes:
            logger.error("No nodes with observation data sampled from database")
            return {
                "success": False,
                "error": "No nodes with observation data sampled",
            }

        output_file = Path("gpt_generated_queries.csv")

        fields = [
            "visual_observation",
            "auditory_observation",
            "positional_observation",
            "error_codes",
            "related_parts",
        ]
        headers = (
            ["node_id"]
            + [f"orig_{f}" for f in fields]
            + [f"query_{f}" for f in fields]
            + ["query"]
        )

        # Ensure file exists and has headers
        if not output_file.exists():
            with open(output_file, mode="w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=headers)
                writer.writeheader()

        batch_size = 50

        async def process_batch(batch):
            # Submit all GPT calls in parallel
            tasks = [self.generate_query_from_observation(node) for node in batch]
            results = await asyncio.gather(*tasks)
            # Write results for the batch at once (minimizes file locks)
            rows = []
            for node, result in zip(batch, results):
                if result:
                    query, in_original, in_query = result
                    row = {"node_id": node.get("id"), "query": query}
                    for f in fields:
                        row[f"orig_{f}"] = in_original.get(f, 0)
                        row[f"query_{f}"] = in_query.get(f, 0)
                    rows.append(row)
            if rows:
                with open(output_file, mode="a", newline="", encoding="utf-8") as f:
                    writer = csv.DictWriter(f, fieldnames=headers)
                    writer.writerows(rows)

        # Actually run all batches (in parallel for each batch)
        batches = [nodes[i : i + batch_size] for i in range(0, len(nodes), batch_size)]
        for batch in batches:
            await process_batch(batch)

        logger.info(f"Generated queries saved to {output_file}")
        return {"success": True, "file": str(output_file)}

    import time  # To allow for flushing, optional

    async def test_gpt_reformulated_queries_from_csv(
        self, csv_path: str
    ) -> Dict[str, Any]:
        """
        Test GPT-reformulated queries loaded from a CSV file.
        Additionally, create a new CSV that includes the top 5 results for each query.
        """

        if not os.path.exists(csv_path):
            logger.error(f"CSV file not found: {csv_path}")
            return {"success": False, "error": "CSV file not found"}

        df = pd.read_csv(csv_path)
        results = []

        # Prepare output CSV with all old and new fields
        orig_cols = list(df.columns)
        extra_cols = [f"top{i}_obs" for i in range(1, 6)] + ["rank_in_top5"]
        out_cols = orig_cols + extra_cols

        out_csv_path = csv_path.replace(".csv", "_with_top5_results.csv")
        with open(out_csv_path, "w", newline="", encoding="utf-8") as fout:
            writer = csv.DictWriter(fout, fieldnames=out_cols)
            writer.writeheader()

            for idx, row in df.iterrows():
                node_id = row["node_id"]
                query_text = row["query"]

                # Get door model
                door_model = await self.get_door_model_for_observation(node_id)
                # Run query
                response = await self.run_query(query_text, door_model)
                # logging.info(f"Response: {response}")

                # Prepare the record for output
                out_row = dict(row)  # Start with the old CSV data

                # Default for new fields
                for i in range(1, 6):
                    out_row[f"top{i}_obs"] = ""
                out_row["rank_in_top5"] = ""

                # Process response if successful
                found = False
                rank = None
                top5_obs_nodes = []

                if "error" not in response and response.get("paths"):
                    # Get the top 5 observation nodes (if available)
                    top5_paths = response["paths"][:5]
                    logger.info(
                        f"Processing {len(top5_paths)} top paths for node {node_id}"
                    )

                    for i, path in enumerate(top5_paths):
                        obs_node = None
                        for node in path.get("nodes", []):
                            if node.get("type") == "OBSERVATION":
                                # Flatten 'properties' dict into the node dict
                                flattened = dict(node)  # copy root keys
                                props = flattened.pop("properties", {})
                                flattened.update(props)
                                obs_node = flattened
                                break
                        if obs_node:
                            out_row[f"top{i+1}_obs"] = json.dumps(
                                obs_node, ensure_ascii=False
                            )
                            # Check rank
                            logger.info(
                                f"Checking node {obs_node.get('id')} against {node_id}"
                            )
                            if not found and str(obs_node.get("id")) == str(node_id):
                                found = True
                                rank = i + 1
                    out_row["rank_in_top5"] = rank if found else ""

                # Save to output CSV
                writer.writerow(out_row)
                fout.flush()  # Immediately flush to disk
                # Optionally: time.sleep(0.01)  # If disk IO issues, but rarely needed

                # Collect result for in-memory summary
                results.append(
                    {
                        "node_id": node_id,
                        "query": query_text,
                        "door_model": door_model,
                        "success": bool(found),
                        "rank": rank,
                        "total_paths": (
                            len(response.get("paths", [])) if "paths" in response else 0
                        ),
                        "answer": (
                            response.get("answer", "") if "answer" in response else ""
                        ),
                    }
                )

        # Final summary (unchanged)
        success_count = sum(1 for r in results if r["success"])
        total_tests = len(results)
        success_rate = success_count / total_tests if total_tests else 0

        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "success_count": success_count,
            "success_rate": success_rate,
            "results": results,
            "out_csv_file": out_csv_path,
        }

        logger.info(
            f"Completed testing {total_tests} queries from CSV — success rate: {success_rate:.1%}"
        )

        # Save results summary as before
        with open("gpt_query_test_results_from_csv.json", "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2, default=str)

        logger.info(f"New CSV file with top 5 results per query: {out_csv_path}")
        return summary


async def main():
    """Run the combined Neo4j + API tests with optional GPT query reuse."""
    logger.info("Starting combined Neo4j + API tests with GPT-reformulated queries")

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Run GPT reformulation tests.")
    parser.add_argument(
        "--gen-new-queries",
        action="store_true",
        help="Use previously generated CSV file instead of generating new GPT queries.",
    )
    args = parser.parse_args()

    # Get Neo4j credentials from environment variables or use defaults
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_username = os.getenv("NEO4J_USERNAME", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    username = "<EMAIL>"
    password = "admin123"

    tester = None

    try:
        # Initialize tester
        tester = CombinedQueryTester(
            api_url="http://localhost:8000",
            neo4j_uri=neo4j_uri,
            neo4j_username=neo4j_username,
            neo4j_password=neo4j_password,
        )

        if not await tester.login(username, password):
            logger.error("Failed to authenticate with API")
            return

        if not args.gen_new_queries:
            logger.info("Using previously generated GPT queries from CSV...")
            gpt_results = await tester.test_gpt_reformulated_queries_from_csv(
                "gpt_generated_queries.csv"
            )
        else:
            logger.info("Generating new GPT queries and running tests...")
            await tester.generate_gpt_queries(sample_size=1000)
            gpt_results = await tester.test_gpt_reformulated_queries_from_csv(
                "gpt_generated_queries.csv"
            )

        # Generate markdown report
        logger.info("Generating markdown report...")
        report = tester.generate_markdown_report(gpt_results)

        # Save report to file
        report_filename = (
            f"gpt_query_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        )
        with open(report_filename, "w", encoding="utf-8") as f:
            f.write(report)

        logger.info(f"Report saved to {report_filename}")

        # Log summary
        logger.info("\nGPT-reformulated query test results summary:")
        logger.info(f"- Tests run: {gpt_results['total_tests']}")
        logger.info(f"- Tests passed: {gpt_results['success_count']}")
        logger.info(f"- Success rate: {gpt_results['success_rate'] * 100:.1f}%")
        logger.info(f"- Detailed report saved to: {report_filename}")

    except Exception as e:
        logger.error(f"Test run failed: {e}")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
    finally:
        # Close Neo4j connection
        if tester:
            await tester.close_neo4j()

    logger.info("GPT-reformulated query tests complete")


if __name__ == "__main__":
    asyncio.run(main())
