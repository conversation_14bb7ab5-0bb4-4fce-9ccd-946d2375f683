# tests/integration/query_quality/simple_query_test.py

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

from app.services.nlp.query.query_service import QueryProcessingService
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)
from app.services.embeddings.embedding import EmbeddingService
from app.services.database.neo4j import Neo4jService
from app.models.domain.knowledge import KnowledgeNode, KnowledgeType

# Set up logging
logging.basicConfig(
    level=logger.info,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("simple_query_test.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


class SimpleQueryTester:
    """
    Simplified tester to validate database connection and basic query functionality.
    """

    def __init__(self, knowledge_repo, query_service):
        self.knowledge_repo = knowledge_repo
        self.query_service = query_service
        self.results = []

    async def sample_observation_nodes(self, limit: int = 5) -> List[KnowledgeNode]:
        """Sample a few observation nodes from the database."""
        logger.info(f"Sampling {limit} observation nodes from database")

        # Simple query to get observation nodes with various attribute combinations
        query = """
        MATCH (o:OBSERVATION)
        WITH o,
            (CASE WHEN o.visual_observation IS NOT NULL THEN 1 ELSE 0 END) +
            (CASE WHEN o.auditory_observation IS NOT NULL THEN 1 ELSE 0 END) +
            (CASE WHEN o.positional_observation IS NOT NULL THEN 1 ELSE 0 END) +
            (CASE WHEN o.error_codes IS NOT NULL THEN 1 ELSE 0 END) +
            (CASE WHEN o.related_parts IS NOT NULL THEN 1 ELSE 0 END) as attribute_count

        // Prioritize nodes with more attributes
        ORDER BY attribute_count DESC, rand() LIMIT $limit

        RETURN o
        """

        results = await self.knowledge_repo.execute_read_query(query, {"limit": limit})

        # Convert to domain objects
        nodes = []
        for result in results:
            node = self.knowledge_repo._dict_to_knowledge_node(result["o"])
            nodes.append(node)

            # Log the node attributes
            attrs = []
            if hasattr(node, "visual_observation") and node.visual_observation:
                attrs.append(f"visual: {node.visual_observation[:30]}...")
            if hasattr(node, "auditory_observation") and node.auditory_observation:
                attrs.append(f"audio: {node.auditory_observation[:30]}...")
            if hasattr(node, "positional_observation") and node.positional_observation:
                attrs.append(f"position: {node.positional_observation[:30]}...")
            if hasattr(node, "error_codes") and node.error_codes:
                attrs.append(f"error: {node.error_codes}")
            if hasattr(node, "related_parts") and node.related_parts:
                attrs.append(f"parts: {node.related_parts}")

            logger.info(
                f"Sampled node: {node.id} - {node.name[:50]}... - Attributes: {', '.join(attrs)}"
            )

        return nodes

    def create_simple_query(self, node: KnowledgeNode) -> str:
        """Create a simple query based on node attributes."""
        query_parts = [node.name]

        # Add specific attributes if available
        if hasattr(node, "error_codes") and node.error_codes:
            query_parts.append(f"Error code: {node.error_codes}")

        if hasattr(node, "related_parts") and node.related_parts:
            parts = node.related_parts
            if isinstance(parts, str):
                try:
                    parts = json.loads(parts)
                except:
                    parts = [parts]

            if isinstance(parts, list):
                parts_text = ", ".join(parts)
                query_parts.append(f"Parts: {parts_text}")

        if hasattr(node, "positional_observation") and node.positional_observation:
            query_parts.append(f"Position: {node.positional_observation}")

        if hasattr(node, "visual_observation") and node.visual_observation:
            query_parts.append(f"Visual: {node.visual_observation}")

        if hasattr(node, "auditory_observation") and node.auditory_observation:
            query_parts.append(f"Sound: {node.auditory_observation}")

        return " ".join(query_parts)

    async def test_node(self, node: KnowledgeNode) -> Dict[str, Any]:
        """Test if we can retrieve a node using a query based on its attributes."""
        # Create query
        query = self.create_simple_query(node)
        logger.info(f"Testing node {node.id} with query: {query}")

        # Run query
        try:
            paths, answer = await self.query_service.process_query(query)

            # Check if original node is in results
            found = False
            rank = None

            for i, path in enumerate(paths):
                for path_node in path.nodes:
                    if path_node.id == node.id:
                        found = True
                        rank = i + 1
                        break
                if found:
                    break

            result = {
                "node_id": node.id,
                "node_name": node.name,
                "query": query,
                "found": found,
                "rank": rank,
                "total_paths": len(paths),
            }

            found_text = "found" if found else "not found"
            rank_text = f" at rank {rank}" if rank else ""
            logger.info(f"Node {node.id} {found_text}{rank_text} in results")

            return result

        except Exception as e:
            logger.error(f"Error testing node {node.id}: {str(e)}")
            return {
                "node_id": node.id,
                "node_name": node.name,
                "query": query,
                "found": False,
                "error": str(e),
            }

    async def run_simple_test(self, sample_size: int = 5) -> Dict[str, Any]:
        """Run a simple test on a few sampled nodes."""
        # Sample nodes
        nodes = await self.sample_observation_nodes(sample_size)

        if not nodes:
            logger.error("No nodes sampled from database")
            return {"success": False, "error": "No nodes sampled"}

        # Test each node
        results = []
        for node in nodes:
            result = await self.test_node(node)
            results.append(result)

        # Calculate success rate
        found_count = sum(1 for r in results if r.get("found", False))
        success_rate = found_count / len(results) if results else 0

        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_nodes": len(results),
            "found_count": found_count,
            "success_rate": success_rate,
            "results": results,
        }

        logger.info(
            f"Test complete: {found_count}/{len(results)} nodes retrieved successfully ({success_rate:.1%})"
        )

        # Save results to file
        with open("simple_query_test_results.json", "w") as f:
            json.dump(summary, f, default=str, indent=2)

        return summary


async def main():
    """Run the simple query test."""
    logger.info("Starting simple query test")

    try:
        # Initialize services
        neo4j_service = Neo4jService()
        await neo4j_service.connect()

        embedding_service = EmbeddingService()

        knowledge_repo = KnowledgeRepository(
            db=neo4j_service,
            embedding_service=embedding_service,
            embedding_cache=None,  # No cache for simple test
        )

        # Simplified version - only minimal dependencies
        from app.services.nlp.query.query_service import QueryProcessingService
        from app.services.nlp.query.search_service import SearchService
        from app.services.nlp.query.path_processor import PathProcessor
        from app.services.nlp.query.door_service import DoorService
        from app.services.nlp.query.llm_client import LLMClient
        from app.core.config import get_settings

        # Create more services needed for QueryProcessingService
        door_service = DoorService(door_repo=None, door_cache=None)
        path_processor = PathProcessor(
            knowledge_repo=knowledge_repo,
            mechanic_repo=None,
            embedding_service=embedding_service,
        )
        llm_client = LLMClient(settings=get_settings(), door_service=door_service)
        search_service = SearchService(
            knowledge_repo=knowledge_repo,
            embedding_service=embedding_service,
            path_processor=path_processor,
        )

        # Create query service with minimal dependencies
        query_service = QueryProcessingService(
            knowledge_repo=knowledge_repo,
            mechanic_repo=None,
            door_repo=None,
            embedding_service=embedding_service,
            door_cache=None,
        )
        query_service.door_service = door_service
        query_service.path_processor = path_processor
        query_service.llm_client = llm_client
        query_service.search_service = search_service

        # Run test
        tester = SimpleQueryTester(knowledge_repo, query_service)
        results = await tester.run_simple_test(sample_size=5)

        print("\nTest results summary:")
        print(f"Nodes tested: {results['total_nodes']}")
        print(f"Nodes found: {results['found_count']}")
        print(f"Success rate: {results['success_rate']:.1%}")

        for i, result in enumerate(results["results"]):
            found = "✓" if result.get("found", False) else "✗"
            rank = f" (rank {result.get('rank')})" if result.get("found", False) else ""
            print(f"{i+1}. [{found}] {result['node_name'][:50]}...{rank}")

    except Exception as e:
        logger.error(f"Error in main test: {str(e)}")
    finally:
        # Clean up
        if "neo4j_service" in locals():
            await neo4j_service.close()

        logger.info("Test complete")


if __name__ == "__main__":
    asyncio.run(main())
