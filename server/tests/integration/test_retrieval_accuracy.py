#!/usr/bin/env python3
"""
Revolving Door Query System Accuracy Test

This script tests the accuracy of the revolving door query system by:
1. Extracting symptoms from CSV data
2. Generating realistic user questions based on those symptoms using OpenAI
3. Querying the system's API with authentication
4. Evaluating the quality of responses using GPT
5. Generating a comprehensive report of the results
"""

import os
import json
import csv
import random
import time
import asyncio
from datetime import datetime
from typing import List, Dict, Any
import logging
import argparse
import pandas as pd
import aiohttp
from openai import OpenAI, AsyncOpenAI
from dotenv import load_dotenv

load_dotenv()

# Setup logging
logging.basicConfig(
    level=logger.info,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(
            f"query_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        ),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)

# Default configuration
DEFAULT_CONFIG = {
    "api_url": os.getenv("API_URL", "http://localhost:8000"),
    "openai_api_key": os.getenv("OPENAI_API_KEY"),
    "csv_paths": [
        "scripts/sample_data/full_outages_data.csv",
        "scripts/sample_data/full_outages_data2.csv",
    ],
    "email": "<EMAIL>",
    "password": "password123",
    "num_test_cases": 25,
    "test_seed": 42,
    "concurrent_requests": 3,
    "evaluation_model": "gpt-4.1-mini",
    "generation_model": "gpt-4.1-mini",
}


class RevolvingDoorTester:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_url = config["api_url"]
        self.token = None
        self.openai_client = OpenAI(api_key=config["openai_api_key"])
        self.async_openai_client = AsyncOpenAI(api_key=config["openai_api_key"])
        self.session = None

        # Set random seed for reproducibility
        random.seed(config["test_seed"])

    async def setup(self):
        """Initialize HTTP session and authenticate."""
        self.session = aiohttp.ClientSession()
        await self.authenticate(self.config["email"], self.config["password"])

    async def cleanup(self):
        """Clean up resources."""
        if self.session:
            await self.session.close()

    async def authenticate(self, email: str, password: str):
        """Authenticate with the API and get a token."""
        if not self.session:
            self.session = aiohttp.ClientSession()

        data = {"username": email, "password": password}

        async with self.session.post(
            f"{self.api_url}/api/mechanics/token",
            data=data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        ) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(
                    f"Authentication failed: {response.status} - {error_text}"
                )

            response_data = await response.json()
            self.token = response_data.get("access_token")
            logger.info("Successfully authenticated with the API")
            return self.token

    async def extract_symptoms_from_csv(
        self, csv_paths: List[str]
    ) -> List[Dict[str, Any]]:
        """Extract symptoms and related information from CSV files."""
        all_data = []
        total_entries = 0
        incomplete_entries = 0
        too_short_entries = 0

        for csv_path in csv_paths:
            try:
                df = pd.read_csv(csv_path, encoding="utf-8-sig")
                logger.info(f"Processing CSV file: {csv_path} with {len(df)} rows")

                # Define relevant columns based on the init_database_from_csv.py script
                relevant_columns = [
                    "Description",  # Symptom
                    "Probleem beschrijving",  # Problem
                    "Grondoorzaak(5x waarom)",  # Cause
                    "Correctieve maatregel(en)",  # Solution
                    "Description of technical object",  # Door type
                    "Monteur",  # Mechanic
                ]

                # Fallback for alternative column names
                alternative_columns = {
                    "Grondoorzaak(5x waarom)": "Grondoorzaak\n(5x waarom)",
                    "Correctieve maatregel(en)": "(Correctieve) maatregel(en)",
                    "(Correctieve) maatregel(en)": "Beheersmaatregel",
                }

                # Create a modified column list by substituting alternative columns
                actual_columns = []
                column_map = {}  # Maps actual column name to standardized name

                # First check which columns actually exist in the dataframe
                for col in relevant_columns:
                    if col in df.columns:
                        actual_columns.append(col)
                        column_map[col] = col
                    else:
                        # Check if an alternative exists
                        for (
                            standard_col,
                            alt_col,
                        ) in alternative_columns.items():
                            if standard_col == col and alt_col in df.columns:
                                actual_columns.append(alt_col)
                                column_map[alt_col] = standard_col
                                logger.info(
                                    f"Using alternative column '{alt_col}' for '{standard_col}'"
                                )
                                break

                # Filter to only include columns that exist
                available_columns = [col for col in actual_columns if col in df.columns]
                if available_columns:
                    df_filtered = df[available_columns]
                    logger.info(
                        f"Selected {len(available_columns)} columns: {available_columns}"
                    )
                else:
                    df_filtered = df
                    logger.warning(
                        f"No matching columns found in {csv_path}, using all columns"
                    )

                # Process each row with proper column handling
                for _, row in df_filtered.iterrows():
                    total_entries += 1

                    # Extract data using the column map for proper standardization
                    entry = {
                        "symptom": "",
                        "problem": "",
                        "cause": "",
                        "solution": "",
                        "door_type": "",
                        "mechanic": "",
                    }

                    # Map to standardized field names
                    for col in row.index:
                        if col in column_map:
                            standard_col = column_map[col]
                            if standard_col == "Description":
                                entry["symptom"] = str(row[col]).strip()
                            elif standard_col == "Probleem beschrijving":
                                entry["problem"] = str(row[col]).strip()
                            elif standard_col == "Grondoorzaak(5x waarom)":
                                entry["cause"] = str(row[col]).strip()
                            elif standard_col == "Correctieve maatregel(en)":
                                entry["solution"] = str(row[col]).strip()
                            elif standard_col == "Description of technical object":
                                entry["door_type"] = str(row[col]).strip()
                            elif standard_col == "Monteur":
                                entry["mechanic"] = str(row[col]).strip()

                    # Check for completeness - must have all required fields
                    if not all(
                        entry[k] for k in ["symptom", "problem", "cause", "solution"]
                    ):
                        incomplete_entries += 1
                        continue

                    # Check for substantive content - each field should have meaningful content
                    min_length = 5  # Minimum characters for meaningful content
                    if (
                        len(entry["symptom"]) < min_length
                        or len(entry["problem"]) < min_length
                        or len(entry["cause"]) < min_length
                        or len(entry["solution"]) < min_length
                    ):
                        too_short_entries += 1
                        continue

                    # Check if door_type is present
                    if not entry["door_type"]:
                        logger.debug(
                            f"Entry has no door type: {entry['symptom'][:30]}..."
                        )

                    # Add valid entry
                    all_data.append(entry)

                logger.info(f"Extracted {len(all_data)} valid entries from {csv_path}")

            except Exception as e:
                logger.error(f"Error processing CSV {csv_path}: {str(e)}")
                import traceback

                logger.error(traceback.format_exc())

        # Remove duplicates based on symptom text
        unique_data = []
        seen_symptoms = set()
        duplicates = 0

        for entry in all_data:
            symptom = entry["symptom"].strip().lower()  # Normalize for comparison
            if symptom not in seen_symptoms:
                seen_symptoms.add(symptom)
                unique_data.append(entry)
            else:
                duplicates += 1

        # Log statistics
        logger.info(f"CSV processing summary:")
        logger.info(f"Total entries processed: {total_entries}")
        logger.info(f"Incomplete entries (missing fields): {incomplete_entries}")
        logger.info(f"Too short entries (insufficient content): {too_short_entries}")
        logger.info(f"Duplicate symptom entries: {duplicates}")
        logger.info(f"Final dataset contains {len(unique_data)} unique valid entries")

        return unique_data

    async def generate_sample_question(self, entry: Dict[str, Any]) -> str:
        """Generate a sample question based on symptom data using OpenAI."""
        prompt = f"""
        You are a junior mechanic at Boon Edam, a company that specializes in revolving doors.

        You need to ask a question about a revolving door issue with the following characteristics:

        Symptom: {entry['symptom']}

        Formulate a realistic question that you would ask a senior technician about this issue.
        Your question should sound natural and conversational, as if you're encountering this problem
        and need guidance on how to fix it. Don't mention that you're a junior mechanic explicitly.

        IMPORTANT: Do NOT include the door model or type in your question.

        Generate the question in Dutch since that's the language used by the mechanics.

        Return ONLY the question text, with no additional commentary.
        """

        response = await self.async_openai_client.chat.completions.create(
            model=self.config["generation_model"],
            messages=[
                {
                    "role": "system",
                    "content": (
                        "You are a helpful assistant that generates realistic questions that a junior mechanic might ask about revolving door issues."
                    ),
                },
                {"role": "user", "content": prompt},
            ],
            temperature=0.7,
            max_tokens=150,
        )

        return response.choices[0].message.content.strip()

    async def generate_sample_questions(
        self, symptom_data: List[Dict[str, Any]], num_questions: int
    ) -> List[Dict[str, Any]]:
        """Generate sample questions based on symptom data using OpenAI."""
        questions = []

        # Randomly select entries to prevent bias toward more frequent issues
        selected_entries = random.sample(
            symptom_data, min(num_questions, len(symptom_data))
        )

        # Generate questions concurrently with rate limiting
        semaphore = asyncio.Semaphore(self.config["concurrent_requests"])

        async def generate_with_rate_limit(i, entry):
            async with semaphore:
                try:
                    logger.info(f"Generating question {i+1}/{len(selected_entries)}...")
                    question_text = await self.generate_sample_question(entry)

                    questions.append(
                        {"question": question_text, "original_data": entry}
                    )

                    logger.info(
                        f"Generated question {i+1}/{len(selected_entries)}: {question_text[:50]}..."
                    )

                    # Add delay to avoid rate limits
                    await asyncio.sleep(0.5)
                    return True

                except Exception as e:
                    logger.error(f"Error generating question for entry {i+1}: {str(e)}")
                    return False

        tasks = [
            generate_with_rate_limit(i, entry)
            for i, entry in enumerate(selected_entries)
        ]
        await asyncio.gather(*tasks)

        # Sort questions by original data order
        questions.sort(key=lambda q: selected_entries.index(q["original_data"]))

        return questions

    async def query_api(self, question: str, door_model: str = None) -> Dict[str, Any]:
        """Send a query to the API and get the response."""
        if not self.token:
            raise Exception("Not authenticated. Call authenticate() first.")

        if not self.session:
            self.session = aiohttp.ClientSession()

        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
        }

        data = {"text": question}

        # Add door_model parameter if provided
        if door_model:
            data["door_model"] = door_model

        try:
            async with self.session.post(
                f"{self.api_url}/api/knowledge/query",
                headers=headers,
                json=data,
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"API query failed: {response.status} - {error_text}")
                    return {
                        "success": False,
                        "error": f"{response.status} - {error_text}",
                    }

                response_data = await response.json()
                return {"success": True, "data": response_data}
        except Exception as e:
            logger.error(f"API query error: {str(e)}")
            return {"success": False, "error": str(e)}

    async def evaluate_response(
        self,
        question: str,
        api_response: Dict[str, Any],
        original_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Evaluate the quality of the API response using OpenAI."""
        if not api_response.get("success", False):
            return {
                "score": 0,
                "accuracy": 0,
                "completeness": 0,
                "clarity": 0,
                "relevance": 0,
                "evaluation": (
                    f"Failed to get API response: {api_response.get('error', 'Unknown error')}"
                ),
            }

        response_data = api_response.get("data", {})
        answer = response_data.get("answer", "")
        paths = response_data.get("paths", [])

        # Check if any knowledge was returned
        knowledge_returned = (
            response_data.get("knowledge_need", False) and len(paths) > 0
        )

        # Extract nodes from the first path (if available)
        path_data = {"symptom": "", "problem": "", "cause": "", "solution": ""}

        if paths:
            for node in paths[0].get("nodes", []):
                node_type = node.get("type", "").lower()
                if node_type in path_data and node.get("name"):
                    path_data[node_type] = node.get("name", "")

        # Prepare prompt for evaluation
        prompt = f"""
        Evaluate the quality of this answer to a revolving door technical support question.

        QUESTION: {question}

        ANSWER FROM SYSTEM: {answer}

        PATH DATA RETRIEVED BY SYSTEM:
        - Retrieved Symptom: {path_data['symptom']}
        - Retrieved Problem: {path_data['problem']}
        - Retrieved Cause: {path_data['cause']}
        - Retrieved Solution: {path_data['solution']}

        The actual facts about this issue are:
        - Symptom: {original_data['symptom']}
        - Problem: {original_data['problem']}
        - Cause: {original_data['cause']}
        - Solution: {original_data['solution']}

        Score the answer on a scale of 1-10 for each of these criteria:
        1. Accuracy: Does the answer align with the actual facts? Are the cause and solution correct?
        2. Completeness: Does it address all aspects of the problem (symptom identification, problem diagnosis, cause explanation, and solution recommendation)?
        3. Clarity: Is the answer clear, well-structured, and easy to understand?
        4. Relevance: How relevant is the retrieved knowledge to the question asked?
        5. Overall: An overall score considering all factors.

        Format your response as a JSON object with the following fields:
        {{
            "score": [1-10 numerical overall score],
            "accuracy": [1-10 score for accuracy],
            "completeness": [1-10 score for completeness],
            "clarity": [1-10 score for clarity],
            "relevance": [1-10 score for relevance],
            "matching_symptom": [true/false - does the retrieved symptom match the actual symptom?],
            "matching_problem": [true/false - does the retrieved problem match the actual problem?],
            "matching_cause": [true/false - does the retrieved cause match the actual cause?],
            "matching_solution": [true/false - does the retrieved solution match the actual solution?],
            "evaluation": [Brief explanation of scores, max 150 words]
        }}
        """

        try:
            response = await self.async_openai_client.chat.completions.create(
                model=self.config["evaluation_model"],
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "You are an expert evaluator of technical support answers for revolving door systems."
                        ),
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.3,
                max_tokens=800,
                response_format={"type": "json_object"},
            )

            evaluation = json.loads(response.choices[0].message.content)

            # Add whether knowledge was found
            evaluation["knowledge_found"] = knowledge_returned
            evaluation["num_paths"] = len(paths)

            return evaluation

        except Exception as e:
            logger.error(f"Error evaluating response: {str(e)}")
            return {
                "score": 0,
                "accuracy": 0,
                "completeness": 0,
                "clarity": 0,
                "relevance": 0,
                "knowledge_found": knowledge_returned,
                "num_paths": len(paths),
                "evaluation": f"Error evaluating response: {str(e)}",
            }

    async def process_question(
        self, i: int, question_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process a single question through API query and evaluation."""
        try:
            question = question_data["question"]
            original_data = question_data["original_data"]
            door_model = original_data.get(
                "door_type", ""
            )  # Get door model from original data

            logger.info(f"Testing question {i+1}: {question[:50]}...")

            # Send query to API with door_model parameter
            api_response = await self.query_api(question, door_model)

            # Evaluate response
            evaluation = await self.evaluate_response(
                question, api_response, original_data
            )

            # Collect results
            result = {
                "question": question,
                "original_data": original_data,
                "api_response": (
                    api_response.get("data", {})
                    if api_response.get("success", False)
                    else None
                ),
                "evaluation": evaluation,
                "test_id": i + 1,
                "door_model": (
                    door_model  # Include door_model in results for analysis
                ),
            }

            # Log result
            logger.info(f"Question {i+1} score: {evaluation.get('score', 0)}/10")

            return result

        except Exception as e:
            logger.error(f"Error processing question {i+1}: {str(e)}")
            return {
                "question": question_data.get("question", ""),
                "original_data": question_data.get("original_data", {}),
                "api_response": None,
                "evaluation": {
                    "score": 0,
                    "evaluation": f"Processing error: {str(e)}",
                },
                "test_id": i + 1,
                "error": str(e),
            }

    async def run_test(self) -> List[Dict[str, Any]]:
        """Run the complete test process and return results."""
        results = []

        # Step 1: Extract symptoms from CSV
        symptom_data = await self.extract_symptoms_from_csv(self.config["csv_paths"])
        logger.info(f"Extracted {len(symptom_data)} complete entries from CSV files")

        # Step 2: Generate sample questions
        questions = await self.generate_sample_questions(
            symptom_data, self.config["num_test_cases"]
        )
        logger.info(f"Generated {len(questions)} sample questions")

        # Step 3: Process questions (already authenticated in setup)
        # Use a semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(self.config["concurrent_requests"])

        async def process_with_rate_limit(i, question_data):
            async with semaphore:
                result = await self.process_question(i, question_data)
                await asyncio.sleep(1)  # Rate limiting
                return result

        tasks = [
            process_with_rate_limit(i, question_data)
            for i, question_data in enumerate(questions)
        ]

        results = await asyncio.gather(*tasks)

        # Sort results by test_id
        results.sort(key=lambda r: r.get("test_id", 0))

        return results

    async def generate_report(self, results: List[Dict[str, Any]]):
        """Generate a comprehensive report from the test results using GPT."""
        # Calculate statistics
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get("api_response") is not None)
        knowledge_found = sum(
            1 for r in results if r.get("evaluation", {}).get("knowledge_found", False)
        )

        avg_score = (
            sum(r.get("evaluation", {}).get("score", 0) for r in results) / total_tests
            if total_tests > 0
            else 0
        )
        avg_accuracy = (
            sum(r.get("evaluation", {}).get("accuracy", 0) for r in results)
            / total_tests
            if total_tests > 0
            else 0
        )
        avg_completeness = (
            sum(r.get("evaluation", {}).get("completeness", 0) for r in results)
            / total_tests
            if total_tests > 0
            else 0
        )
        avg_clarity = (
            sum(r.get("evaluation", {}).get("clarity", 0) for r in results)
            / total_tests
            if total_tests > 0
            else 0
        )
        avg_relevance = (
            sum(r.get("evaluation", {}).get("relevance", 0) for r in results)
            / total_tests
            if total_tests > 0
            else 0
        )

        # Prepare summary statistics
        summary_stats = {
            "total_tests": total_tests,
            "successful_tests": successful_tests,
            "success_rate": (
                successful_tests / total_tests * 100 if total_tests > 0 else 0
            ),
            "knowledge_found": knowledge_found,
            "knowledge_rate": (
                knowledge_found / total_tests * 100 if total_tests > 0 else 0
            ),
            "avg_score": avg_score,
            "avg_accuracy": avg_accuracy,
            "avg_completeness": avg_completeness,
            "avg_clarity": avg_clarity,
            "avg_relevance": avg_relevance,
            "date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

        # Add matching metrics
        matching_symptom = sum(
            1 for r in results if r.get("evaluation", {}).get("matching_symptom", False)
        )
        matching_problem = sum(
            1 for r in results if r.get("evaluation", {}).get("matching_problem", False)
        )
        matching_cause = sum(
            1 for r in results if r.get("evaluation", {}).get("matching_cause", False)
        )
        matching_solution = sum(
            1
            for r in results
            if r.get("evaluation", {}).get("matching_solution", False)
        )

        summary_stats.update(
            {
                "matching_symptom": matching_symptom,
                "matching_symptom_rate": (
                    matching_symptom / total_tests * 100 if total_tests > 0 else 0
                ),
                "matching_problem": matching_problem,
                "matching_problem_rate": (
                    matching_problem / total_tests * 100 if total_tests > 0 else 0
                ),
                "matching_cause": matching_cause,
                "matching_cause_rate": (
                    matching_cause / total_tests * 100 if total_tests > 0 else 0
                ),
                "matching_solution": matching_solution,
                "matching_solution_rate": (
                    matching_solution / total_tests * 100 if total_tests > 0 else 0
                ),
            }
        )

        # Select top and bottom 3 examples for illustration
        scored_results = [
            (r, r.get("evaluation", {}).get("score", 0))
            for r in results
            if r.get("api_response") is not None
        ]
        scored_results.sort(key=lambda x: x[1], reverse=True)

        top_examples = (
            scored_results[:3] if len(scored_results) >= 3 else scored_results
        )
        bottom_examples = scored_results[-3:] if len(scored_results) >= 3 else []

        # Create examples for the report
        examples = []

        for idx, (result, score) in enumerate(top_examples):
            examples.append(
                {
                    "type": "top",
                    "index": idx + 1,
                    "question": result.get("question", ""),
                    "answer": result.get("api_response", {}).get("answer", ""),
                    "score": score,
                    "evaluation": (result.get("evaluation", {}).get("evaluation", "")),
                }
            )

        for idx, (result, score) in enumerate(bottom_examples):
            examples.append(
                {
                    "type": "bottom",
                    "index": idx + 1,
                    "question": result.get("question", ""),
                    "answer": result.get("api_response", {}).get("answer", ""),
                    "score": score,
                    "evaluation": (result.get("evaluation", {}).get("evaluation", "")),
                }
            )

        # Prepare prompt for GPT to generate Notion-style report
        prompt = f"""
        Create a comprehensive, professional Notion-style report analyzing the accuracy of a revolving door query system.

        Here are the summary statistics:
        ```json
        {json.dumps(summary_stats, indent=2)}
        ```

        Here are some example test cases (top and bottom performers):
        ```json
        {json.dumps(examples, indent=2)}
        ```

        Create a complete, well-structured report with the following sections:
        1. Executive Summary - High-level overview of test results and key findings
        2. Test Methodology - How the test was conducted
        3. Results Analysis - Detailed analysis of results with metrics
           - Overall System Performance
           - Knowledge Retrieval Performance
           - Answer Quality Analysis
           - Component Matching Accuracy
        4. Example Analysis - Analysis of top and bottom performing examples
        5. Recommendations - Suggestions for improvement based on findings
        6. Conclusion - Summary of findings and next steps

        Use Notion-style formatting with headings, subheadings, callouts, tables, and other formatting to make the report readable and professional.
        Include data visualizations described in text (e.g., "Bar chart showing scores across different metrics").

        The report should be detailed and insightful, focused on helping the team understand the system's strengths and weaknesses.
        """

        # Generate the report
        try:
            response = await self.async_openai_client.chat.completions.create(
                model="gpt-4.1-mini",
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "You are an expert data analyst creating comprehensive reports on system performance."
                        ),
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.5,
                max_tokens=4000,
            )

            report_text = response.choices[0].message.content

            # Save the report
            report_file = f"revolving_door_accuracy_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            with open(report_file, "w", encoding="utf-8") as f:
                f.write(report_text)

            logger.info(f"Report saved to {report_file}")

            # Print a portion of the report to the console
            print("\n===== REVOLVING DOOR QUERY TEST REPORT =====")
            print(f"Full report saved to: {report_file}")
            print("===========================================\n")
            print(report_text[:500] + "...\n")

            return report_file

        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")

            # Fallback to basic report
            basic_report = f"""
            # Revolving Door Query System Accuracy Report

            ## Summary Statistics
            - Total tests: {total_tests}
            - Successful API calls: {successful_tests} ({summary_stats['success_rate']:.1f}%)
            - Knowledge found: {knowledge_found} ({summary_stats['knowledge_rate']:.1f}%)

            ## Average Scores
            - Overall: {avg_score:.1f}/10
            - Accuracy: {avg_accuracy:.1f}/10
            - Completeness: {avg_completeness:.1f}/10
            - Clarity: {avg_clarity:.1f}/10
            - Relevance: {avg_relevance:.1f}/10

            ## Component Matching
            - Symptom match: {matching_symptom} ({summary_stats['matching_symptom_rate']:.1f}%)
            - Problem match: {matching_problem} ({summary_stats['matching_problem_rate']:.1f}%)
            - Cause match: {matching_cause} ({summary_stats['matching_cause_rate']:.1f}%)
            - Solution match: {matching_solution} ({summary_stats['matching_solution_rate']:.1f}%)

            Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            report_file = f"revolving_door_accuracy_report_basic_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            with open(report_file, "w", encoding="utf-8") as f:
                f.write(basic_report)

            logger.info(f"Basic report saved to {report_file}")
            print(basic_report)

            return report_file

    async def save_results(self, results: List[Dict[str, Any]]):
        """Save raw results to files for further analysis."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save raw results to JSON
        json_file = f"query_test_results_{timestamp}.json"
        with open(json_file, "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)

        logger.info(f"Raw results saved to {json_file}")

        # Save summary to CSV
        csv_file = f"query_test_results_{timestamp}.csv"
        with open(csv_file, "w", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            writer.writerow(
                [
                    "Test ID",
                    "Question",
                    "Knowledge Found",
                    "Paths Found",
                    "Overall Score",
                    "Accuracy",
                    "Completeness",
                    "Clarity",
                    "Relevance",
                    "Symptom Match",
                    "Problem Match",
                    "Cause Match",
                    "Solution Match",
                    "Evaluation",
                ]
            )

            for result in results:
                eval_data = result.get("evaluation", {})

                writer.writerow(
                    [
                        result.get("test_id", ""),
                        result.get("question", ""),
                        ("Yes" if eval_data.get("knowledge_found", False) else "No"),
                        eval_data.get("num_paths", 0),
                        eval_data.get("score", 0),
                        eval_data.get("accuracy", 0),
                        eval_data.get("completeness", 0),
                        eval_data.get("clarity", 0),
                        eval_data.get("relevance", 0),
                        ("Yes" if eval_data.get("matching_symptom", False) else "No"),
                        ("Yes" if eval_data.get("matching_problem", False) else "No"),
                        ("Yes" if eval_data.get("matching_cause", False) else "No"),
                        ("Yes" if eval_data.get("matching_solution", False) else "No"),
                        eval_data.get("evaluation", ""),
                    ]
                )

        logger.info(f"Summary results saved to {csv_file}")
        return json_file, csv_file


async def main():
    """Main function to run the test."""
    parser = argparse.ArgumentParser(
        description="Test revolving door query system accuracy"
    )
    parser.add_argument(
        "--num-tests",
        type=int,
        default=DEFAULT_CONFIG["num_test_cases"],
        help="Number of test cases to generate",
    )
    parser.add_argument(
        "--api-url",
        type=str,
        default=DEFAULT_CONFIG["api_url"],
        help="API URL",
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=DEFAULT_CONFIG["test_seed"],
        help="Random seed for reproducibility",
    )
    parser.add_argument(
        "--concurrent",
        type=int,
        default=DEFAULT_CONFIG["concurrent_requests"],
        help="Number of concurrent requests",
    )
    args = parser.parse_args()

    # Update config with command line arguments
    config = DEFAULT_CONFIG.copy()
    config["num_test_cases"] = args.num_tests
    config["api_url"] = args.api_url
    config["test_seed"] = args.seed
    config["concurrent_requests"] = args.concurrent

    # Validate required environment variables
    if not config["openai_api_key"]:
        logger.error("OPENAI_API_KEY environment variable is required")
        print("Please set the OPENAI_API_KEY environment variable")
        return

    logger.info(f"Starting test with {config['num_test_cases']} test cases")
    print(
        f"Testing revolving door query system with {config['num_test_cases']} test cases..."
    )
    print(f"API URL: {config['api_url']}")

    try:
        tester = RevolvingDoorTester(config)
        await tester.setup()

        # Run the test
        start_time = time.time()
        results = await tester.run_test()

        # Save results
        json_file, csv_file = await tester.save_results(results)

        # Generate report
        report_file = await tester.generate_report(results)

        # Cleanup
        await tester.cleanup()

        elapsed_time = time.time() - start_time
        logger.info(f"Test completed in {elapsed_time:.2f} seconds")
        print(f"\nTest completed in {elapsed_time:.2f} seconds")
        print(f"Raw results: {json_file}")
        print(f"CSV summary: {csv_file}")
        print(f"Report: {report_file}")
        print("\nDone!")

    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        print(f"Test failed: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
