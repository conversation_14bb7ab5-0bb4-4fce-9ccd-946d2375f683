"""
Neo4j database initialization and seeding script for OCS structure.
Creates the necessary constraints and indexes, and populates the database from JSON.
"""

import json
import os
import asyncio
import logging
from datetime import datetime, UTC
import uuid
import json
from passlib.context import CryptContext
from typing import Dict, List, Any, Optional, Set
from collections import defaultdict
import random

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase, AsyncDriver, AsyncSession


# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

load_dotenv()

# Neo4j connection parameters
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USERNAME = os.getenv("NEO4J_USERNAME", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "password")

# JSON File path
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
JSON_PATH = os.getenv(
    "JSON_PATH",
    os.path.join(SCRIPT_DIR, "sample_data", "synthetic_dataset_v3.json"),
)


# Track statistics
stats = {
    "nodes_created": 0,
    "nodes_skipped": 0,
    "regular_edges_created": 0,
    "solution_door_edges_created": 0,
    "edges_skipped": 0,
    "door_types_created": 0,
    "validation_errors": 0,
    "paths_traced": 0,
    "batches_processed": 0,
    "batches_processed": 0,
}


class ValidationError(Exception):
    """Exception for data validation errors."""

    pass


async def create_constraints(session: AsyncSession) -> None:
    """Create Neo4j constraints for uniqueness and indexing for OCS structure."""
    logger.info("Creating constraints and indexes...")

    # Create constraints - updated for OCS structure
    constraints = [
        "CREATE CONSTRAINT IF NOT EXISTS FOR (n:OBSERVATION) REQUIRE n.id IS UNIQUE",
        "CREATE CONSTRAINT IF NOT EXISTS FOR (n:CAUSE) REQUIRE n.id IS UNIQUE",
        "CREATE CONSTRAINT IF NOT EXISTS FOR (n:SOLUTION) REQUIRE n.id IS UNIQUE",
        "CREATE CONSTRAINT IF NOT EXISTS FOR (n:DOOR_TYPE) REQUIRE n.id IS UNIQUE",
        "CREATE CONSTRAINT IF NOT EXISTS FOR (n:DOOR_PART) REQUIRE n.id IS UNIQUE",
        "CREATE CONSTRAINT IF NOT EXISTS FOR (n:ENVIRONMENT) REQUIRE n.id IS UNIQUE",
    ]

    # Create indexes for OCS structure
    indexes = [
        "CREATE INDEX IF NOT EXISTS FOR (n:OBSERVATION) ON (n.name)",
        "CREATE INDEX IF NOT EXISTS FOR (n:OBSERVATION) ON (n.visual_observation)",
        "CREATE INDEX IF NOT EXISTS FOR (n:OBSERVATION) ON (n.auditory_observation)",
        "CREATE INDEX IF NOT EXISTS FOR (n:OBSERVATION) ON (n.positional_observation)",
        "CREATE INDEX IF NOT EXISTS FOR (n:OBSERVATION) ON (n.error_codes)",
        "CREATE INDEX IF NOT EXISTS FOR (n:CAUSE) ON (n.name)",
        "CREATE INDEX IF NOT EXISTS FOR (n:SOLUTION) ON (n.name)",
        "CREATE INDEX IF NOT EXISTS FOR (n:DOOR_TYPE) ON (n.model)",
        # NEW: Feedback system indexes
        "CREATE INDEX IF NOT EXISTS FOR ()-[r:RESOLVED_BY]-() ON (r.trust_score)",
        "CREATE INDEX IF NOT EXISTS FOR ()-[r:RESOLVED_BY]-() ON (r.feedback_entries)",
    ]

    # Execute constraints
    for constraint in constraints:
        try:
            await session.run(constraint)
        except Exception as e:
            logger.error(f"Error creating constraint: {str(e)}")

    # Execute indexes
    for index in indexes:
        try:
            await session.run(index)
        except Exception as e:
            logger.error(f"Error creating index: {str(e)}")

    # Create vector indices for all embedding types (SINGLE DEFINITION)
    vector_indices = [
        # Original embedding indices
        """
        CREATE VECTOR INDEX observation_embedding IF NOT EXISTS
        FOR (n:OBSERVATION)
        ON (n.embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        """
        CREATE VECTOR INDEX cause_embedding IF NOT EXISTS
        FOR (n:CAUSE)
        ON (n.embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        """
        CREATE VECTOR INDEX solution_embedding IF NOT EXISTS
        FOR (n:SOLUTION)
        ON (n.embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        # New multi-embedding indices
        """
        CREATE VECTOR INDEX observation_visual_embedding IF NOT EXISTS
        FOR (n:OBSERVATION)
        ON (n.visual_embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        """
        CREATE VECTOR INDEX observation_auditory_embedding IF NOT EXISTS
        FOR (n:OBSERVATION)
        ON (n.auditory_embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        """
        CREATE VECTOR INDEX observation_positional_embedding IF NOT EXISTS
        FOR (n:OBSERVATION)
        ON (n.positional_embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
    ]

    # Create vector indices
    for index_query in vector_indices:
        try:
            await session.run(index_query)
            logger.info(f"Created vector index: {index_query.strip().split()[3]}")
        except Exception as e:
            logger.error(f"Error creating vector index: {str(e)}")

    logger.info("Constraints and indexes created")


async def read_json_data(file_path: str) -> Dict[str, Any]:
    """Read and validate JSON data from file."""
    logger.info(f"Reading data from {file_path}...")

    try:
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)

        # Basic validation
        if not isinstance(data, dict):
            raise ValidationError("JSON data must be an object/dictionary")

        if "nodes" not in data or not isinstance(data["nodes"], list):
            raise ValidationError("JSON data must contain a 'nodes' array")

        if "edges" not in data or not isinstance(data["edges"], list):
            raise ValidationError("JSON data must contain an 'edges' array")

        # Count node types for logging
        node_types = {}
        for node in data.get("nodes", []):
            node_type = node.get("type", "unknown")
            node_types[node_type] = node_types.get(node_type, 0) + 1

        logger.info(f"Node types found: {node_types}")

        return data
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON format: {str(e)}")
        raise ValidationError(f"Invalid JSON format: {str(e)}")
    except Exception as e:
        logger.error(f"Error reading JSON data: {str(e)}")
        raise


async def validate_node(node_data: Dict[str, Any]) -> None:
    """Validate node data from JSON."""
    node_id = node_data.get("id")
    node_type = node_data.get("type")

    if not node_id:
        raise ValidationError("Node missing required 'id' field")

    if not node_type:
        raise ValidationError(f"Node {node_id} missing required 'type' field")

    if node_type not in ["O", "C", "S", "D"]:  # Added "D" for Door type
        raise ValidationError(
            f"Node {node_id} has invalid type '{node_type}', must be O, C, S, or D"
        )

    # Type-specific validation
    if node_type == "O":
        observation = node_data.get("observation")
        if not observation:
            raise ValidationError(
                f"Observation node {node_id} missing 'observation' object"
            )

        # Only check if the observation has ANY properties (not necessarily visual)
        if not any(observation.values()):
            raise ValidationError(
                f"Observation node {node_id} has no properties (empty observation)"
            )

    elif node_type == "C":
        cause = node_data.get("cause")
        if not cause:
            raise ValidationError(
                f"Cause node {node_id} missing required 'cause' field"
            )

    elif node_type == "S":
        solution = node_data.get("solution")
        if not solution:
            raise ValidationError(
                f"Solution node {node_id} missing required 'solution' field"
            )

    # Door type nodes don't need validation beyond having an ID and type


async def validate_edge(edge_data: Dict[str, Any], node_map: Dict[str, str]) -> None:
    """Validate edge data from JSON."""
    source_id = edge_data.get("source")
    target_id = edge_data.get("target")

    if not source_id:
        raise ValidationError("Edge missing required 'source' field")

    if not target_id:
        raise ValidationError("Edge missing required 'target' field")

    # Validate that nodes exist
    if source_id not in node_map:
        raise ValidationError(f"Edge references non-existent source node '{source_id}'")

    if target_id not in node_map:
        raise ValidationError(f"Edge references non-existent target node '{target_id}'")


async def create_node(
    session: AsyncSession, node_data: Dict[str, Any], embedding_service=None
) -> Optional[str]:
    """
    Create a node in the database from JSON data.
    Updated to support multi-embeddings for OBSERVATION nodes and pre-generated embeddings.
    """
    try:
        await validate_node(node_data)
    except ValidationError as e:
        logger.warning(f"Node validation error: {str(e)}")
        stats["validation_errors"] += 1
        stats["nodes_skipped"] += 1
        return None

    node_id = node_data.get("id")
    node_type = node_data.get("type")

    # Map single-letter type to database node type
    type_mapping = {
        "O": "OBSERVATION",
        "C": "CAUSE",
        "S": "SOLUTION",
        "D": "DOOR_TYPE",
    }

    db_node_type = type_mapping.get(node_type)

    if not db_node_type:
        logger.warning(f"Unknown node type '{node_type}' for node {node_id}")
        stats["nodes_skipped"] += 1
        return None

    properties = {
        "id": node_id,
        "created_at": datetime.now(UTC).isoformat(),
        "updated_at": datetime.now(UTC).isoformat(),
    }

    # Check if pre-generated embeddings are available in node_data
    pre_generated_embeddings = {}
    if "properties" in node_data:
        pre_generated_embeddings = node_data.get("properties", {})

    if db_node_type == "OBSERVATION":
        observation_data = node_data.get("observation", {})

        # Map JSON fields to database fields with proper mapping for auditory/auditory
        visual = observation_data.get("visual", "")
        auditory = observation_data.get("auditory", "")
        positional = observation_data.get("positional", "")
        error_codes = observation_data.get("error_codes", "")

        # Use visual as name/description if present, otherwise use the first non-empty field
        name = visual
        if not name and auditory:
            name = auditory
        if not name and positional:
            name = positional
        if not name and error_codes:
            name = f"Error code: {error_codes}"
        if not name:
            name = "Unspecified observation"

        properties.update(
            {
                "name": name,
                "description": name,
                "visual_observation": visual,
                "auditory_observation": auditory,  # Map auditory to auditory
                "positional_observation": positional,
                "error_codes": error_codes,
            }
        )

        # Handle related_parts as a list or convert to one
        related_parts = observation_data.get("related_parts", [])
        if isinstance(related_parts, list):
            properties["related_parts"] = json.dumps(related_parts)
        else:
            properties["related_parts"] = json.dumps([related_parts])

        # Use pre-generated embeddings if available
        if pre_generated_embeddings:
            if "visual_embedding" in pre_generated_embeddings:
                properties["visual_embedding"] = pre_generated_embeddings[
                    "visual_embedding"
                ]
            if "auditory_embedding" in pre_generated_embeddings:
                properties["auditory_embedding"] = pre_generated_embeddings[
                    "auditory_embedding"
                ]
            if "positional_embedding" in pre_generated_embeddings:
                properties["positional_embedding"] = pre_generated_embeddings[
                    "positional_embedding"
                ]
            if "embedding" in pre_generated_embeddings:
                properties["embedding"] = pre_generated_embeddings["embedding"]

            # Set the embedding update timestamp
            properties["embedding_updated_at"] = datetime.now(UTC).isoformat()
        # Generate embeddings if service provided and no pre-generated embeddings
        elif embedding_service:
            # Generate embeddings for different observation types
            if visual:
                visual_embedding = embedding_service.generate_embedding(visual).tolist()
                properties["visual_embedding"] = visual_embedding
            else:
                visual_embedding = embedding_service.generate_embedding(name).tolist()
                properties["visual_embedding"] = visual_embedding

            if auditory:
                auditory_embedding = embedding_service.generate_embedding(
                    auditory
                ).tolist()
                properties["auditory_embedding"] = auditory_embedding
            else:
                auditory_embedding = embedding_service.generate_embedding(name).tolist()
                properties["auditory_embedding"] = auditory_embedding

            if positional:
                positional_embedding = embedding_service.generate_embedding(
                    positional
                ).tolist()
                properties["positional_embedding"] = positional_embedding
            else:
                positional_embedding = embedding_service.generate_embedding(
                    name
                ).tolist()
                properties["positional_embedding"] = positional_embedding

            # Also add the combined embedding for backward compatibility
            combined_text = f"{name} {visual} {auditory} {positional} {error_codes}"
            properties["embedding"] = embedding_service.generate_embedding(
                combined_text
            ).tolist()

            # Set the embedding update timestamp
            properties["embedding_updated_at"] = datetime.now(UTC).isoformat()

    elif db_node_type == "CAUSE" or db_node_type == "SOLUTION":
        cause_description = (
            node_data.get("cause", "") if db_node_type == "CAUSE" else ""
        )
        solution_description = (
            node_data.get("solution", "") if db_node_type == "SOLUTION" else ""
        )
        content = cause_description or solution_description

        properties.update(
            {
                "name": content,
                "description": content,
            }
        )

        # Use pre-generated embeddings if available
        if pre_generated_embeddings and "embedding" in pre_generated_embeddings:
            properties["embedding"] = pre_generated_embeddings["embedding"]
            properties["embedding_updated_at"] = datetime.now(UTC).isoformat()
        # Generate regular embedding if embedding_service is provided
        elif embedding_service:
            text = f"{content} {content}"  # Repeating for emphasis
            properties["embedding"] = embedding_service.generate_embedding(
                text
            ).tolist()
            properties["embedding_updated_at"] = datetime.now(UTC).isoformat()

    elif db_node_type == "DOOR_TYPE":
        # For door nodes, use the ID as the model name if no other information is provided
        # This simplifies door node handling to just need an ID and type
        door_model = f"Door-{node_id}"
        properties.update(
            {
                "model": door_model,
                "manufacturer": "Boon Edam",
                "name": door_model,  # For consistency
                "description": f"Door model {door_model}",
            }
        )

    # Store properties as JSON string for Neo4j
    properties_json = json.dumps(properties)

    query = f"""
    MERGE (n:{db_node_type} {{id: $id}})
    ON CREATE SET
        n.id = $id,
        n.name = $name,
        n.description = $description,
        n.created_at = $created_at,
        n.updated_at = $updated_at
    """

    params = {
        "id": node_id,
        "name": properties.get("name", ""),
        "description": properties.get("description", ""),
        "created_at": properties["created_at"],
        "updated_at": properties["updated_at"],
    }

    # Add type-specific properties based on node type
    if db_node_type == "OBSERVATION":
        query += """
        , n.visual_observation = $visual_observation,
        n.auditory_observation = $auditory_observation,
        n.positional_observation = $positional_observation,
        n.error_codes = $error_codes,
        n.related_parts = $related_parts
        """

        params.update(
            {
                "visual_observation": properties.get("visual_observation", ""),
                "auditory_observation": properties.get("auditory_observation", ""),
                "positional_observation": properties.get("positional_observation", ""),
                "error_codes": properties.get("error_codes", ""),
                "related_parts": properties.get("related_parts", "[]"),
            }
        )

        # Add embeddings if available
        if (
            "visual_embedding" in properties
            and "auditory_embedding" in properties
            and "positional_embedding" in properties
        ):
            query += """
            , n.visual_embedding = $visual_embedding,
            n.auditory_embedding = $auditory_embedding,
            n.positional_embedding = $positional_embedding,
            n.embedding = $embedding,
            n.embedding_updated_at = $embedding_updated_at
            """

            params.update(
                {
                    "visual_embedding": properties.get("visual_embedding"),
                    "auditory_embedding": properties.get("auditory_embedding"),
                    "positional_embedding": properties.get("positional_embedding"),
                    "embedding": properties.get("embedding"),
                    "embedding_updated_at": properties.get("embedding_updated_at"),
                }
            )

    elif db_node_type == "DOOR_TYPE":
        query += """
        , n.model = $model,
        n.manufacturer = $manufacturer
        """

        params.update(
            {
                "model": properties.get("model", ""),
                "manufacturer": properties.get("manufacturer", ""),
            }
        )

    elif "embedding" in properties:
        # Add embedding for CAUSE and SOLUTION nodes
        query += """
        , n.embedding = $embedding,
        n.embedding_updated_at = $embedding_updated_at
        """

        params.update(
            {
                "embedding": properties.get("embedding"),
                "embedding_updated_at": properties.get("embedding_updated_at"),
            }
        )

    query += " RETURN n.id as id"

    try:
        result = await session.run(query, params)
        record = await result.single()

        if record:
            if db_node_type == "DOOR_TYPE":
                stats["door_types_created"] += 1
                logger.debug(f"Created door type with ID {node_id}")
            else:
                stats["nodes_created"] += 1
            return record["id"]
        else:
            stats["nodes_skipped"] += 1
            return None
    except Exception as e:
        logger.error(f"Error creating node {node_id}: {str(e)}")
        stats["nodes_skipped"] += 1
        return None


async def create_nodes_with_batch_embeddings(
    session: AsyncSession,
    nodes_batch: List[Dict[str, Any]],
    embedding_service=None,
) -> Dict[str, str]:
    """Create multiple nodes with batch embedding generation."""
    node_map = {}

    if not embedding_service:
        # If no embedding service, just create nodes individually
        for node in nodes_batch:
            node_id = node.get("id")
            node_type = node.get("type")

            if node_id and node_type:
                created_id = await create_node(session, node, None)
                if created_id:
                    node_map[node_id] = node_type
        return node_map

    # Prepare batches for different node types
    observation_nodes = []
    cause_solution_nodes = []
    door_nodes = []

    # Organize nodes by type
    for node in nodes_batch:
        node_id = node.get("id")
        node_type = node.get("type")

        if not node_id or not node_type:
            continue

        if node_type == "O":
            observation_nodes.append(node)
        elif node_type in ["C", "S"]:
            cause_solution_nodes.append(node)
        elif node_type == "D":
            door_nodes.append(node)

    # Process OBSERVATION nodes with batch embeddings
    if observation_nodes:
        # Collect texts for different observation types
        visual_texts = []
        auditory_texts = []
        positional_texts = []
        combined_texts = []
        obs_node_ids = []

        for node in observation_nodes:
            node_id = node.get("id")
            observation_data = node.get("observation", {})

            visual = observation_data.get("visual", "")
            auditory = observation_data.get("auditory", "")
            positional = observation_data.get("positional", "")
            error_codes = observation_data.get("error_codes", "")

            # Use visual as name/description if present, otherwise use the first non-empty field
            name = visual
            if not name and auditory:
                name = auditory
            if not name and positional:
                name = positional
            if not name and error_codes:
                name = f"Error code: {error_codes}"
            if not name:
                name = "Unspecified observation"

            # Add texts to their respective lists
            visual_texts.append(visual if visual else name)
            auditory_texts.append(auditory if auditory else name)
            positional_texts.append(positional if positional else name)
            combined_texts.append(
                f"{name} {visual} {auditory} {positional} {error_codes}"
            )
            obs_node_ids.append(node_id)

        # Generate embeddings in batch
        visual_embeddings = embedding_service.generate_embeddings_batch(visual_texts)
        auditory_embeddings = embedding_service.generate_embeddings_batch(
            auditory_texts
        )
        positional_embeddings = embedding_service.generate_embeddings_batch(
            positional_texts
        )
        combined_embeddings = embedding_service.generate_embeddings_batch(
            combined_texts
        )

        # Now create each observation node with its embeddings
        for i, node in enumerate(observation_nodes):
            # Add embeddings to node properties
            node_copy = node.copy()
            if "properties" not in node_copy:
                node_copy["properties"] = {}

            if i < len(visual_embeddings):
                node_copy["properties"]["visual_embedding"] = visual_embeddings[
                    i
                ].tolist()
                node_copy["properties"]["auditory_embedding"] = auditory_embeddings[
                    i
                ].tolist()
                node_copy["properties"]["positional_embedding"] = positional_embeddings[
                    i
                ].tolist()
                node_copy["properties"]["embedding"] = combined_embeddings[i].tolist()

            # Create the node
            created_id = await create_node(
                session, node_copy, None
            )  # Pass None to avoid regenerating embeddings
            if created_id:
                node_map[node.get("id")] = node.get("type")

    # Process CAUSE and SOLUTION nodes with batch embeddings
    if cause_solution_nodes:
        # Collect texts
        texts = []
        cs_node_ids = []
        node_types = []

        for node in cause_solution_nodes:
            node_id = node.get("id")
            node_type = node.get("type")

            if node_type == "C":
                content = node.get("cause", "")
            else:  # "S"
                content = node.get("solution", "")

            # Repeat text for emphasis as in the original code
            text = f"{content} {content}"
            texts.append(text)
            cs_node_ids.append(node_id)
            node_types.append(node_type)

        # Generate embeddings in batch
        embeddings = embedding_service.generate_embeddings_batch(texts)

        # Create each node with its embedding
        for i, node in enumerate(cause_solution_nodes):
            # Add embedding to node properties
            node_copy = node.copy()
            if "properties" not in node_copy:
                node_copy["properties"] = {}

            if i < len(embeddings):
                node_copy["properties"]["embedding"] = embeddings[i].tolist()

            # Create the node
            created_id = await create_node(
                session, node_copy, None
            )  # Pass None to avoid regenerating embeddings
            if created_id:
                node_map[node.get("id")] = node.get("type")

    # Process door nodes (no embeddings needed)
    for node in door_nodes:
        created_id = await create_node(session, node, None)
        if created_id:
            node_map[node.get("id")] = node.get("type")

    # Update stats
    stats["batches_processed"] += 1

    return node_map


async def create_regular_relationship(
    session: AsyncSession, edge_data: Dict[str, Any], node_map: Dict[str, str]
) -> bool:
    """Create a standard OCS relationship (Observation -> Cause or Cause -> Solution)."""
    try:
        await validate_edge(edge_data, node_map)
    except ValidationError as e:
        logger.warning(f"Edge validation error: {str(e)}")
        stats["validation_errors"] += 1
        stats["edges_skipped"] += 1
        return False

    source_id = edge_data.get("source")
    target_id = edge_data.get("target")

    # Get the database node types
    source_type = node_map[source_id]
    target_type = node_map[target_id]

    # Only process Observation -> Cause and Cause -> Solution relationships
    # Skip Door -> Observation edges as they will be handled separately
    if source_type == "O" and target_type == "C":
        rel_type = "OBSERVED_WITH"
    elif source_type == "C" and target_type == "S":
        rel_type = "RESOLVED_BY"
    else:
        # Skip Door -> Observation edges without counting as skipped
        return False

    # Get Neo4j node type strings
    source_db_type = "OBSERVATION" if source_type == "O" else "CAUSE"
    target_db_type = "CAUSE" if target_type == "C" else "SOLUTION"

    # Base properties for all relationships
    properties = {
        "created_at": datetime.now(UTC).isoformat(),
        "updated_at": datetime.now(UTC).isoformat(),
        "valid_from": datetime.now(UTC).isoformat(),
        "is_current": True,
    }

    # Add trust_score and feedback_entries ONLY for RESOLVED_BY relationships
    if rel_type == "RESOLVED_BY":
        properties.update(
            {
                "trust_score": round(random.uniform(0.0, 1.0), 2),
                "feedback_entries": "[]",  # Empty JSON array for feedback
            }
        )

    if rel_type == "RESOLVED_BY":
        query = f"""
        MATCH (a:{source_db_type} {{id: $source_id}}), (b:{target_db_type} {{id: $target_id}})
        MERGE (a)-[r:{rel_type}]->(b)
        ON CREATE SET
            r.trust_score = $trust_score,
            r.feedback_entries = $feedback_entries,
            r.created_at = $created_at,
            r.updated_at = $updated_at,
            r.valid_from = $valid_from,
            r.is_current = $is_current
        RETURN r
        """
    else:
        query = f"""
        MATCH (a:{source_db_type} {{id: $source_id}}), (b:{target_db_type} {{id: $target_id}})
        MERGE (a)-[r:{rel_type}]->(b)
        ON CREATE SET
            r.created_at = $created_at,
            r.updated_at = $updated_at,
            r.valid_from = $valid_from,
            r.is_current = $is_current
        RETURN r
        """

    try:
        params = {
            "source_id": source_id,
            "target_id": target_id,
            "created_at": properties["created_at"],
            "updated_at": properties["updated_at"],
            "valid_from": properties["valid_from"],
            "is_current": properties["is_current"],
        }

        if rel_type == "RESOLVED_BY":
            params.update(
                {
                    "trust_score": properties["trust_score"],
                    "feedback_entries": properties["feedback_entries"],
                }
            )

        result = await session.run(query, params)

        record = await result.single()
        if record:
            stats["regular_edges_created"] += 1
            return True
        else:
            stats["edges_skipped"] += 1
            return False
    except Exception as e:
        logger.error(
            f"Error creating relationship {source_id} -> {target_id}: {str(e)}"
        )
        stats["edges_skipped"] += 1
        return False


async def create_solution_door_relationship(
    session: AsyncSession, solution_id: str, door_id: str
) -> bool:
    """Create a relationship from a Solution to a Door (APPLIES_TO)."""
    properties = {
        "created_at": datetime.now(UTC).isoformat(),
        "updated_at": datetime.now(UTC).isoformat(),
        "valid_from": datetime.now(UTC).isoformat(),
        "is_current": True,
    }

    query = """
    MATCH (s:SOLUTION {id: $solution_id}), (d:DOOR_TYPE {id: $door_id})
    MERGE (s)-[r:APPLIES_TO]->(d)
    ON CREATE SET
        r.created_at = $created_at,
        r.updated_at = $updated_at,
        r.valid_from = $valid_from,
        r.is_current = $is_current
    RETURN r
    """

    try:
        result = await session.run(
            query,
            {
                "solution_id": solution_id,
                "door_id": door_id,
                "created_at": properties["created_at"],
                "updated_at": properties["updated_at"],
                "valid_from": properties["valid_from"],
                "is_current": properties["is_current"],
            },
        )

        record = await result.single()
        if record:
            stats["solution_door_edges_created"] += 1
            return True
        else:
            stats["edges_skipped"] += 1
            return False
    except Exception as e:
        logger.error(
            f"Error creating Solution -> Door relationship {solution_id} -> {door_id}: {str(e)}"
        )
        stats["edges_skipped"] += 1
        return False


async def build_path_graph(
    edges: List[Dict[str, Any]], node_map: Dict[str, str]
) -> Dict[str, List[str]]:
    """
    Build an adjacency graph from the edge data to track paths.
    This creates a map from each node to its outgoing connections.
    """
    graph = defaultdict(list)

    for edge in edges:
        source_id = edge.get("source")
        target_id = edge.get("target")

        if source_id in node_map and target_id in node_map:
            graph[source_id].append(target_id)

    return graph


def find_paths(
    graph: Dict[str, List[str]],
    start: str,
    end_type: str,
    node_map: Dict[str, str],
) -> List[str]:
    """Find all paths from start node to nodes of end_type using DFS."""
    paths = []
    visited = set()

    def dfs(current: str, path: List[str]):
        if node_map[current] == end_type:
            paths.append(current)
            return

        visited.add(current)

        for neighbor in graph.get(current, []):
            if neighbor not in visited:
                dfs(neighbor, path + [neighbor])

        visited.remove(current)

    dfs(start, [start])
    return paths


async def seed_from_json(session: AsyncSession, embedding_service=None) -> None:
    """Seed the database with data from JSON."""
    try:
        data = await read_json_data(JSON_PATH)
    except Exception as e:
        logger.error(f"Failed to read JSON data: {str(e)}")
        return

    nodes = data.get("nodes", [])
    edges = data.get("edges", [])

    logger.info(f"Found {len(nodes)} nodes and {len(edges)} edges in JSON data")

    # Map of node IDs to their types
    node_map = {}

    # Process in batches to avoid memory issues
    batch_size = 50
    for i in range(0, len(nodes), batch_size):
        batch = nodes[i : i + batch_size]
        logger.info(f"Processing node batch {i//batch_size + 1} ({len(batch)} nodes)")

        # Use the new batch embeddings function
        batch_node_map = await create_nodes_with_batch_embeddings(
            session, batch, embedding_service
        )
        node_map.update(batch_node_map)

    logger.info(
        f"Created {stats['nodes_created']} nodes ({stats['door_types_created']} door types), skipped {stats['nodes_skipped']} in {stats['batches_processed']} batches"
    )

    # First, create standard OCS relationships (Observation -> Cause, Cause -> Solution)
    for i in range(0, len(edges), batch_size):
        batch = edges[i : i + batch_size]
        logger.info(
            f"Processing regular relationship batch {i//batch_size + 1} ({len(batch)} edges)"
        )

        for edge in batch:
            await create_regular_relationship(session, edge, node_map)

    logger.info(f"Created {stats['regular_edges_created']} regular relationships")

    # Now build the path graph and find Door -> Solution paths
    logger.info("Building path graph for Door -> Solution connections...")

    graph = await build_path_graph(edges, node_map)

    # Find all door nodes
    door_nodes = [
        node_id for node_id, node_type in node_map.items() if node_type == "D"
    ]
    logger.info(f"Found {len(door_nodes)} door nodes to process")

    # For each door, find paths to solutions
    solution_door_pairs = []

    for door_id in door_nodes:
        # Find all solutions reachable from this door
        solution_nodes = find_paths(graph, door_id, "S", node_map)
        stats["paths_traced"] += len(solution_nodes)

        # Create pairs for Solution -> Door relationships
        for solution_id in solution_nodes:
            solution_door_pairs.append((solution_id, door_id))

    logger.info(
        f"Found {len(solution_door_pairs)} Solution -> Door relationships to create"
    )

    # Create Solution -> Door relationships
    for i in range(0, len(solution_door_pairs), batch_size):
        batch = solution_door_pairs[i : i + batch_size]
        logger.info(
            f"Processing Solution -> Door relationship batch {i//batch_size + 1} ({len(batch)} pairs)"
        )

        for solution_id, door_id in batch:
            await create_solution_door_relationship(session, solution_id, door_id)

    logger.info(
        f"Created {stats['solution_door_edges_created']} Solution -> Door relationships"
    )

    # Check for disconnected solutions (without APPLIES_TO relationships)
    query = """
    MATCH (s:SOLUTION)
    WHERE NOT (s)-[:APPLIES_TO]->(:DOOR_TYPE)
    RETURN count(s) as disconnected_solutions
    """

    result = await session.run(query)
    record = await result.single()
    disconnected_solutions = record["disconnected_solutions"] if record else 0

    logger.info(f"Found {disconnected_solutions} solutions without door connections")

    # Print final statistics
    logger.info("Database seeding stats:")
    for key, value in stats.items():
        logger.info(f"  {key}: {value}")


pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


# Default mechanics to create
DEFAULT_MECHANICS = [
    {
        "name": "John Smith",
        "email": "<EMAIL>",
        "password": "admin123",
        "role": "ADMIN",
        "reliability_score": 0.95,
        "experience_years": 15,
        "properties": {
            "department": "Technical Support",
            "specialization": "Revolving Doors",
            "location": "Netherlands",
        },
    },
    {
        "name": "Sarah Johnson",
        "email": "<EMAIL>",
        "password": "mechanic123",
        "role": "MECHANIC",
        "reliability_score": 0.88,
        "experience_years": 8,
        "properties": {
            "department": "Field Service",
            "specialization": "Maintenance",
            "location": "Germany",
        },
    },
    {
        "name": "Development User",
        "email": "<EMAIL>",
        "password": "dev123",
        "role": "ADMIN",
        "reliability_score": 1.0,
        "experience_years": 10,
        "properties": {
            "department": "Development",
            "specialization": "System Testing",
            "location": "Local",
        },
    },
]


async def create_mechanic_constraints(session: AsyncSession) -> None:
    """Create Neo4j constraints and indexes for mechanics."""
    logger.info("Creating mechanic constraints and indexes...")

    # Create constraints for mechanics
    constraints = [
        "CREATE CONSTRAINT IF NOT EXISTS FOR (m:Mechanic) REQUIRE m.id IS UNIQUE",
        "CREATE CONSTRAINT IF NOT EXISTS FOR (m:Mechanic) REQUIRE m.email IS UNIQUE",
    ]

    # Create indexes for mechanics
    indexes = [
        "CREATE INDEX IF NOT EXISTS FOR (m:Mechanic) ON (m.email)",
        "CREATE INDEX IF NOT EXISTS FOR (m:Mechanic) ON (m.role)",
        "CREATE INDEX IF NOT EXISTS FOR (m:Mechanic) ON (m.name)",
    ]

    # Execute constraints
    for constraint in constraints:
        try:
            await session.run(constraint)
            logger.info(f"Created mechanic constraint: {constraint}")
        except Exception as e:
            logger.error(f"Error creating mechanic constraint: {str(e)}")

    # Execute indexes
    for index in indexes:
        try:
            await session.run(index)
            logger.info(f"Created mechanic index: {index}")
        except Exception as e:
            logger.error(f"Error creating mechanic index: {str(e)}")

    logger.info("Mechanic constraints and indexes created")


async def check_mechanics_exist(session: AsyncSession) -> bool:
    """Check if any mechanics exist in the database."""
    query = """
    MATCH (m:Mechanic)
    RETURN count(m) as count
    """

    result = await session.run(query)
    record = await result.single()
    count = record["count"] if record else 0

    logger.info(f"Found {count} existing mechanics in database")
    return count > 0


async def seed_mechanics(
    session: AsyncSession, mechanics_data: List[Dict[str, Any]] = None
) -> Dict[str, int]:
    """Seed mechanics in the database using Neo4j session."""
    if mechanics_data is None:
        mechanics_data = DEFAULT_MECHANICS

    logger.info(f"Seeding {len(mechanics_data)} mechanics...")

    stats = {"created": 0, "skipped": 0, "errors": 0}

    for mechanic_data in mechanics_data:
        try:
            # Check if mechanic already exists
            check_query = """
            MATCH (m:Mechanic {email: $email})
            RETURN m.id as id
            """

            check_result = await session.run(
                check_query, {"email": mechanic_data["email"]}
            )
            existing = await check_result.single()

            if existing:
                logger.info(
                    f"Mechanic {mechanic_data['email']} already exists, skipping"
                )
                stats["skipped"] += 1
                continue

            # Generate unique ID
            mechanic_id = str(uuid.uuid4())

            # Hash the password
            hashed_password = get_password_hash(mechanic_data["password"])

            # Prepare properties JSON
            properties_json = ""
            if mechanic_data.get("properties"):
                properties_json = json.dumps(mechanic_data["properties"])

            # Create mechanic
            create_query = """
            CREATE (m:Mechanic {
                id: $id,
                name: $name,
                email: $email,
                hashed_password: $hashed_password,
                role: $role,
                reliability_score: $reliability_score,
                experience_years: $experience_years,
                properties_json: $properties_json,
                created_at: $created_at,
                updated_at: $updated_at,
                created_by: $created_by
            })
            RETURN m.id as id
            """

            create_result = await session.run(
                create_query,
                {
                    "id": mechanic_id,
                    "name": mechanic_data["name"],
                    "email": mechanic_data["email"],
                    "hashed_password": hashed_password,
                    "role": mechanic_data["role"],
                    "reliability_score": mechanic_data["reliability_score"],
                    "experience_years": mechanic_data["experience_years"],
                    "properties_json": properties_json,
                    "created_at": datetime.now(UTC).isoformat(),
                    "updated_at": datetime.now(UTC).isoformat(),
                    "created_by": "system",
                },
            )

            record = await create_result.single()
            if record:
                logger.info(
                    f"✅ Created mechanic: {mechanic_data['name']} ({mechanic_data['email']})"
                )
                stats["created"] += 1
            else:
                logger.error(f"❌ Failed to create mechanic: {mechanic_data['email']}")
                stats["errors"] += 1

        except Exception as e:
            logger.error(
                f"❌ Error creating mechanic {mechanic_data['email']}: {str(e)}"
            )
            stats["errors"] += 1

    logger.info(
        f"Mechanics seeding complete. Created: {stats['created']}, Skipped: {stats['skipped']}, Errors: {stats['errors']}"
    )

    # Print login credentials if any were created
    if stats["created"] > 0:
        logger.info("👥 Default login credentials created:")
        for mechanic in mechanics_data:
            if any(m for m in mechanics_data if m["email"] == mechanic["email"]):
                logger.info(
                    f"   {mechanic['role']}: {mechanic['email']} / {mechanic['password']}"
                )

    return stats


async def initialize_mechanics(session: AsyncSession) -> None:
    """Initialize mechanics in the database if they don't exist."""
    try:
        logger.info("Checking for existing mechanics...")

        # Create constraints and indexes for mechanics
        await create_mechanic_constraints(session)

        # Check if mechanics exist
        mechanics_exist = await check_mechanics_exist(session)

        if not mechanics_exist:
            logger.info("No mechanics found, creating default mechanics...")
            await seed_mechanics(session)
        else:
            logger.info("Mechanics already exist in database")

    except Exception as e:
        logger.error(f"Error initializing mechanics: {str(e)}")
        # Don't fail the entire initialization if mechanics fail
        logger.info("Continuing with knowledge data initialization...")


# Update your existing main() function to include mechanics initialization:
async def main() -> None:
    """Main function to run the initialization script."""
    # Connect to Neo4j
    logger.info(f"Connecting to Neo4j at {NEO4J_URI}")
    driver = None

    try:
        driver = AsyncGraphDatabase.driver(
            NEO4J_URI, auth=(NEO4J_USERNAME, NEO4J_PASSWORD)
        )

        # Test connection
        await driver.verify_connectivity()
        logger.info(f"Connected to Neo4j at {NEO4J_URI}")

        # Initialize embedding service if API key is provided
        embedding_service = None
        if os.getenv("OPENAI_API_KEY"):
            from app.services.embeddings.embedding import EmbeddingService
            from app.core.config import get_settings
            from app.services.cache.embedding_cache import EmbeddingCache

            settings = get_settings()
            embedding_cache = EmbeddingCache.get_instance()
            embedding_service = EmbeddingService(
                settings=settings, embedding_cache=embedding_cache
            )
            logger.info("Initialized embedding service for generating embeddings")
        else:
            logger.warning("No OpenAI API key found, skipping embedding generation")

        async with driver.session() as session:
            # Initialize mechanics FIRST (before knowledge data)
            await initialize_mechanics(session)

            # Create constraints and indexes for knowledge data
            await create_constraints(session)

            # Seed knowledge data from JSON with embedding service
            await seed_from_json(session, embedding_service)

            logger.info("Database initialization complete")

    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise
    finally:
        # Close the driver
        if driver:
            await driver.close()


if __name__ == "__main__":
    # Import path fix for running as script
    import sys
    import os

    # Add project directory to path
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # Run the script
    asyncio.run(main())
