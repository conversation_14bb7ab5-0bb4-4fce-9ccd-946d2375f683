# replaces singular error codes with multiple possible combinations

import json
import random

possible_code = [
    "F",
    "E",
    "EF",
    "D",
    "DF",
    "DE",
    "DEF",
    "C",
    "CF",
    "CE",
    "CEF",
    "CD",
    "CDF",
    "CDE",
    "CDEF",
    "B",
    "BF",
    "BE",
    "BEF",
    "BD",
    "BDF",
    "BDE",
    "BDEF",
    "BCE",
    "BCF",
    "BCE",
    "BCEF",
    "BCD",
    "BCDF",
    "BCDE",
    "BCDEF",
    "A",
    "AF",
    "AE",
    "AEF",
    "AD",
    "ADF",
    "ADE",
    "ADEF",
    "AC",
    "ACF",
    "ACE",
    "ACEF",
    "ACD",
    "ACDF",
    "ACDE",
    "ACDEF",
    "AB",
    "ABF",
    "ABE",
    "ABEF",
    "ABD",
    "ABDF",
    "ABDE",
    "ABDEF",
    "ABC",
    "ABCF",
    "ABCE",
    "ABCEF",
    "ABCD",
    "ABCDF",
    "ABCDE",
    "ABCDEF",
]


def get_num_flashes():
    """
    Returns a number between 1 and 4 with custom weighted probabilities:
        - 2 is most likely
        - 1 and 3 are moderately likely
        - 4 is least likely
    """
    choices = [1, 2, 3, 4]
    weights = [0.2, 0.5, 0.2, 0.1]
    return random.choices(choices, weights=weights, k=1)[0]


def generate_random_error_code():
    num_flashes = get_num_flashes()
    error_codes = random.sample(possible_code, k=num_flashes)
    return error_codes


if __name__ == "__main__":
    file_path = "server/scripts/sample_data/synthetic_dataset_v2.json"
    export_path = "server/scripts/sample_data/synthetic_dataset_v3.json"

    dataset = json.load(open(file_path, "r"))
    for entry in dataset["nodes"]:
        if entry["type"] == "O":
            if entry["observation"] and entry["observation"]["error_codes"]:
                print(f"Original error code: {entry['observation']['error_codes']}")
                new_error_codes = generate_random_error_code()
                print(f"New error codes: {new_error_codes}")
                entry["observation"]["error_codes"] = new_error_codes

    with open(export_path, "w") as f:
        json.dump(dataset, f, indent=4)
