import openai
import numpy as np
import pandas as pd
import json
import os
from sklearn.metrics.pairwise import cosine_similarity
from dotenv import load_dotenv

load_dotenv()

# ——— CONFIG ———
openai.api_key = os.getenv("OPENAI_API_KEY")
print(f"OpenAI API key: {openai.api_key}")
EMBED_MODEL = "text-embedding-3-large"
CHAT_MODEL = "gpt-4.1"
DATA_FILE = "server/scripts/sample_data/deduplication_dataset.json"
OUTPUT_CSV = "similarity_results.csv"


# ——— HELPERS ———
def get_embedding(text, model=EMBED_MODEL):
    try:
        resp = openai.embeddings.create(input=text, model=model)
        return resp.data[0].embedding
    except Exception as e:
        print(f"[Embedding ERROR] {e}")
        return None


def calc_similarity(e1, e2):
    if e1 is None or e2 is None:
        return None
    v1, v2 = np.array(e1).reshape(1, -1), np.array(e2).reshape(1, -1)
    return float(cosine_similarity(v1, v2)[0][0])


def ask_llm_to_merge(sent1, sent2, modality):
    # possibly, we want to add modality specific correct/incorrect examples (few shot learning)
    prompt = f"""
You are given two short diagnostic sentences in the *{modality}* modality.
Decide whether they describe the *same* underlying issue (i.e. should be merged).

Sentence A: "{sent1}"
Sentence B: "{sent2}"


Reply with exactly "true" if they refer to the same issue, or "false" otherwise.
    """.strip()
    openai_client = openai.OpenAI(api_key=openai.api_key)
    resp = openai_client.chat.completions.create(
        model=CHAT_MODEL,
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt},
        ],
        temperature=0,
    )
    answer = resp.choices[0].message.content.strip().lower()
    return True if answer.startswith("true") else False


# ——— LOAD DATA ———
with open(DATA_FILE, "r") as f:
    data = json.load(f)

# ——— PROCESS ALL PAIRS ———
rows = []
for modality, pairs in data.items():
    for p in pairs:
        sid = p["id"]
        s1, s2 = p["sent1"], p["sent2"]
        gold = bool(p["merge"])
        emb1 = get_embedding(s1)
        emb2 = get_embedding(s2)
        sim = calc_similarity(emb1, emb2)
        llm_dec = ask_llm_to_merge(s1, s2, modality)
        correct = llm_dec == gold
        difficulty = p["difficulty"]

        rows.append(
            {
                "id": sid,
                "modality": modality,
                "sent1": s1,
                "sent2": s2,
                "difficulty": difficulty,
                "gold_merge": gold,
                "llm_merge": llm_dec,
                "cosine_similarity": sim,
                "correct": correct,
            }
        )
        print(
            f"[{sid}] sim={sim:.3f} • difficulty={difficulty} • gold={gold} • llm={llm_dec} • {'✅' if correct else '❌'}"
        )

# ——— SAVE & SUMMARY ———
df = pd.DataFrame(rows)
df.to_csv(OUTPUT_CSV, index=False)

total = len(df)
corrects = df["correct"].sum()
accuracy = corrects / total

print("\n=== RESULTS PER MODALITY PER DIFFICULTY ===")

# split per modality and per difficulty
for modality in df["modality"].unique():
    for difficulty in df["difficulty"].unique():
        subset = df[(df["modality"] == modality) & (df["difficulty"] == difficulty)]
        total = len(subset)
        corrects = subset["correct"].sum()
        accuracy = corrects / total
        print(
            f"[{modality}] - [{difficulty}] Total pairs: {total}, Correctly decided: {corrects}, Accuracy: {accuracy:.2%}"
        )

print("\n=== RESULTS OVERALL ===")
print(f"Total pairs       : {total}")
print(f"Correctly decided : {corrects}")
print(f"Incorrectly decided: {total - corrects}")
print(f"Accuracy          : {accuracy:.2%}")
print(f"\nSaved detailed results to: {OUTPUT_CSV}")
