id,name,visual_observation,auditory_observation,positional_observation,error_code,related_parts,door_model,custom_query,comment
N1103,De deur blijft volledig stil staan en beweegt niet bij activering.,De deur blijft volledig stil staan en beweegt niet bij activering.,"Geen geluiden hoorbaar bij poging tot openen, volledige stilte.",,ABEF,"[""Motor"", ""Besturingsmodule""]",Door-N971,De deur draait niet met foutcode ABEF. Kan vermoedelijk komen door motor of besturingsmodule,"Auditory observation means ""nothing"" but it still says something, so how does it deal when the input query does not contain auditory cues?"
N754,De deur hapert bij het draaien en stopt onverwachts.,De deur hapert bij het draaien en stopt onverwachts.,,,BEF,[],Door-N702,De deur draait hapert een beetje en stopt midden tijdens het roteren,"Not a one to one translation of visual observation, also missing error code"
N1150,De deur draait niet open bij het activeren van de sensor.,De deur draait niet open bij het activeren van de sensor.,,,AC,"[""Sensorunit"", ""Besturingsmodule""]",Door-N971,Error code AC,Just error code reporting while leaving out other details
N584,De draaideur staat stil en beweegt niet automatisch bij inschakeling.,De draaideur staat stil en beweegt niet automatisch bij inschakeling.,,,,[],Door-N464,Deur draait niet,Very ambiguous and open query which is less detailed than the visual observation but practically mean the same. Will it detect this one or is it too vague?
N1,De deur blijft hangen en blokkeert halverwege het openen.,De deur blijft hangen en blokkeert halverwege het openen.,,,F,[],Door-N0,Deur draait maar tot de helft. Ik lees hier foutcode F.,Slightly different wording of visual observation but they mean the same.
N334,De deur beweegt niet wanneer deze geactiveerd wordt.,De deur beweegt niet wanneer deze geactiveerd wordt.,Er is geen geluid te horen bij poging tot beweging.,,CD,[],Door-N305,De sensor werkt maar deur beweegt niet. Er zijn geen geluiden te horen verder,"1) Added word sensor to observation, will this mess up the detection?. 2) Explicit mention of no auditory observation, will it add this to the observation node? Observatio node itself has a value but is a negative indication of auditory presence. 3) Omitted error code."
N1619,De deur hapert en stopt onregelmatig tijdens het draaien.,De deur hapert en stopt onregelmatig tijdens het draaien.,,Bij de flatscan sensor aan de onderkant van de deur.,ACF,"[""Flatscan sensor"", ""Motor"", ""Deurframe""]",Door-N1598,De deur stopt een aantal keer tijdens het roteren. Ik vermoed dat het probleem zit bij de flatscan sensor en zie foutcode ACF.,"1) Slightly different wording of visual observation, added an explicit positional observation, added error code, did not mention all related parts."
