# GPT-Reformulated Query Test Report

## Executive Summary

**Test Date:** 2025-05-22 09:43:21

**Results Overview:**

| Metric | Value |
|--------|-------|
| Total Tests | 1 |
| Successful Tests | 0 |
| Success Rate | 0.0% |

---

## Test Configuration

- **API Endpoint:** http://localhost:8000/api/knowledge/query
- **GPT Model:** gpt-4.1-mini
- **Default Door Model:** Door-N1441
- **Sample Size:** 1

---

## Observation Type Analysis

### Distribution of Observation Types

| Observation Type | Count | Percentage |
|------------------|-------|------------|
| Visual | 1 | 100.0% |
| auditory | 0 | 0.0% |
| Positional | 0 | 0.0% |
| Error_code | 1 | 100.0% |
| Related_parts | 1 | 100.0% |

### Success Rates by Observation Type

| Observation Type | Success Rate | Successful Tests | Total Tests |
|------------------|--------------|------------------|-------------|
| Visual | 0.0% | 0 | 1 |
| auditory | N/A | 0 | 0 |
| Positional | N/A | 0 | 0 |
| Error_code | 0.0% | 0 | 1 |
| Related_parts | 0.0% | 0 | 1 |

### Success Rates by Number of Observation Types

| Observation Types | Success Rate | Successful Tests | Total Tests |
|-------------------|--------------|------------------|-------------|
| 3 | 0.0% | 0 | 1 |

---

### Success Rate by Door Model

| Door Model | Success Rate | Successful Tests | Total Tests |
|------------|--------------|------------------|-------------|
| None | 0.0% | 0 | 1 |

---

## Failed Tests

### Failed Test #1: Node ID N287

**Door Model:** None

**Original Node Name:** Het bedieningspaneel toont geen enkele reactie bij aanraking.

**Observation Details:**

🔍 **Visual Observation:**
```
Het bedieningspaneel toont geen enkele reactie bij aanraking.
```

⚠️ **Error Code:** `CF`

🔧 **Related Parts:** Bedieningspaneel, Besturingsmodule

**Error:** Failed to generate query

---

## Conclusion

The GPT query reformulation shows **poor performance**. Significant improvements are required.

The 'visual' observation type showed the highest success rate at **0.0%**.

### Recommendations

1. **Improve GPT Prompt Engineering**: Refine the prompt to better capture the essence of various observation types.
2. **Enhance Vector Search**: Review vector search implementation to improve retrieval accuracy.

### Test Generated: 2025-05-22 09:43:21
