# Revolving Door Knowledge System - Backend Service

## Table of Contents
- [Revolving Door Knowledge System - Backend Service](#revolving-door-knowledge-system---backend-service)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
    - [Key Features](#key-features)
    - [Technology Stack](#technology-stack)
  - [System Architecture](#system-architecture)
    - [High-Level Architecture](#high-level-architecture)
    - [Component Interaction Flow](#component-interaction-flow)
  - [Knowledge Processing Pipeline](#knowledge-processing-pipeline)
    - [Query Processing Pipeline](#query-processing-pipeline)
      - [1. Entity Extraction Stage](#1-entity-extraction-stage)
      - [2. Knowledge Path Search Stage](#2-knowledge-path-search-stage)
      - [3. Path Ranking Algorithm](#3-path-ranking-algorithm)
    - [Knowledge Extraction Pipeline](#knowledge-extraction-pipeline)
      - [1. Initial Validation](#1-initial-validation)
      - [2. LLM-Based Extraction](#2-llm-based-extraction)
      - [3. Knowledge Graph Construction](#3-knowledge-graph-construction)
  - [Database Architecture](#database-architecture)
    - [Graph Model](#graph-model)
      - [Node Types](#node-types)
      - [Relationship Types](#relationship-types)
  - [API Reference](#api-reference)
    - [Authentication Endpoints](#authentication-endpoints)
      - [`POST /auth/token`](#post-authtoken)
    - [Knowledge Management Endpoints](#knowledge-management-endpoints)
      - [`POST /api/knowledge/extract`](#post-apiknowledgeextract)
  - [Services Implementation](#services-implementation)
    - [1. Speech Service (`services/speech/`)](#1-speech-service-servicesspeech)
      - [Transcription Service](#transcription-service)
    - [2. NLP Service (`services/nlp/`)](#2-nlp-service-servicesnlp)
      - [Query Processing Pipeline](#query-processing-pipeline-1)
      - [Knowledge Extraction Pipeline](#knowledge-extraction-pipeline-1)
    - [3. Database Service (`services/database/`)](#3-database-service-servicesdatabase)
      - [Neo4j Repository Pattern](#neo4j-repository-pattern)

## Overview

The Revolving Door Knowledge System is an advanced backend service designed to manage and process knowledge about revolving door mechanics, maintenance, and repairs. The system employs a sophisticated graph database architecture combined with natural language processing and machine learning capabilities to provide intelligent responses to maintenance queries.

### Key Features
- Bilingual support (English/Dutch)
- Knowledge extraction from natural language
- Graph-based knowledge representation
- Semantic search capabilities
- Speech-to-text processing
- Multi-factor path ranking
- Context-aware query processing

### Technology Stack
- **Framework**: FastAPI
- **Database**: Neo4j
- **AI/ML**: OpenAI GPT-4.1-mini
- **Authentication**: JWT
- **Testing**: pytest
- **Documentation**: OpenAPI/Swagger

## System Architecture

### High-Level Architecture
```plaintext
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Client Apps   │ ──► │  FastAPI Server │ ──► │  Neo4j Graph DB │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                              │
                              ▼
                     ┌─────────────────┐
                     │   OpenAI API    │
                     └─────────────────┘
```

### Component Interaction Flow
1. Client request received by FastAPI server
2. Authentication & validation
3. Query processing & knowledge extraction
4. Graph database operations
5. Response generation & ranking
6. Client response

## Knowledge Processing Pipeline

### Query Processing Pipeline

The query processing pipeline is implemented in `QueryProcessingService` and consists of several sophisticated stages:

#### 1. Entity Extraction Stage
```python
async def _extract_entities_and_intent(self, query_text: str, context_info: Optional[Dict[str, Any]] = None)
```

- **Input Processing**
  - Text normalization
  - Language detection (Dutch/English)
  - Context incorporation

- **Entity Types Extracted**
  ```python
  {
      "query_intent": str,  # symptom_lookup, problem_diagnosis, cause_identification, solution_finding, general_info
      "entities": {
          "symptom": str,
          "problem": str,
          "cause": str,
          "door_model": str,
          "door_part": str,
          "environment": str
      },
      "language": str  # "nl" or "en"
  }
  ```

- **Fallback Mechanisms**
  1. Primary GPT-4.1 extraction
  2. Fallback GPT extraction with simplified prompt
  3. Pattern-based extraction as last resort

#### 2. Knowledge Path Search Stage
```python
async def _find_knowledge_paths(self, extracted_entities: Dict[str, Any], door_type_id: Optional[str] = None)
```

- **Search Strategies**
  1. Direct match by symptom
  2. Semantic similarity search
  3. Problem-based search
  4. Cause-based search
  5. General entity search

- **Path Finding Algorithm**
  ```plaintext
  1. Start with matched node (symptom/problem/cause)
  2. Traverse relationships bidirectionally
  3. Collect all possible paths to solutions
  4. Filter by relevance and completeness
  ``

### Knowledge Extraction Pipeline

The knowledge extraction pipeline is implemented in `KnowledgeExtractionService` and processes maintenance reports into structured knowledge:

#### 1. Initial Validation
```python
async def extract_knowledge(self, text: str, mechanic_id: Optional[str] = None)
```

- **Prerequisites Check**
  - Door model presence
  - Minimum content requirements
  - Mechanic credentials

#### 2. LLM-Based Extraction
```python
async def _llm_extract_knowledge(self, text: str) -> Dict[str, Any]
```

- **SPCS Framework Processing**
  ```python
  {
      "door_model": str,
      "environment": {
          "condition": str,
          "description": str
      },
      "knowledge_paths": [
          {
              "symptom": {"name": str, "description": str},
              "problem": {"name": str, "description": str},
              "cause": {"name": str, "description": str},
              "solution": {
                  "name": str,
                  "description": str,
                  "parts_involved": List[str],
                  "part_numbers": List[str]
              }
          }
      ],
      "tacit_knowledge": [
          {
              "type": "TECHNIQUE|SHORTCUT|OBSERVATION",
              "name": str,
              "description": str
          }
      ]
  }
  ```

#### 3. Knowledge Graph Construction
```python
async def _build_knowledge_graph(self, extracted_data: Dict[str, Any], mechanic_id: str)
```

- **Node Creation**
  - UUID generation
  - Timestamp assignment
  - Property validation

- **Relationship Establishment**
  - Bidirectional linking
  - Trust scoring
  - Temporal tracking

## Database Architecture

### Graph Model

#### Node Types
1. **SYMPTOM**
   ```python
   {
       "id": UUID,
       "name": str,
       "description": str,
       "created_at": datetime,
       "updated_at": datetime,
       "created_by": UUID
   }
   ```

2. **PROBLEM**
   ```python
   {
       "id": UUID,
       "name": str,
       "description": str,
       "severity": int,
       "created_at": datetime
   }
   ```

3. **CAUSE**
   ```python
   {
       "id": UUID,
       "name": str,
       "description": str,
       "frequency": float
   }
   ```

4. **SOLUTION**
   ```python
   {
       "id": UUID,
       "name": str,
       "description": str,
       "parts_involved": List[str],
       "estimated_time": int,
       "success_rate": float
   }
   ```

#### Relationship Types
1. **LEADS_TO**
   - Symptom → Problem
   - Problem → Cause
   - Weight: 0.0 - 1.0

2. **RESOLVED_BY**
   - Problem → Solution
   - Cause → Solution
   - Properties:
     - success_count: int
     - last_used: datetime

3. **APPLIES_TO**
   - Solution → DoorType
   - Properties:
     - trust: float

## API Reference

### Authentication Endpoints

#### `POST /auth/token`
- **Purpose**: Obtain JWT access token
- **Input**:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **Response**:
  ```json
  {
    "access_token": "string",
    "token_type": "bearer"
  }
  ```
- **Error Handling**:
  - 401: Invalid credentials
  - 422: Validation error

### Knowledge Management Endpoints

#### `POST /api/knowledge/extract`
- **Purpose**: Extract knowledge from maintenance text
- **Security**: Requires JWT token
- **Input**:
  ```json
  {
    "text": "string",
    "mechanic_id": "UUID",
    "door_type_id": "UUID",
    "trust_threshold": 0.7
  }
  ```
- **Response**:
  ```json
  {
    "nodes": [
      {
        "id": "UUID",
        "type": "SYMPTOM|PROBLEM|CAUSE|SOLUTION",
        "name": "string",
        "description": "string",
        "properties": {}
      }
    ],
    "relationships": [
      {
        "source_id": "UUID",
        "target_id": "UUID",
        "type": "LEADS_TO|RESOLVED_BY|APPLIES_TO",
        "properties": {}
      }
    ]
  }
  ```
- **Error Codes**:
  - 400: Invalid input
  - 401: Unauthorized
  - 422: Validation error
  - 500: Extraction error



## Services Implementation

### 1. Speech Service (`services/speech/`)

#### Transcription Service
```python
class TranscriptionService:
    async def transcribe_audio(self, audio_file: UploadFile) -> Dict[str, Any]:
        """
        Transcribes audio using OpenAI Transcription API

        Implementation:
        1. Audio validation & preprocessing
        2. Chunking for long audio
        3. Parallel processing of chunks
        4. Result aggregation
        """
```

- **Features**:
  - Multi-language support
  - Noise reduction
  - Speaker diarization
  - Trust scoring

- **Performance Optimization**:
  - Streaming for large files
  - Caching of frequent transcriptions
  - Batch processing capability

### 2. NLP Service (`services/nlp/`)

#### Query Processing Pipeline
```python
class QueryProcessor:
    def __init__(self):
        self.embedding_service = EmbeddingService()
        self.knowledge_repo = KnowledgeRepository()

    async def process_query(self, query: str) -> List[KnowledgePath]:
        """
        Multi-stage query processing pipeline

        Stages:
        1. Entity extraction
        2. Intent classification
        3. Knowledge path search
        4. Path ranking
        5. Answer generation
        """
```

#### Knowledge Extraction Pipeline
```python
class KnowledgeExtractor:
    async def extract(self, text: str) -> Tuple[List[Node], List[Relationship]]:
        """
        Knowledge extraction pipeline

        Steps:
        1. Text preprocessing
        2. Entity recognition
        3. Relationship extraction
        4. Trust scoring
        5. Graph construction
        """
```

### 3. Database Service (`services/database/`)

#### Neo4j Repository Pattern
```python
class Neo4jRepository:
    async def create_node(self, node: Node) -> str:
        """
        Node creation with automatic indexing
        """

    async def create_relationship(self, relationship: Relationship) -> None:
        """
        Relationship creation with property validation
        """
```
