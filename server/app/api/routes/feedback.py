from typing import List, Dict, Any
import logging
import json
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status

from app.models.schemas.mechanic import MechanicInDB
from app.models.schemas.feedback import (
    RelationshipFeedbackRequest,
    RelationshipFeedbackResponse,
    FeedbackEntry,
)
from app.core.dependencies import get_current_mechanic
from app.services.analytics.scoring import KnowledgeScoringService
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)
from app.core.exceptions import DatabaseError, database_operation_exception

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post(
    "/relationship-feedback", response_model=RelationshipFeedbackResponse
)
async def add_relationship_feedback(
    request: RelationshipFeedbackRequest,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    knowledge_repo: KnowledgeRepository = Depends(),
    scoring_service: KnowledgeScoringService = Depends(),
):
    """
    Add feedback to RESOLVED_BY relationships.
    Stores feedback as entries in the relationship's feedback_entries list.
    """
    try:
        logger.info(f"=== ADDING RELATIONSHIP FEEDBACK ===")
        logger.info(
            f"Mechanic: {current_mechanic.name} ({current_mechanic.id})"
        )
        logger.info(f"Feedback Type: {request.feedback_type}")
        logger.info(f"Relationships to update: {len(request.relationships)}")

        # Create feedback entry
        feedback_entry = FeedbackEntry(
            type=request.feedback_type,
            timestamp=datetime.utcnow(),
            mechanic_id=current_mechanic.id,
        )

        updated_relationships = []

        # Update each RESOLVED_BY relationship
        for rel_data in request.relationships:
            source_id = rel_data["source_id"]
            target_id = rel_data["target_id"]

            logger.info(
                f"Adding feedback to relationship: {source_id} -> {target_id}"
            )

            # Get current relationship
            relationship = await knowledge_repo.get_knowledge_relationship(
                source_id, target_id, "RESOLVED_BY"
            )

            if not relationship:
                logger.warning(
                    f"RESOLVED_BY relationship not found: {source_id} -> {target_id}"
                )
                continue

            # Get current feedback entries
            current_feedback = relationship.get("feedback_entries", [])

            # Handle JSON string case (from database)
            if isinstance(current_feedback, str):
                try:
                    current_feedback = json.loads(current_feedback)
                except (json.JSONDecodeError, TypeError):
                    current_feedback = []

            # Add new feedback entry
            current_feedback.append(feedback_entry.dict())

            # Calculate new trust score using the scoring service
            old_trust_score = relationship.get("trust_score", 0.5)
            new_trust_score = scoring_service.update_trust(
                trust_score=old_trust_score, feedback_entries=current_feedback
            )

            # Update the relationship
            success = await knowledge_repo.update_knowledge_relationship(
                source_id,
                target_id,
                "RESOLVED_BY",
                {
                    "feedback_entries": current_feedback,
                    "trust_score": new_trust_score,
                },
            )

            if success:
                updated_relationships.append(
                    {
                        "source_id": source_id,
                        "target_id": target_id,
                        "feedback_type": request.feedback_type,
                        "feedback_count": len(current_feedback),
                        "old_trust_score": old_trust_score,
                        "new_trust_score": new_trust_score,
                        "timestamp": feedback_entry.timestamp.isoformat(),
                    }
                )

                logger.info(
                    f"Feedback added successfully. Total feedback entries: {len(current_feedback)}"
                )
                logger.info(
                    f"Trust score updated: {old_trust_score:.3f} -> {new_trust_score:.3f}"
                )
            else:
                logger.error(
                    f"Failed to update relationship: {source_id} -> {target_id}"
                )

        logger.info(
            f"Successfully updated {len(updated_relationships)} relationships"
        )
        logger.info("=== FEEDBACK OPERATION COMPLETE ===")

        return RelationshipFeedbackResponse(
            message=f"Feedback added to {len(updated_relationships)} RESOLVED_BY relationships",
            updated_relationships=updated_relationships,
        )

    except DatabaseError as e:
        logger.error(f"Database error while adding feedback: {str(e)}")
        raise database_operation_exception()
    except Exception as e:
        logger.error(f"Error adding relationship feedback: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to add relationship feedback"
        )


@router.get("/relationship-feedback/{source_id}/{target_id}")
async def get_relationship_feedback(
    source_id: str,
    target_id: str,
    knowledge_repo: KnowledgeRepository = Depends(),
):
    """
    Get feedback entries for a specific RESOLVED_BY relationship.
    """
    try:
        relationship = await knowledge_repo.get_knowledge_relationship(
            source_id, target_id, "RESOLVED_BY"
        )

        if not relationship:
            raise HTTPException(
                status_code=404, detail="Relationship not found"
            )

        feedback_entries = relationship.get("feedback_entries", [])

        # Handle JSON string case
        if isinstance(feedback_entries, str):
            try:
                feedback_entries = json.loads(feedback_entries)
            except (json.JSONDecodeError, TypeError):
                feedback_entries = []

        # Calculate feedback statistics
        positive_count = len(
            [f for f in feedback_entries if f.get("type") == "positive"]
        )
        negative_count = len(
            [f for f in feedback_entries if f.get("type") == "negative"]
        )

        return {
            "source_id": source_id,
            "target_id": target_id,
            "trust_score": relationship.get("trust_score", 0.5),
            "feedback_entries": feedback_entries,
            "total_feedback": len(feedback_entries),
            "positive_feedback": positive_count,
            "negative_feedback": negative_count,
            "positive_ratio": (
                positive_count / len(feedback_entries)
                if feedback_entries
                else 0
            ),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting relationship feedback: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to get relationship feedback"
        )


@router.post("/path-success", status_code=status.HTTP_200_OK)
async def record_path_success(
    resolved_by_relationships: List[Dict[str, str]],
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    knowledge_repo: KnowledgeRepository = Depends(),
    scoring_service: KnowledgeScoringService = Depends(),
):
    """
    Record successful path by adding positive feedback to RESOLVED_BY relationships.
    Simplified version of the old success endpoint.

    Body should contain:
    {
        "resolved_by_relationships": [
            {"source_id": "cause_123", "target_id": "solution_456"}
        ]
    }
    """
    try:
        logger.info(
            f"Recording path success for mechanic: {current_mechanic.name}"
        )
        logger.info(
            f"RESOLVED_BY relationships to update: {len(resolved_by_relationships)}"
        )

        # Create the feedback request
        feedback_request = RelationshipFeedbackRequest(
            relationships=resolved_by_relationships, feedback_type="positive"
        )

        # Reuse the main feedback endpoint logic
        response = await add_relationship_feedback(
            request=feedback_request,
            current_mechanic=current_mechanic,
            knowledge_repo=knowledge_repo,
            scoring_service=scoring_service,
        )

        logger.info("=== PATH SUCCESS RECORDED ===")
        return {
            "message": "Path success recorded",
            "relationships_updated": len(response.updated_relationships),
            "details": response.updated_relationships,
        }

    except Exception as e:
        logger.error(f"Error recording path success: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to record path success"
        )


@router.post("/path-failure", status_code=status.HTTP_200_OK)
async def record_path_failure(
    resolved_by_relationships: List[Dict[str, str]],
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    knowledge_repo: KnowledgeRepository = Depends(),
    scoring_service: KnowledgeScoringService = Depends(),
):
    """
    Record failed path by adding negative feedback to RESOLVED_BY relationships.
    Simplified version of the old failure endpoint.

    Body should contain:
    {
        "resolved_by_relationships": [
            {"source_id": "cause_123", "target_id": "solution_456"}
        ]
    }
    """
    try:
        logger.info(
            f"Recording path failure for mechanic: {current_mechanic.name}"
        )
        logger.info(
            f"RESOLVED_BY relationships to update: {len(resolved_by_relationships)}"
        )

        # Create the feedback request
        feedback_request = RelationshipFeedbackRequest(
            relationships=resolved_by_relationships, feedback_type="negative"
        )

        # Reuse the main feedback endpoint logic
        response = await add_relationship_feedback(
            request=feedback_request,
            current_mechanic=current_mechanic,
            knowledge_repo=knowledge_repo,
            scoring_service=scoring_service,
        )

        logger.info("=== PATH FAILURE RECORDED ===")
        return {
            "message": "Path failure recorded",
            "relationships_updated": len(response.updated_relationships),
            "details": response.updated_relationships,
        }

    except Exception as e:
        logger.error(f"Error recording path failure: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Failed to record path failure"
        )


# ============================ DEPRECATED ENDPOINTS ============================
# These endpoints are kept for backward compatibility but should not be used
# =============================================================================


@router.post("/success", status_code=status.HTTP_200_OK)
async def record_successful_solution_deprecated(
    knowledge_path: Dict[str, Any],
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
):
    """
    DEPRECATED: Use /path-success instead.
    This endpoint is kept for backward compatibility only.
    """
    logger.warning(
        "DEPRECATED: /success endpoint used. Please migrate to /path-success"
    )
    logger.info(
        f"Legacy success feedback from mechanic: {current_mechanic.name}"
    )

    return {
        "message": "DEPRECATED: Use /path-success endpoint instead",
        "status": "logged_only",
        "recommended_endpoint": "/api/feedback/path-success",
    }


@router.post("/failure", status_code=status.HTTP_200_OK)
async def record_failed_solution_deprecated(
    knowledge_path: Dict[str, Any],
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
):
    """
    DEPRECATED: Use /path-failure instead.
    This endpoint is kept for backward compatibility only.
    """
    logger.warning(
        "DEPRECATED: /failure endpoint used. Please migrate to /path-failure"
    )
    logger.info(
        f"Legacy failure feedback from mechanic: {current_mechanic.name}"
    )

    return {
        "message": "DEPRECATED: Use /path-failure endpoint instead",
        "status": "logged_only",
        "recommended_endpoint": "/api/feedback/path-failure",
    }


@router.post("/custom", status_code=status.HTTP_200_OK)
async def submit_custom_feedback_deprecated(
    feedback_data: Dict[str, Any],
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
):
    """
    DEPRECATED: Custom feedback not supported in simplified system.
    This endpoint is kept for backward compatibility only.
    """
    logger.warning(
        "DEPRECATED: /custom endpoint used. Feature not supported in new system"
    )
    logger.info(f"Custom feedback from mechanic: {current_mechanic.name}")
    logger.info(f"Feedback data keys: {list(feedback_data.keys())}")

    return {
        "message": (
            "DEPRECATED: Custom feedback not supported in simplified system"
        ),
        "status": "logged_only",
        "note": "Use relationship-feedback endpoint instead",
    }


@router.post("/user-feedback", status_code=status.HTTP_200_OK)
async def record_user_feedback_deprecated(
    feedback: Dict[str, Any],
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
):
    """
    DEPRECATED: Use /relationship-feedback instead.
    This endpoint is kept for backward compatibility only.
    """
    logger.warning(
        "DEPRECATED: /user-feedback endpoint used. Please migrate to /relationship-feedback"
    )
    logger.info(f"Legacy user feedback from mechanic: {current_mechanic.name}")

    return {
        "message": "DEPRECATED: Use /relationship-feedback endpoint instead",
        "status": "logged_only",
        "recommended_endpoint": "/api/feedback/relationship-feedback",
    }


@router.delete("/user-feedback/{feedback_id}", status_code=status.HTTP_200_OK)
async def delete_user_feedback_deprecated(
    feedback_id: str,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
):
    """
    DEPRECATED: Feedback deletion not supported in simplified system.
    This endpoint is kept for backward compatibility only.
    """
    logger.warning(
        "DEPRECATED: DELETE /user-feedback endpoint used. Feature not supported"
    )
    logger.info(
        f"Mechanic {current_mechanic.name} attempted to delete feedback {feedback_id}"
    )

    return {
        "message": (
            "DEPRECATED: Feedback deletion not supported in simplified system"
        ),
        "status": "not_processed",
        "note": "Feedback is now stored permanently in relationships",
    }


@router.get("/user-feedback", status_code=status.HTTP_200_OK)
async def get_user_feedback_deprecated(
    path_id: str = None,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
):
    """
    DEPRECATED: Use /relationship-feedback/{source_id}/{target_id} instead.
    This endpoint is kept for backward compatibility only.
    """
    logger.warning(
        "DEPRECATED: GET /user-feedback endpoint used. Please migrate to /relationship-feedback"
    )
    logger.info(f"Mechanic {current_mechanic.name} requested legacy feedback")

    return {
        "message": (
            "DEPRECATED: Use /relationship-feedback/{source_id}/{target_id} endpoint instead"
        ),
        "status": "not_processed",
        "recommended_endpoint": (
            "/api/feedback/relationship-feedback/{source_id}/{target_id}"
        ),
    }
