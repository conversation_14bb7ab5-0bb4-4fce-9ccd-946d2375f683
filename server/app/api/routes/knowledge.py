import json
from typing import List, Dict, Any, Optional
import logging
import uuid
import tempfile
import os

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    status,
    UploadFile,
    File,
)
from fastapi.responses import JSONResponse

from app.models.schemas.knowledge import (
    KnowledgeNodeInDB,
    KnowledgeRelationshipInDB,
    KnowledgePathResponse,
    TranscriptionResponse,
    KnowledgeExtractionRequest,
    QueryRequest,
    QueryResponse,
    FirstKnowledgeExtractionResponse,
    QueryObservationRequest,
    FeedbackRequest,
    FeedbackResponse,
)

from app.models.schemas.mechanic import MechanicInDB
from app.core.dependencies import get_current_mechanic
from app.services.speech.transcription import TranscriptionService
from app.services.nlp.extraction.service import KnowledgeExtractionService
from app.services.nlp.query.query_service import QueryProcessingService
from app.core.exceptions import (
    KnowledgeExtractionError,
    TranscriptionError,
    QueryProcessingError,
    query_processing_exception,
)
from app.services.database.repositories.door_repo import DoorRepository
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)

from app.core.config import get_settings
from app.core.logging import log_api
from app.core.session_logging import (
    get_current_session_id,
    log_feedback_session,
    log_extraction_session,
    detect_and_handle_extraction_changes,
    log_query_session,
    save_audio,
)

logger = log_api()

router = APIRouter()

# Generate a session ID for the entire conversation
# session_id = generate_session_id()


DEFAULT_USER_ID = "3"  # Replace with actual default user ID if needed
settings = get_settings()


@router.post("/upload-audio", response_model=TranscriptionResponse)
async def upload_audio(
    audio_file: UploadFile = File(...),
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    transcription_service: TranscriptionService = Depends(),
):
    """
    Upload an audio file for transcription.
    Requires authentication.
    """
    try:
        # Enhanced diagnostic logging
        logger.info(f"Processing audio upload for mechanic: {current_mechanic.name}")
        logger.info(f"File content type: {audio_file.content_type}")
        logger.info(f"File name: {audio_file.filename}")

        # Read a small part first to verify it's not empty
        initial_chunk = await audio_file.read(1024)
        if not initial_chunk:
            logger.error("Uploaded file is empty (first chunk)")
            return TranscriptionResponse(
                text="No audio data was received. Please check your microphone and try recording again."
            )

        # Rewind the file pointer
        await audio_file.seek(0)

        # Now read the entire file
        audio_data = await audio_file.read()
        file_size = len(audio_data)
        logger.info(f"Received audio file size: {file_size} bytes")

        # Double-check for empty file
        if file_size == 0:
            logger.error("Uploaded file is empty after full read")
            return TranscriptionResponse(
                text="No audio data was received. Please check your microphone and try recording again."
            )

        # Analyze first few bytes for debugging (hexdump-like output)
        sample_size = min(file_size, 32)
        hex_bytes = " ".join([f"{b:02x}" for b in audio_data[:sample_size]])
        logger.info(f"First {sample_size} bytes: {hex_bytes}")

        # Encode to base64
        import base64

        encoded_audio = base64.b64encode(audio_data).decode("utf-8")
        logger.info(f"Audio encoded, size: {len(encoded_audio)} bytes")

        # Determine file format from content type or filename
        file_format = "mp3"  # Default to a well-supported format

        # Extract format from filename first if available
        if hasattr(audio_file, "filename") and audio_file.filename:
            ext = audio_file.filename.split(".")[-1].lower()
            if ext in [
                "flac",
                "m4a",
                "mp3",
                "mp4",
                "mpeg",
                "mpga",
                "oga",
                "ogg",
                "wav",
                "webm",
            ]:
                file_format = ext
                logger.info(f"Format determined from filename: {file_format}")

        # If can't determine from filename, try content type
        elif audio_file.content_type:
            # Extract format from content type (e.g., "audio/wav" -> "wav")
            content_parts = audio_file.content_type.split("/")
            if len(content_parts) >= 2:
                mime_format = content_parts[1].split(";")[0]
                if mime_format in [
                    "flac",
                    "m4a",
                    "mp3",
                    "mp4",
                    "mpeg",
                    "mpga",
                    "oga",
                    "ogg",
                    "wav",
                    "webm",
                ]:
                    file_format = mime_format
                    logger.info(f"Format determined from content type: {file_format}")

        logger.info(f"Final detected format: {file_format}")


        temp_dir = tempfile.gettempdir()
        debug_file_path = os.path.join(
            temp_dir, f"debug_audio_{current_mechanic.id}.{file_format}"
        )
        with open(debug_file_path, "wb") as debug_file:
            debug_file.write(audio_data)
        logger.info(f"Saved debug copy to {debug_file_path}")

        # Proceed with transcription
        logger.info("Calling transcription service...")
        text = await transcription_service.transcribe_audio(encoded_audio, file_format)

        
        if settings.SAVE_AUDIO:
            save_audio(audio_data, file_format)

        # Clean up debug file
        try:
            os.remove(debug_file_path)
        except:
            pass  # Don't worry if cleanup fails

        if not text or text.strip() == "":
            logger.warning("Transcription resulted in empty text")
            return TranscriptionResponse(
                text="I couldn't detect any speech in this recording. Please try speaking more clearly or check your microphone."
            )

        logger.info(f"Transcription successful: {text[:50]}...")
        return TranscriptionResponse(text=text)
    except TranscriptionError as e:
        logger.error(f"Transcription error: {str(e)}")
        return TranscriptionResponse(
            text="Sorry, I had trouble transcribing your audio. Please try again or check your microphone."
        )
    except Exception as e:
        logger.error(f"File processing error: {str(e)}")
        # Return a user-friendly error instead of raising an exception
        return TranscriptionResponse(
            text="There was a problem processing your audio. Please try again in a moment."
        )


@router.post("/extract", response_model=FirstKnowledgeExtractionResponse)
async def extract_knowledge(
    request: KnowledgeExtractionRequest,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    extraction_service: KnowledgeExtractionService = Depends(),
):
    """
    Extract knowledge from a mechanic's natural language description.
    Requires authentication.
    """

    try:
        # Validate input
        if not request.text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Text content is required",
            )
        
        session_id = get_current_session_id()
        logger.info(f"session id for extract: {session_id}")
        logger.info(
            f"Processing knowledge extraction for mechanic: {current_mechanic.name}"
        )
        logger.info(f"Service order number: {request.service_order_number}")
        
        
        log_extraction_session(
            session_id=session_id,
            transcription_text=request.text,
        )

        # Process extraction using the service
        response = await extraction_service.process_extraction(
            text=request.text,
            door_model=request.door_model,
            mechanic_id=current_mechanic.id, # TODO: change to name if we want to log by name (consider GDPR)
            service_order_number=request.service_order_number,
        )

        return response

    except KnowledgeExtractionError as e:
        logger.error(f"Knowledge extraction error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Unexpected error during knowledge extraction: {str(e)}")
        import traceback

        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}",
        )


@router.post("/evaluate")
async def store_extracted_knowledge(
    request: KnowledgeExtractionRequest,
    extraction_service: KnowledgeExtractionService = Depends(),
) -> bool:
    """
    Store extracted knowledge by building and saving the knowledge graph.
    """
    try:
        # Validate input
        if not request.text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Text content is required",
            )

        # Parse the raw extracted data JSON
        try:
            extracted_data = json.loads(request.text)
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid JSON in text field",
            )

        # Check if frontend made changes by comparing with original extraction
        session_id = get_current_session_id()

        # check if frontend made changes to the extraction (llm_output_paths)
        frontend_changes = detect_and_handle_extraction_changes(
            session_id=session_id,
            new_data=extracted_data,
            what_changed="llm_output_paths",
        )

        # Log the changes status
        log_extraction_session(
            session_id=session_id,
            frontend_extraction_changes=frontend_changes,
        )
        # Get door_type from door_model if provided
        door_type = None
        if request.door_model:
            logger.debug(f"Looking up door type for model {request.door_model}")
            door_type = await extraction_service.door_repo.get_door_type_by_model(
                request.door_model.replace("Boon Edam ", "")
            )

        # Get mechanic experience
        mechanic_experience = await extraction_service._get_experience_years(
            settings.DEFAULT_USER_ID
        )

        # Build and store the knowledge graph
        response = await extraction_service.start_build_knowledge_graph(
            extracted_data=extracted_data,
            mechanic_id=settings.DEFAULT_USER_ID,
            mechanic_experience=mechanic_experience,
            door_type=door_type,
            service_order_number=request.service_order_number,
        )

        return response

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error in evaluate endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Evaluation endpoint error: {str(e)}",
        )


@router.post("/query", response_model=QueryResponse)
async def process_query(
    request: QueryRequest,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    query_service: QueryProcessingService = Depends(),
    door_repo: DoorRepository = Depends(),
):
    """
    Detect knowledge need and process a natural language query.
    Requires authentication.
    """

    try:
        session_id = get_current_session_id()
        logger.info(f"session id for query: {session_id}")

        logger.info(f"Processing query for mechanic: {current_mechanic.name}")

        log_query_session(
            session_id=session_id,
            mechanic_id=current_mechanic.id,
            service_order_number=request.service_order_number,
            query_text=request.text,
            door_model=request.door_model,
            context_info=request.context_info,
        )
        
        

        # Get door_type_id from door_model if provided
        door_type_id = (
            request.door_model.replace("Boon Edam ", "") if request.door_model else None
        )


        # If knowledge is needed, process the query
        knowledge_paths, answer = await query_service.process_query(
            request.text, 
            door_type_id,
            request.context_info,
        )
        logger.info(
            f"API endpoint received {len(knowledge_paths)} paths from service"
        )
        
        log_query_session(
            session_id=session_id,
            paths_found=knowledge_paths,
            answer=answer,
        )

        # --- STEP 3: Build knowledge paths response ---
        # Convert to response model
        path_responses = []
        for path in knowledge_paths:
            node_responses = []
            for node in path.nodes:
                node_responses.append(
                    KnowledgeNodeInDB(
                        id=node.id,
                        type=node.type,
                        name=node.name,
                        description=node.description,
                        properties=node.properties,
                        created_at=node.created_at,
                        updated_at=node.updated_at,
                        created_by=node.created_by,
                    )
                )

            relationship_responses = []
            for relationship in path.relationships:
                relationship_responses.append(
                    KnowledgeRelationshipInDB(
                        source_id=relationship.source_id,
                        target_id=relationship.target_id,
                        type=relationship.type,
                        properties=relationship.properties,
                        trust_score=relationship.trust_score,
                        success_count=relationship.success_count,
                        created_at=relationship.created_at,
                        updated_at=relationship.updated_at,
                        created_by=relationship.created_by,
                        valid_from=relationship.valid_from,
                        valid_to=relationship.valid_to,
                        is_current=relationship.is_current,
                    )
                )

                # Generate deterministic path ID
                path_id = path.generate_path_id()

            path_responses.append(
                KnowledgePathResponse(
                    id=path_id,
                    nodes=node_responses,
                    relationships=relationship_responses,
                    trust_score=path.trust_score,
                )
            )

        logger.info(
            f"API endpoint returning {len(path_responses)} formatted path responses"
        )

        # --- STEP 4: Return BOTH outputs ---
        return QueryResponse(
            paths=path_responses,
            answer=answer,
        )

    except QueryProcessingError as e:
        logger.error(f"Query processing error: {str(e)}")
        raise query_processing_exception()


@router.post("/query-test", response_model=QueryResponse)
async def process_query_test(
    request: QueryRequest,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    query_service: QueryProcessingService = Depends(),
):
    try:
        logger.info(f"Processing query for mechanic: {current_mechanic.name}")

        # Get door_type_id from door_model if provided
        door_type_id = (
            request.door_model.replace("Boon Edam ", "") if request.door_model else None
        )
        # If knowledge is needed, process the query
        knowledge_paths, answer = await query_service.process_query(
            request.text,  # knowledge_check.get("question"), # TODO: check why we are passing llm output here
            door_type_id,
            request.context_info,
            answer_desired=False,
        )
        logger.info(f"API endpoint received {len(knowledge_paths)} paths from service")

        # --- STEP 3: Build knowledge paths response ---
        # Convert to response model
        path_responses = []
        for path in knowledge_paths:
            node_responses = []
            for node in path.nodes:
                node_responses.append(
                    KnowledgeNodeInDB(
                        id=node.id,
                        type=node.type,
                        name=node.name,
                        description=node.description,
                        properties=node.properties,
                        created_at=node.created_at,
                        updated_at=node.updated_at,
                        created_by=node.created_by,
                    )
                )

            relationship_responses = []
            for relationship in path.relationships:
                relationship_responses.append(
                    KnowledgeRelationshipInDB(
                        source_id=relationship.source_id,
                        target_id=relationship.target_id,
                        type=relationship.type,
                        properties=relationship.properties,
                        trust_score=relationship.trust_score,
                        success_count=relationship.success_count,
                        created_at=relationship.created_at,
                        updated_at=relationship.updated_at,
                        created_by=relationship.created_by,
                        valid_from=relationship.valid_from,
                        valid_to=relationship.valid_to,
                        is_current=relationship.is_current,
                    )
                )

                # Generate deterministic path ID
                path_id = path.generate_path_id()

            path_responses.append(
                KnowledgePathResponse(
                    id=path_id,
                    nodes=node_responses,
                    relationships=relationship_responses,
                    trust_score=path.trust_score,
                )
            )

        logger.info(
            f"API endpoint returning {len(path_responses)} formatted path responses"
        )

        # --- STEP 4: Return BOTH outputs ---
        return QueryResponse(paths=path_responses)

    except QueryProcessingError as e:
        logger.error(f"Query processing error: {str(e)}")
        raise query_processing_exception()


@router.post("/query-threshold-test", response_model=QueryResponse)
async def process_query_test(
    request: QueryObservationRequest,
    query_service: QueryProcessingService = Depends(),
):
    try:

        logger.info(f"Received request: {request}")

        # Get door_type_id from door_model if provided
        door_type_id = (
            request.door_model.replace("Boon Edam ", "") if request.door_model else None
        )
        # If knowledge is needed, process the query
        knowledge_paths, answer = await query_service.process_observation_query(
            request.model_dump(),
            door_type_id,
            request.context_info,
            answer_desired=False,
        )
        logger.info(f"API endpoint received {len(knowledge_paths)} paths from service")

        # --- STEP 3: Build knowledge paths response ---
        # Convert to response model
        path_responses = []
        for path in knowledge_paths:
            node_responses = []
            for node in path.nodes:
                node_responses.append(
                    KnowledgeNodeInDB(
                        id=node.id,
                        type=node.type,
                        name=node.name,
                        description=node.description,
                        properties=node.properties,
                        created_at=node.created_at,
                        updated_at=node.updated_at,
                        created_by=node.created_by,
                    )
                )

            relationship_responses = []
            for relationship in path.relationships:
                relationship_responses.append(
                    KnowledgeRelationshipInDB(
                        source_id=relationship.source_id,
                        target_id=relationship.target_id,
                        type=relationship.type,
                        properties=relationship.properties,
                        trust_score=relationship.trust_score,
                        success_count=relationship.success_count,
                        created_at=relationship.created_at,
                        updated_at=relationship.updated_at,
                        created_by=relationship.created_by,
                        valid_from=relationship.valid_from,
                        valid_to=relationship.valid_to,
                        is_current=relationship.is_current,
                    )
                )

                # Generate deterministic path ID
                path_id = path.generate_path_id()

            path_responses.append(
                KnowledgePathResponse(
                    id=path_id,
                    nodes=node_responses,
                    relationships=relationship_responses,
                    trust_score=path.trust_score,
                )
            )

        logger.info(
            f"API endpoint returning {len(path_responses)} formatted path responses"
        )

        # --- STEP 4: Return BOTH outputs ---
        return QueryResponse(paths=path_responses)

    except QueryProcessingError as e:
        logger.error(f"Query processing error: {str(e)}")
        raise query_processing_exception()


@router.get("/door-types", response_model=List[Dict[str, Any]])
async def get_door_types(
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    knowledge_repo: KnowledgeRepository = Depends(),
):
    """
    Get all door types for filtering.
    Requires authentication.
    """
    try:
        logger.info(f"Fetching door types for mechanic: {current_mechanic.name}")

        # Improved query that ensures we get all DOOR_TYPE nodes
        query = """
        MATCH (d:DOOR_TYPE)
        RETURN d.id as id, d.model as model, d.manufacturer as manufacturer
        ORDER BY d.manufacturer, d.model
        """

        result = await knowledge_repo.execute_read_query(query)

        door_types = []
        for record in result:
            # Combine manufacturer and model for display
            manufacturer = record.get("manufacturer", "")
            model = record.get("model", "")

            # Create a clean, combined display name
            if manufacturer and model:
                display_name = f"{manufacturer} {model}"
            elif model:
                display_name = model
            elif manufacturer:
                display_name = manufacturer
            else:
                display_name = "Unknown Model"

            door_type = {"id": record["id"], "model": display_name.strip()}
            door_types.append(door_type)

        logger.info(f"Retrieved {len(door_types)} door types")
        return door_types

    except Exception as e:
        logger.error(f"Error fetching door types: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch door types: {str(e)}",
        )


@router.post("/app-feedback", response_model=FeedbackResponse)
async def submit_feedback(
    feedback: FeedbackRequest,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
):
    """
    Submit feedback for a session about the general app experience.
    Requires authentication.

    Args:
        feedback: FeedbackRequest containing session_id, thumbs_up, optional comment
        current_mechanic: Automatically injected current mechanic from authentication

    Returns:
        FeedbackResponse indicating success/failure
    """
    try:
        logger.info(f"Processing feedback for mechanic: {current_mechanic.name}")

        session_id = get_current_session_id()
        log_feedback_session(
            session_id=session_id,
            thumbs_up=feedback.thumbs_up,
            comment=feedback.comment,
            mechanic_id=current_mechanic.id,  # Use the authenticated mechanic's ID
        )

        return FeedbackResponse(
            message=f"Feedback logged successfully for session {session_id}",
        )

    except Exception as e:
        logger.error(f"Error logging feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to log feedback: {str(e)}")
