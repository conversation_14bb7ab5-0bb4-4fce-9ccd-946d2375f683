from typing import List, Optional
import logging
from datetime import timed<PERSON><PERSON>

from fastapi import APIRouter, Depends, HTTPException, status, Header
from fastapi.security import OAuth2PasswordRequestForm


from app.models.schemas.mechanic import (
    MechanicCreate,
    MechanicUpdate,
    MechanicResponse,
    Token,
    EphemeralToken,
    MechanicInDB,
)
from app.core.dependencies import get_current_mechanic
from app.services.database.repositories.mechanic_repo import MechanicRepository
from app.core.security import create_access_token
from app.core.config import get_settings
from app.core.exceptions import (
    DatabaseError,
    entity_not_found_exception,
    database_operation_exception,
)
from app.services.database.neo4j import Neo4jService
from app.services.speech.realtime import RealtimeGPTService


from app.core.session_logging import set_session_id
from app.core.logging import log_api

logger = log_api()

router = APIRouter()


@router.post("/", response_model=MechanicResponse, status_code=status.HTTP_201_CREATED)
async def create_mechanic(
    mechanic: Mechan<PERSON><PERSON><PERSON>,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    mechanic_repo: MechanicRepository = Depends(),
):
    """
    Create a new mechanic.
    Only mechanics with admin role can create new mechanics.
    Requires authentication.
    """
    # Check if current mechanic has admin role
    from app.models.domain.mechanic import MechanicRole

    if current_mechanic.role != MechanicRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admin mechanics can create new mechanics",
        )

    try:
        # Check if email already exists
        existing_mechanic = await mechanic_repo.get_by_email(mechanic.email)
        if existing_mechanic:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered",
            )

        # Convert to domain model
        from app.models.domain.mechanic import Mechanic

        domain_mechanic = Mechanic(
            name=mechanic.name,
            email=mechanic.email,
            role=mechanic.role,
            experience_years=mechanic.experience_years,
            properties=mechanic.properties,
        )

        created_mechanic = await mechanic_repo.create_mechanic(
            domain_mechanic, mechanic.password
        )

        # Convert to response model
        return MechanicResponse(
            id=created_mechanic.id,
            name=created_mechanic.name,
            email=created_mechanic.email,
            role=created_mechanic.role,
            experience_years=created_mechanic.experience_years,
            properties=created_mechanic.properties,
            created_at=created_mechanic.created_at,
            updated_at=created_mechanic.updated_at,
        )
    except DatabaseError as e:
        logger.error(f"Error creating mechanic: {str(e)}")
        raise database_operation_exception()


@router.get("/", response_model=List[MechanicResponse])
async def get_mechanics(
    skip: int = 0,
    limit: int = 100,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    mechanic_repo: MechanicRepository = Depends(),
):
    """
    Get all mechanics.
    Requires authentication.
    """
    try:
        mechanics = await mechanic_repo.get_all(skip, limit)

        # Convert to response models
        return [
            MechanicResponse(
                id=mechanic.id,
                name=mechanic.name,
                email=mechanic.email,
                role=mechanic.role,
                experience_years=mechanic.experience_years,
                properties=mechanic.properties,
                created_at=mechanic.created_at,
                updated_at=mechanic.updated_at,
            )
            for mechanic in mechanics
        ]
    except DatabaseError as e:
        logger.error(f"Error getting mechanics: {str(e)}")
        raise database_operation_exception()


async def get_current_mechanic_or_dev(
    authorization: Optional[str] = Header(None),
    mechanic_repo: MechanicRepository = Depends(),
) -> MechanicInDB:
    """
    Dependency that returns current mechanic or dev user in DEV_MODE
    """
    settings = get_settings()

    logger.info(f"Authorization header received: {authorization}")
    logger.info(f"DEV_MODE setting: {settings.DEV_MODE}")

    # Try to authenticate if token is provided
    if authorization and authorization.startswith("Bearer "):
        try:
            token = authorization.split(" ")[1]
            logger.info(f"Extracted token: {token[:20]}...")

            # Use the existing decode_access_token function from security.py
            try:
                from app.core.security import decode_access_token

                payload = decode_access_token(token)
                logger.info(f"Token payload: {payload}")

                user_id = payload.get("sub")
                if user_id:
                    logger.info(f"Looking up user with ID: {user_id}")
                    mechanic = await mechanic_repo.get_by_id(user_id)
                    if mechanic:
                        logger.info(f"Found mechanic: {mechanic.email}")
                        return mechanic
                    else:
                        logger.warning(f"No mechanic found with ID: {user_id}")
                else:
                    logger.warning("No 'sub' field in token payload")

            except Exception as e:
                logger.error(f"JWT verification error: {e}")

        except Exception as e:
            logger.error(f"Token processing error: {e}")
    else:
        logger.info("No Authorization header or not Bearer token")

    # Handle case when no valid authentication
    if settings.DEV_MODE:
        logger.info("Returning dev user")
        # Return dev user when in DEV_MODE
        from app.models.domain.mechanic import MechanicRole
        from datetime import datetime

        return MechanicInDB(
            id="dev-user-001",
            name="Development User",
            email="<EMAIL>",
            role=MechanicRole.ADMIN,
            reliability_score=100,
            experience_years=10,
            properties={},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            hashed_password="",
        )
    else:
        logger.error("Not in dev mode and no valid authentication")
        # In production, require authentication
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.get("/me", response_model=MechanicResponse)
async def get_current_mechanic_info(
    current_mechanic: MechanicInDB = Depends(get_current_mechanic_or_dev),
):
    """
    Get current mechanic information.
    In DEV_MODE, returns dev user if not authenticated.
    """
    return MechanicResponse(
        id=current_mechanic.id,
        name=current_mechanic.name,
        email=current_mechanic.email,
        role=current_mechanic.role,
        experience_years=current_mechanic.experience_years,
        properties=current_mechanic.properties,
        created_at=current_mechanic.created_at,
        updated_at=current_mechanic.updated_at,
    )


@router.get("/{mechanic_id}", response_model=MechanicResponse)
async def get_mechanic(
    mechanic_id: str,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    mechanic_repo: MechanicRepository = Depends(),
):
    """
    Get a mechanic by ID.
    Requires authentication.
    """
    try:
        mechanic = await mechanic_repo.get_by_id(mechanic_id)
        if not mechanic:
            raise entity_not_found_exception("Mechanic", mechanic_id)

        # Convert to response model
        return MechanicResponse(
            id=mechanic.id,
            name=mechanic.name,
            email=mechanic.email,
            role=mechanic.role,
            experience_years=mechanic.experience_years,
            properties=mechanic.properties,
            created_at=mechanic.created_at,
            updated_at=mechanic.updated_at,
        )
    except DatabaseError as e:
        logger.error(f"Error getting mechanic: {str(e)}")
        raise database_operation_exception()


@router.put("/{mechanic_id}", response_model=MechanicResponse)
async def update_mechanic(
    mechanic_id: str,
    mechanic_update: MechanicUpdate,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    mechanic_repo: MechanicRepository = Depends(),
):
    """
    Update a mechanic.
    Mechanics can only update their own information unless they have admin role.
    Requires authentication.
    """
    # Check permissions
    from app.models.domain.mechanic import MechanicRole

    if (
        current_mechanic.id != mechanic_id
        and current_mechanic.role != MechanicRole.ADMIN
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own information",
        )

    try:
        # Convert to dict, removing None values
        update_data = mechanic_update.dict(exclude_unset=True, exclude={"password"})

        # Update mechanic
        updated_mechanic = await mechanic_repo.update_mechanic(
            mechanic_id, update_data, mechanic_update.password
        )

        if not updated_mechanic:
            raise entity_not_found_exception("Mechanic", mechanic_id)

        # Convert to response model
        return MechanicResponse(
            id=updated_mechanic.id,
            name=updated_mechanic.name,
            email=updated_mechanic.email,
            role=updated_mechanic.role,
            experience_years=updated_mechanic.experience_years,
            properties=updated_mechanic.properties,
            created_at=updated_mechanic.created_at,
            updated_at=updated_mechanic.updated_at,
        )
    except DatabaseError as e:
        logger.error(f"Error updating mechanic: {str(e)}")
        raise database_operation_exception()


@router.delete("/{mechanic_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_mechanic(
    mechanic_id: str,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    mechanic_repo: MechanicRepository = Depends(),
):
    """
    Delete a mechanic.
    Only mechanics with admin role can delete mechanics.
    Requires authentication.
    """
    # Check if current mechanic has admin role
    from app.models.domain.mechanic import MechanicRole

    if current_mechanic.role != MechanicRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admin mechanics can delete mechanics",
        )

    try:
        deleted = await mechanic_repo.delete_mechanic(mechanic_id)
        if not deleted:
            raise entity_not_found_exception("Mechanic", mechanic_id)

        return {"ok": True}
    except DatabaseError as e:
        logger.error(f"Error deleting mechanic: {str(e)}")
        raise database_operation_exception()


# DEBUG ENDPOINTS - These should be protected too in production
@router.get("/debug/check-user/{email}")
async def check_user_exists(
    email: str,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    mechanic_repo: MechanicRepository = Depends(),
):
    """
    Debug endpoint to check if user exists.
    Requires authentication.
    """
    mechanic = await mechanic_repo.get_by_email(email)
    if mechanic:
        return {
            "exists": True,
            "mechanic": {"id": mechanic.id, "email": mechanic.email},
        }
    return {"exists": False}


@router.get("/debug/raw-user/{email}")
async def get_raw_user(
    email: str,
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    db: Neo4jService = Depends(),
):
    """
    DEBUG ONLY: Get raw user data directly from Neo4j.
    Requires authentication.
    """
    query = """
    MATCH (m:Mechanic {email: $email})
    RETURN m
    """
    results = await db.execute_query(query, {"email": email})
    if results and len(results) > 0:
        return results[0]["m"]
    return {"error": "User not found"}


@router.post("/reset-admin-password")
async def reset_admin_password(
    current_mechanic: MechanicInDB = Depends(get_current_mechanic),
    db: Neo4jService = Depends(),
):
    """
    Reset the admin user password (TEMPORARY FIX).
    Only admins can use this endpoint.
    Requires authentication.
    """
    from app.models.domain.mechanic import MechanicRole
    from app.core.security import get_password_hash

    if current_mechanic.role != MechanicRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admin mechanics can reset passwords",
        )

    query = """
    MATCH (m:Mechanic {email: '<EMAIL>'})
    SET m.hashed_password = $password
    RETURN m
    """

    results = await db.execute_query(
        query, {"password": get_password_hash("adminpass123")}
    )
    if results and len(results) > 0:
        return {"success": True, "message": "Admin password reset"}
    return {"success": False, "error": "Admin user not found"}


# UNPROTECTED ENDPOINTS - These don't require authentication
@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    mechanic_repo: MechanicRepository = Depends(),
    settings=Depends(get_settings),
):
    """
    Get an access token.
    This endpoint is UNPROTECTED - no authentication required.
    """
    mechanic = await mechanic_repo.authenticate(form_data.username, form_data.password)
    if not mechanic:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token_expires = timedelta(minutes=settings.JWT_EXPIRATION_MINUTES)
    access_token = create_access_token(
        subject=mechanic.id, expires_delta=access_token_expires
    )
    
    # Set the session ID to the access token for logging purposes
    set_session_id(access_token)
    
    return {"access_token": access_token, "token_type": "bearer"}


@router.post("/ephemeral-token", response_model=EphemeralToken)
async def login_for_ephemeral_token(settings=Depends(get_settings)):
    """
    Authenticates a mechanic and returns an ephemeral token for real-time GPT session.
    This endpoint is UNPROTECTED - no authentication required.
    """
    gpt_service = RealtimeGPTService(settings.OPENAI_API_KEY)
    session_response = gpt_service.create_ephemeral_session()

    return session_response
