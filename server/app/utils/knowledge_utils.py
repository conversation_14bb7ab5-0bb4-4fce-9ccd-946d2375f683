# app/utils/knowledge_utils.py
import json
import uuid
import numpy as np
from typing import Any, Dict, List, Optional
from datetime import datetime
import logging

from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgeRelationship,
    KnowledgeType,
    RelationshipType,
)

logger = logging.getLogger(__name__)


def prepare_text_for_similarity(
    node_type: KnowledgeType,
    name: str,
    description: str,
    properties: Optional[Dict[str, Any]] = None,
) -> str:
    """
    Prepare text for similarity comparison.
    Updated for OCS structure to include observation-specific fields.
    """
    # Start with the name, repeat for weight
    processed_text = f"{name} {name} "

    # Add description
    if description:
        processed_text += description + " "

    # Add relevant properties based on node type
    if properties:
        if node_type == KnowledgeType.OBSERVATION:
            # For observations, include specific observation attributes
            for attr in [
                "visual_observation",
                "auditory_observation",
                "positional_observation",
                "error_codes",
            ]:
                if attr in properties and properties[attr]:
                    processed_text += f"{attr}: {properties[attr]} "

            # Handle related parts as a list
            if "related_parts" in properties and properties["related_parts"]:
                parts = properties["related_parts"]
                if isinstance(parts, list):
                    processed_text += "Related parts: " + " ".join(parts) + " "
                elif isinstance(parts, str):
                    processed_text += f"Related parts: {parts} "

        elif node_type == KnowledgeType.SOLUTION:
            # For solutions, part numbers and parts involved are important
            if "part_numbers" in properties:
                parts = properties.get("part_numbers", [])
                if isinstance(parts, list):
                    processed_text += "Parts: " + " ".join(parts) + " "
                elif isinstance(parts, str):
                    processed_text += f"Parts: {parts} "

            if "parts_involved" in properties:
                parts = properties.get("parts_involved", [])
                if isinstance(parts, list):
                    processed_text += "Components: " + " ".join(parts) + " "
                elif isinstance(parts, str):
                    processed_text += f"Components: {parts} "

        # For all node types, include string or list properties
        for key, value in properties.items():
            if key not in [
                "part_numbers",
                "parts_involved",
                "visual_observation",
                "auditory_observation",
                "positional_observation",
                "error_codes",
                "related_parts",
            ] and (isinstance(value, str) or isinstance(value, list)):
                if isinstance(value, list):
                    processed_text += f"{key}: " + " ".join(value) + " "
                else:
                    processed_text += f"{key}: {value} "

    return normalize_text(processed_text)


def normalize_text(text: str) -> str:
    """Normalize text by removing extra whitespace and converting to lowercase."""
    if not text:
        return ""

    # Convert to lowercase and remove extra whitespace
    text = text.lower()
    text = " ".join(text.split())
    return text


def get_similarity_threshold_for_type(
    node_type: KnowledgeType, default_threshold: float
) -> float:
    """Get an appropriate similarity threshold based on node type."""
    thresholds = {
        KnowledgeType.OBSERVATION: 0.80,
        KnowledgeType.CAUSE: 0.75,
        KnowledgeType.SOLUTION: 0.70,
        KnowledgeType.GENERAL: 0.75,
        KnowledgeType.TECHNIQUE: 0.75,
        KnowledgeType.SHORTCUT: 0.80,
        KnowledgeType.DOOR_TYPE: 0.90,
        KnowledgeType.DOOR_PART: 0.85,
    }
    return thresholds.get(node_type, default_threshold)


def is_numpy_array(value) -> bool:
    """Check if a value is a NumPy array without triggering boolean ambiguity errors."""
    return (
        hasattr(value, "shape") and hasattr(value, "dtype") and hasattr(value, "tolist")
    )


class KnowledgeNodeConverter:
    """Converter for KnowledgeNode objects."""

    def dict_to_knowledge_node(self, node_dict: Dict[str, Any]) -> KnowledgeNode:
        """Convert a dictionary to a KnowledgeNode domain object."""
        # Determine node type
        node_type = self._extract_node_type(node_dict)

        # Extract basic properties
        node_id = node_dict.get("id", str(uuid.uuid4()))
        name = node_dict.get("name", "")
        description = node_dict.get("description", "")

        # Handle dates
        created_at = self._parse_datetime(node_dict.get("created_at"), datetime.now())
        updated_at = self._parse_datetime(node_dict.get("updated_at"), created_at)
        created_by = node_dict.get("created_by", "system")

        # Extract embedding
        embedding = self._extract_embedding(node_dict)
        embedding_updated_at = self._parse_datetime(
            node_dict.get("embedding_updated_at")
        )

        # Create clean properties
        clean_properties = self._extract_clean_properties(node_dict, node_id)

        return KnowledgeNode(
            id=node_id,
            type=node_type,
            name=name,
            description=description,
            properties=clean_properties,
            created_at=created_at,
            updated_at=updated_at,
            created_by=created_by,
            embedding=embedding,
            embedding_updated_at=embedding_updated_at,
        )

    def _extract_node_type(self, node_dict: Dict[str, Any]) -> KnowledgeType:
        """Extract node type from various sources."""
        # Check type property
        if "type" in node_dict and node_dict["type"]:
            try:
                return KnowledgeType(node_dict["type"])
            except ValueError:
                pass

        # Check labels
        try:
            if "labels" in node_dict and node_dict["labels"]:
                for label in node_dict["labels"]:
                    try:
                        return KnowledgeType[label.upper()]
                    except (KeyError, AttributeError):
                        continue
            elif hasattr(node_dict, "labels"):
                for label in list(node_dict.labels):
                    try:
                        return KnowledgeType[label.upper()]
                    except (KeyError, AttributeError):
                        continue
        except Exception as e:
            logger.warning(
                f"Failed to extract label from node {node_dict.get('id', '')}: {e}"
            )

        # Infer from ID or name
        node_id = str(node_dict.get("id", ""))
        name = node_dict.get("name", "").lower()

        if node_id.startswith("O") or "observation" in node_id.lower():
            return KnowledgeType.OBSERVATION
        elif node_id.startswith("C") or "cause" in node_id.lower():
            return KnowledgeType.CAUSE
        elif node_id.startswith("S") or "solution" in node_id.lower():
            return KnowledgeType.SOLUTION
        elif (
            node_id.startswith("D")
            or "door" in node_id.lower()
            or ("deur" in name and "model" in name)
        ):
            return KnowledgeType.DOOR_TYPE

        logger.warning(f"No valid type found for node {node_id}, defaulting to GENERAL")
        return KnowledgeType.GENERAL

    def _parse_datetime(
        self, date_str: Any, default: Optional[datetime] = None
    ) -> Optional[datetime]:
        """Parse datetime string safely."""
        if isinstance(date_str, str):
            try:
                return datetime.fromisoformat(date_str)
            except ValueError:
                return default
        return default

    def _extract_embedding(self, node_dict: Dict[str, Any]) -> Optional[np.ndarray]:
        """Extract embedding from node data."""
        if "embedding" in node_dict and node_dict["embedding"]:
            try:
                return np.array(node_dict["embedding"])
            except Exception as e:
                logger.warning(f"Failed to convert embedding: {str(e)}")
        return None

    def _extract_clean_properties(
        self, node_dict: Dict[str, Any], node_id: str
    ) -> Dict[str, Any]:
        """Extract clean properties from node data."""
        clean_properties = {}

        for key, value in node_dict.items():
            # Skip special fields
            if key in ["embedding", "labels", "embedding_updated_at"]:
                continue

            try:
                if hasattr(value, "tolist") and callable(value.tolist):
                    clean_properties[key] = value.tolist()
                elif hasattr(value, "item") and callable(value.item):
                    clean_properties[key] = value.item()
                else:
                    clean_properties[key] = value

                # Test boolean context
                bool(clean_properties[key])
            except (ValueError, TypeError):
                logger.debug(f"Skipping problematic property {key} for node {node_id}")
                continue

        return clean_properties


class KnowledgeRelationshipConverter:
    """Converter for KnowledgeRelationship objects."""

    def dict_to_knowledge_relationship(
        self,
        rel_data: Any,
        source_id: str,
        target_id: str,
        relationship_type: RelationshipType,
    ) -> KnowledgeRelationship:
        """Convert relationship data to KnowledgeRelationship object."""
        # Handle various formats from Neo4j
        rel_dict = self._extract_relationship_dict(rel_data, relationship_type)

        # Parse timestamps
        created_at = self._parse_datetime(rel_dict.get("created_at"), datetime.utcnow())
        updated_at = self._parse_datetime(rel_dict.get("updated_at"), datetime.utcnow())
        valid_from = self._parse_datetime(rel_dict.get("valid_from"))
        valid_to = self._parse_datetime(rel_dict.get("valid_to"))

        # Extract properties
        properties = self._extract_properties(rel_dict)
        trust_score = float(rel_dict.get("trust_score", 0.0) or 0.0)
        success_count = int(rel_dict.get("success_count", 0) or 0)

        return KnowledgeRelationship(
            source_id=source_id,
            target_id=target_id,
            type=relationship_type,
            properties=properties,
            trust_score=trust_score,
            success_count=success_count,
            created_at=created_at,
            updated_at=updated_at,
            created_by=rel_dict.get("created_by"),
            valid_from=valid_from,
            valid_to=valid_to,
            is_current=bool(rel_dict.get("is_current", True)),
        )

    def _extract_relationship_dict(
        self, rel_data: Any, relationship_type: RelationshipType
    ) -> Dict[str, Any]:
        """Extract relationship dictionary from various Neo4j formats."""
        if isinstance(rel_data, dict):
            return rel_data
        elif isinstance(rel_data, tuple):
            try:
                if len(rel_data) >= 3:
                    middle_item = rel_data[1]
                    if isinstance(middle_item, dict) and middle_item:
                        return middle_item
                    elif hasattr(middle_item, "properties") and middle_item.properties:
                        return middle_item.properties
                return {"type": str(relationship_type.value)}
            except Exception as e:
                logger.error(f"Error processing relationship tuple: {str(e)}")
                return {"type": str(relationship_type.value)}
        elif hasattr(rel_data, "get"):
            return rel_data
        elif hasattr(rel_data, "properties"):
            return rel_data.properties
        else:
            logger.warning(f"Unexpected relationship data format: {type(rel_data)}")
            return {"type": str(relationship_type.value)}

    def _parse_datetime(
        self, date_str: Any, default: Optional[datetime] = None
    ) -> Optional[datetime]:
        """Parse datetime string safely."""
        if not date_str:
            return default
        try:
            return datetime.fromisoformat(date_str)
        except (ValueError, TypeError):
            return default

    def _extract_properties(self, rel_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Extract properties from relationship dictionary."""
        properties_json = rel_dict.get("properties_json")
        if properties_json:
            try:
                return json.loads(properties_json)
            except (json.JSONDecodeError, TypeError):
                logger.warning("Failed to parse properties_json for relationship")
        return rel_dict.get("properties", {})
