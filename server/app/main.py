import asyncio
from datetime import datetime
import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import get_settings
from app.core.exceptions import (
    DatabaseError,
    KnowledgeExtractionError,
    TranscriptionError,
    QueryProcessingError,
)
from app.api.routes import knowledge, mechanics, feedback
from app.services.database.repositories.door_repo import DoorRepository
from app.services.cache.door_cache import DoorTypeCache
from app.services.database.neo4j import Neo4jService
from app.services.cache.embedding_cache import EmbeddingCache
from app.services.embeddings.embedding import EmbeddingService

# Import the refactored repositories and services
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)
from app.services.database.repositories.knowledge.node_repository import (
    NodeRepository,
)
from app.services.database.repositories.knowledge.relationship_repository import (
    RelationshipRepository,
)
from app.services.database.repositories.knowledge.path_repository import (
    PathRepository,
)
from app.services.knowledge.similarity_service import SimilarityService
from app.services.knowledge.feedback_service import FeedbackService

# Import the migration utilities
from app.services.database.migrations import (
    create_vector_indices,
    migrate_embeddings,
)

from app.core.logging import (
    setup_logging,
    log_api,
    print_logger_info,
)

DEBUG = "DEBUG"
INFO = "INFO"
WARNING = "WARNING"
ERROR = "ERROR"


# INFO: to add new loggers, add them to the LOGGER_REGISTRY in logging.py
LOGGING_LEVEL_CONSOLE = INFO  # final console filter
LOGGING_LEVEL_EXTRACTION = DEBUG  # what to display in extraction.log
LOGGING_LEVEL_RETRIEVAL = DEBUG  # what to display in retrieval.log
LOGGING_LEVEL_DATABASE = DEBUG  # what to display in database.log
LOGGING_LEVEL_API = DEBUG  # what to display in api.log
LOGGING_LEVEL_OTHER = DEBUG  # what to display in other.log

setup_logging(
    console_level=LOGGING_LEVEL_CONSOLE,
    component_levels={
        "extraction": LOGGING_LEVEL_EXTRACTION,
        "retrieval": LOGGING_LEVEL_RETRIEVAL,
        "database": LOGGING_LEVEL_DATABASE,
        "api": LOGGING_LEVEL_API,
        "other": LOGGING_LEVEL_OTHER,
    },
)

logger = log_api()
print_logger_info()


EMBEDDING_VECTOR_SIZE = 3072
STARTUP_EMBEDDING_BATCH_SIZE = 100
BACKGROUND_EMBEDDING_BATCH_SIZE = 200
MAX_EMBEDDING_BATCHES = 20

# Application startup event
app_ready_event = asyncio.Event()

# Global repository instances (for dependency injection)
knowledge_repo_instance = None
neo4j_service_instance = None
embedding_service_instance = None
embedding_cache_instance = None


# Create FastAPI app
settings = get_settings()
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="API for the Revolving Door Knowledge System",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(
    knowledge.router, prefix="/api/knowledge", tags=["knowledge"]
)
app.include_router(
    mechanics.router, prefix="/api/mechanics", tags=["mechanics"]
)
app.include_router(feedback.router, prefix="/api/feedback", tags=["feedback"])


# Exception handlers
@app.exception_handler(DatabaseError)
async def database_exception_handler(request: Request, exc: DatabaseError):
    logger.error(f"Database error: {exc.message}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": exc.message},
    )


@app.exception_handler(KnowledgeExtractionError)
async def knowledge_extraction_exception_handler(
    request: Request, exc: KnowledgeExtractionError
):
    logger.error(f"Knowledge extraction error: {exc.message}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": exc.message},
    )


@app.exception_handler(TranscriptionError)
async def transcription_exception_handler(
    request: Request, exc: TranscriptionError
):
    logger.error(f"Transcription error: {exc.message}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": exc.message},
    )


@app.exception_handler(QueryProcessingError)
async def query_processing_exception_handler(
    request: Request, exc: QueryProcessingError
):
    logger.error(f"Query processing error: {exc.message}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": exc.message},
    )


@app.on_event("startup")
async def startup_event():
    global knowledge_repo_instance, neo4j_service_instance, embedding_service_instance, embedding_cache_instance

    logger.info("Starting application...")
    

    # Initialize core services
    neo4j_service_instance = Neo4jService()
    embedding_cache_instance = EmbeddingCache.get_instance()
    embedding_service_instance = EmbeddingService(
        settings=get_settings(), embedding_cache=embedding_cache_instance
    )

    # Initialize door cache
    door_repo = DoorRepository(db=neo4j_service_instance)
    door_cache = DoorTypeCache.get_instance()
    await door_cache.preload(door_repo)
    logger.info("Door type cache initialized")

    # Create the refactored knowledge repository with proper dependency injection
    knowledge_repo_instance = KnowledgeRepository(
        db=neo4j_service_instance,
        embedding_service=embedding_service_instance,
        embedding_cache=embedding_cache_instance,
    )

    try:
        # Check if vector indices exist
        check_query = """
        SHOW INDEXES
        YIELD name, type
        WHERE type = 'VECTOR'
        AND (name = 'observation_embedding' OR
            name = 'cause_embedding' OR
            name = 'solution_embedding' OR
            name = 'observation_visual_embedding' OR
            name = 'observation_auditory_embedding' OR
            name = 'observation_positional_embedding')
        RETURN count(*) > 0 as has_indices
        """

        index_result = await neo4j_service_instance.execute_query(
            check_query, {}
        )
        has_indices = (
            index_result[0].get("has_indices", False)
            if index_result
            else False
        )

        if not has_indices:
            # Try to create vector indices
            logger.info(
                "Vector indices not found, attempting to create them..."
            )
            async with neo4j_service_instance.driver.session() as session:
                await create_vector_indices(session)
        else:
            logger.info("Vector indices already exist, skipping creation")

        # Check if embeddings need to be generated or migrated
        # Count nodes without embeddings
        missing_embedding_query = f"""
        MATCH (n)
        WHERE (n:OBSERVATION OR n:CAUSE OR n:SOLUTION)
        AND (n.embedding IS NULL OR size(n.embedding) <> {EMBEDDING_VECTOR_SIZE})
        RETURN count(n) as missing_embeddings
        """

        missing_result = await neo4j_service_instance.execute_query(
            missing_embedding_query, {}
        )

        missing_embeddings = 0
        if missing_result and len(missing_result) > 0:
            missing_embeddings = missing_result[0].get("missing_embeddings", 0)

        # If there are missing embeddings, start the migration process
        if missing_embeddings > 0:
            logger.info(
                f"Found {missing_embeddings} nodes without valid embeddings"
            )
            # Only process a small batch during startup to keep startup fast
            startup_batch_size = min(100, missing_embeddings)
            if startup_batch_size > 0:
                logger.info(
                    f"Processing initial batch of {startup_batch_size} embeddings during startup"
                )
                await process_embedding_batch(
                    neo4j_service_instance,
                    embedding_service_instance,
                    startup_batch_size,
                )
                logger.info(
                    f"Initial embedding batch processed, scheduling background task for remaining {missing_embeddings - startup_batch_size}"
                )

            # Schedule background task for remaining embeddings
            if missing_embeddings > startup_batch_size:
                asyncio.create_task(
                    update_missing_embeddings(
                        neo4j_service_instance, embedding_service_instance
                    )
                )
        else:
            logger.info("All nodes have valid embeddings, no migration needed")

    except Exception as e:
        logger.error(f"Error setting up vector search: {str(e)}")
        logger.info("Continuing with fallback similarity search")

    try:
        # Preload embedding cache with frequently accessed nodes
        await embedding_cache_instance.preload(
            knowledge_repo_instance, limit=500
        )
        logger.info("Embedding cache initialized")

    except Exception as e:
        logger.error(f"Error initializing embedding cache: {str(e)}")
        logger.info("Continuing startup without embedding cache preload")

    # Signal that application startup is complete
    app_ready_event.set()
    logger.info("Application startup complete")


async def process_embedding_batch(
    neo4j_service, embedding_service, batch_size=100
):
    """
    Process a batch of embeddings using efficient batch operations.
    Updated to use multi-embeddings for OBSERVATION nodes.
    """
    # Query to get nodes that need embedding updates
    query = f"""
    MATCH (n)
    WHERE (n:OBSERVATION OR n:CAUSE OR n:SOLUTION)
    AND (
        (n:OBSERVATION AND (
            n.visual_embedding IS NULL OR
            n.auditory_embedding IS NULL OR
            n.positional_embedding IS NULL OR
            size(n.visual_embedding) <> {EMBEDDING_VECTOR_SIZE} OR
            size(n.auditory_embedding) <> {EMBEDDING_VECTOR_SIZE} OR
            size(n.positional_embedding) <> {EMBEDDING_VECTOR_SIZE}
        )) OR
        (NOT n:OBSERVATION AND (
            n.embedding IS NULL OR
            size(n.embedding) <> {EMBEDDING_VECTOR_SIZE}
        ))
    )
    RETURN n.id as id, n.name as name, n.description as description,
    CASE WHEN n:OBSERVATION THEN n.visual_observation ELSE NULL END as visual,
    CASE WHEN n:OBSERVATION THEN n.auditory_observation ELSE NULL END as auditory,
    CASE WHEN n:OBSERVATION THEN n.positional_observation ELSE NULL END as positional,
    CASE WHEN n:OBSERVATION THEN n.error_codes ELSE NULL END as error_codes,
    labels(n) as labels
    LIMIT $batch_size
    """

    results = await neo4j_service.execute_query(
        query, {"batch_size": batch_size}
    )

    if not results:
        return 0

    # Separate nodes by type
    observation_nodes = []
    other_nodes = []
    for node in results:
        labels = node.get("labels", [])
        if "OBSERVATION" in labels:
            observation_nodes.append(node)
        else:
            other_nodes.append(node)

    # Process OBSERVATION nodes with multi-embeddings
    observation_updates = 0
    if observation_nodes:
        # Prepare data for batch processing
        observation_ids = []
        visual_texts = []
        auditory_texts = []
        positional_texts = []

        for node in observation_nodes:
            node_id = node.get("id")
            if not node_id:
                continue

            observation_ids.append(node_id)

            # Get observation texts
            visual = node.get("visual", "") or ""
            auditory = node.get("auditory", "") or ""
            positional = node.get("positional", "") or ""

            # Add texts to batch
            visual_texts.append(visual)
            auditory_texts.append(auditory)
            positional_texts.append(positional)

        if observation_ids:
            embedding_tasks = []

            if visual_texts:
                embedding_tasks.append(
                    (
                        "visual",
                        asyncio.create_task(
                            embedding_service.generate_embeddings_batch(
                                visual_texts
                            )
                        ),
                    )
                )

            if auditory_texts:
                embedding_tasks.append(
                    (
                        "auditory",
                        asyncio.create_task(
                            embedding_service.generate_embeddings_batch(
                                auditory_texts
                            )
                        ),
                    )
                )

            if positional_texts:
                embedding_tasks.append(
                    (
                        "positional",
                        asyncio.create_task(
                            embedding_service.generate_embeddings_batch(
                                positional_texts
                            )
                        ),
                    )
                )

            # Wait for all embedding tasks
            embeddings = {}
            for emb_type, task in embedding_tasks:
                try:
                    embedding_result = await task
                    embeddings[emb_type] = embedding_result
                except Exception as e:
                    logger.error(
                        f"Error generating {emb_type} embeddings: {str(e)}"
                    )

            # Create batch updates
            updates = []
            for i, node_id in enumerate(observation_ids):
                update = {
                    "id": node_id,
                    "updated_at": datetime.utcnow().isoformat(),
                }

                # Add embeddings to update
                for emb_type, emb_results in embeddings.items():
                    if i < len(emb_results):
                        update[f"{emb_type}_embedding"] = emb_results[
                            i
                        ].tolist()

                        # Update cache
                        if emb_type == "visual":
                            embedding_service.embedding_cache.set(
                                node_id, emb_results[i], "visual"
                            )
                        elif emb_type == "auditory":
                            embedding_service.embedding_cache.set(
                                node_id, emb_results[i], "auditory"
                            )
                        elif emb_type == "positional":
                            embedding_service.embedding_cache.set(
                                node_id, emb_results[i], "positional"
                            )

                updates.append(update)

            # Execute batch update
            update_query = """
            UNWIND $updates as update
            MATCH (n:OBSERVATION {id: update.id})
            SET n.embedding_updated_at = update.updated_at
            """

            # Add SET clauses for each embedding type
            set_clauses = []
            params = {"updates": updates}

            if "visual" in embeddings:
                set_clauses.append(
                    "n.visual_embedding = update.visual_embedding"
                )

            if "auditory" in embeddings:
                set_clauses.append(
                    "n.auditory_embedding = update.auditory_embedding"
                )

            if "positional" in embeddings:
                set_clauses.append(
                    "n.positional_embedding = update.positional_embedding"
                )

            if set_clauses:
                update_query = update_query.replace(
                    "SET n.embedding_updated_at = update.updated_at",
                    f"SET {', '.join(set_clauses)}, n.embedding_updated_at = update.updated_at",
                )

            # Execute update
            try:
                await neo4j_service.execute_query(update_query, params)
                observation_updates = len(updates)
                logger.info(
                    f"Updated {observation_updates} OBSERVATION nodes with multi-embeddings"
                )
            except Exception as e:
                logger.error(
                    f"Error updating OBSERVATION embeddings: {str(e)}"
                )

    # Process other node types with single embeddings (for backward compatibility)
    other_updates = 0
    if other_nodes:
        node_ids = []
        texts = []

        for node in other_nodes:
            node_id = node.get("id")
            name = node.get("name", "")
            description = node.get("description", "")

            if not node_id:
                continue

            node_ids.append(node_id)
            texts.append(f"{name} {description}")

        if node_ids and texts:
            # Generate embeddings
            embeddings = await embedding_service.generate_embeddings_batch(
                texts
            )

            # Create updates
            updates = []
            for i, node_id in enumerate(node_ids):
                if i < len(embeddings):
                    updates.append(
                        {
                            "id": node_id,
                            "embedding": embeddings[i].tolist(),
                            "updated_at": datetime.utcnow().isoformat(),
                        }
                    )

                    # Update cache
                    embedding_service.embedding_cache.set(
                        node_id, embeddings[i], "default"
                    )

            # Execute update
            if updates:
                update_query = """
                UNWIND $updates as update
                MATCH (n {id: update.id})
                WHERE NOT n:OBSERVATION
                SET n.embedding = update.embedding,
                    n.embedding_updated_at = update.updated_at
                """

                try:
                    await neo4j_service.execute_query(
                        update_query, {"updates": updates}
                    )
                    other_updates = len(updates)
                    logger.info(
                        f"Updated {other_updates} non-OBSERVATION nodes with embeddings"
                    )
                except Exception as e:
                    logger.error(
                        f"Error updating non-OBSERVATION embeddings: {str(e)}"
                    )

    # Return total number of nodes processed
    return observation_updates + other_updates


# Helper function for async embedding generation
async def generate_embeddings_batch_async(embedding_service, texts):
    """Generate embeddings in a separate task"""
    return await embedding_service.generate_embeddings_batch(texts)


async def update_missing_embeddings(neo4j_service, embedding_service):
    """Background task to update missing embeddings efficiently."""
    try:
        # Wait for application to fully start
        await app_ready_event.wait()
        logger.info(
            "Starting background task to calculate missing embeddings in vector format"
        )

        # Count remaining nodes needing embeddings
        count_query = f"""
        MATCH (n)
        WHERE (n:OBSERVATION OR n:CAUSE OR n:SOLUTION)
        AND (n.embedding IS NULL OR size(n.embedding) <> {EMBEDDING_VECTOR_SIZE})
        RETURN count(n) as missing_count
        """

        count_result = await neo4j_service.execute_query(count_query, {})
        missing_count = (
            count_result[0].get("missing_count", 0) if count_result else 0
        )

        if missing_count == 0:
            logger.info("No missing embeddings to process in background task")
            return

        logger.info(
            f"Background task processing {missing_count} nodes with missing embeddings"
        )

        # Process in larger batches for efficiency
        batch_size = 200
        total_processed = 0
        max_batches = 20  # Limit total processing to avoid excessive API calls
        batch_count = 0

        while total_processed < missing_count and batch_count < max_batches:
            processed = await process_embedding_batch(
                neo4j_service, embedding_service, batch_size
            )

            if processed == 0:
                # No more nodes to process
                break

            total_processed += processed
            batch_count += 1

            logger.info(
                f"Background task: processed {total_processed}/{missing_count} embeddings"
            )

            # Sleep between batches to avoid rate limits
            await asyncio.sleep(2)

        remaining = missing_count - total_processed
        if remaining > 0:
            logger.info(
                f"Background task completed with {remaining} embeddings remaining. These will be processed during future application startups."
            )
        else:
            logger.info(
                f"Background embedding calculation completed: all {total_processed} nodes processed"
            )

    except Exception as e:
        logger.error(f"Error in background embedding calculation: {str(e)}")


# Dependency injection functions for FastAPI
def get_knowledge_repository() -> KnowledgeRepository:
    """Get the global knowledge repository instance."""
    if knowledge_repo_instance is None:
        raise RuntimeError(
            "Knowledge repository not initialized. Application may not have started properly."
        )
    return knowledge_repo_instance


def get_neo4j_service() -> Neo4jService:
    """Get the global Neo4j service instance."""
    if neo4j_service_instance is None:
        raise RuntimeError(
            "Neo4j service not initialized. Application may not have started properly."
        )
    return neo4j_service_instance


def get_embedding_service() -> EmbeddingService:
    """Get the global embedding service instance."""
    if embedding_service_instance is None:
        raise RuntimeError(
            "Embedding service not initialized. Application may not have started properly."
        )
    return embedding_service_instance


def get_embedding_cache() -> EmbeddingCache:
    """Get the global embedding cache instance."""
    if embedding_cache_instance is None:
        raise RuntimeError(
            "Embedding cache not initialized. Application may not have started properly."
        )
    return embedding_cache_instance


@app.get("/")
async def root():
    """
    Root endpoint.
    """
    return {
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "environment": settings.APP_ENV,
    }


@app.get("/health")
async def health_check():
    """
    Health check endpoint.
    """
    return {"status": "ok"}
