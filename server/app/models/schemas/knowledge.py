from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

from app.models.domain.knowledge import KnowledgeType, RelationshipType


# Base models for various knowledge node types
class KnowledgeNodeBase(BaseModel):
    type: KnowledgeType
    name: str
    description: str
    properties: Optional[Dict[str, Any]] = {}


# New observation node base model with specific fields
class ObservationNodeBase(BaseModel):
    type: KnowledgeType = KnowledgeType.OBSERVATION
    name: str
    description: str
    properties: Optional[Dict[str, Any]] = {}
    visual_observation: Optional[str] = None
    auditory_observation: Optional[str] = None
    positional_observation: Optional[str] = None
    error_codes: Optional[List[str]] = None
    related_parts: Optional[List[str]] = None
    other_information: Optional[str] = None


class KnowledgeNodeCreate(KnowledgeNodeBase):
    pass


class ObservationNodeCreate(ObservationNodeBase):
    pass


class KnowledgeNodeUpdate(BaseModel):
    type: Optional[KnowledgeType] = None
    name: Optional[str] = None
    description: Optional[str] = None
    properties: Optional[Dict[str, Any]] = None


class ObservationNodeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    properties: Optional[Dict[str, Any]] = None
    visual_observation: Optional[str] = None
    auditory_observation: Optional[str] = None
    positional_observation: Optional[str] = None
    error_codes: Optional[List[str]] = None
    related_parts: Optional[List[str]] = None
    other_information: Optional[str] = None


class KnowledgeNodeInDB(KnowledgeNodeBase):
    id: str
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None

    class Config:
        from_attributes = True


class ObservationNodeInDB(ObservationNodeBase):
    id: str
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None

    class Config:
        from_attributes = True


class KnowledgeRelationshipBase(BaseModel):
    source_id: str
    target_id: str
    type: RelationshipType
    properties: Optional[Dict[str, Any]] = {}
    trust_score: float = 0.0
    valid_from: Optional[datetime] = None
    valid_to: Optional[datetime] = None
    is_current: bool = True


class KnowledgeRelationshipCreate(KnowledgeRelationshipBase):
    pass


class KnowledgeRelationshipUpdate(BaseModel):
    properties: Optional[Dict[str, Any]] = None
    trust_score: Optional[float] = None
    valid_to: Optional[datetime] = None
    is_current: Optional[bool] = None


class KnowledgeRelationshipInDB(KnowledgeRelationshipBase):
    success_count: int = 0
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str] = None

    class Config:
        from_attributes = True


# Updated path response model to represent OCS instead of SPCS
class KnowledgePathResponse(BaseModel):
    id: str
    nodes: List[KnowledgeNodeInDB]
    relationships: List[KnowledgeRelationshipInDB]
    trust_score: float = 0.0


class TranscriptionRequest(BaseModel):
    audio_data: str = Field(..., description="Base64 encoded audio data")


class TranscriptionResponse(BaseModel):
    text: str


class KnowledgeExtractionRequest(BaseModel):
    text: str
    door_model: Optional[str] = None
    service_order_number: Optional[str] = None


class KnowledgeExtractionSummary(BaseModel):
    """Summary of extracted knowledge components."""

    observation: int = Field(0, description="Number of observations extracted")
    cause: int = Field(0, description="Number of causes extracted")
    solution: int = Field(0, description="Number of solutions extracted")
    tacit_knowledge: int = Field(
        0, description="Number of tacit knowledge items extracted"
    )


class KnowledgeExtractionResponse(BaseModel):
    """Response model for knowledge extraction."""

    nodes: List[KnowledgeNodeInDB]
    relationships: Optional[List[KnowledgeRelationshipInDB]] = None
    paths: List[KnowledgePathResponse] = []
    summary: Optional[KnowledgeExtractionSummary] = None
    admin: Optional[str] = None


class ExtractedNode(BaseModel):
    """Simple extracted node with just the data and type."""

    type: str
    data: Dict[str, Any]
    path_id: Optional[int] = None


class FirstKnowledgeExtractionResponse(BaseModel):
    """Response model for initial knowledge extraction and before entering the database."""

    nodes: List[ExtractedNode]
    raw_extracted_data: Optional[str]
    admin: Optional[str] = None


class OCSNode(BaseModel):
    """A node in an extracted OCS path."""

    name: str
    description: str
    properties: Optional[Dict[str, Any]] = {}


class KnowledgePath(BaseModel):
    """A single OCS path representing an observation-cause-solution chain."""

    observation: Optional[OCSNode] = None
    cause: Optional[OCSNode] = None
    solution: Optional[OCSNode] = None


class KnowledgePreviewResponse(BaseModel):
    """Response model for knowledge extraction preview."""

    knowledge_paths: List[KnowledgePath]
    tacit_knowledge: List[OCSNode] = []
    confirmation_token: str
    door_model: str
    environment: Optional[OCSNode] = None
    summary: KnowledgeExtractionSummary


class KnowledgeConfirmation(BaseModel):
    """Model for confirming extracted knowledge."""

    confirmation_token: str
    knowledge_paths: List[KnowledgePath]
    tacit_knowledge: List[OCSNode] = []
    door_model: str
    environment: Optional[OCSNode] = None


class QueryRequest(BaseModel):
    text: str
    door_type_id: Optional[str] = None
    door_model: Optional[str] = None
    context_info: Optional[Dict[str, Any]] = {}
    service_order_number: Optional[str] = None


class QueryObservationRequest(BaseModel):
    query_intent: str
    language: str
    observation: Optional[str] = None  # Summary of all observations/symptoms
    visual_observation: Optional[str] = None  # What is visually observed
    auditory_observation: Optional[str] = None  # Sounds heard from the door
    positional_observation: Optional[str] = (
        None  # Where on the door the issue is located
    )
    error_codes: Optional[List[str]] = None  # Any error code mentioned
    related_parts: Optional[List[str]] = None  # Parts of the door related to the issue
    cause: Optional[str] = None  # Any cause mentioned
    door_model: Optional[str] = (
        None  # Any door model mentioned - match to one in our database if possible
    )
    environment: Optional[str] = None  # Any environment condition mentioned
    context_info: Optional[Dict[str, Any]] = {}  # Additional context information


class QueryResponse(BaseModel):
    paths: List[KnowledgePathResponse]
    answer: Optional[str] = None
    transcribed_text: Optional[str] = None


# Pydantic model for the feedback request
class FeedbackRequest(BaseModel):
    session_id: str
    thumbs_up: bool
    comment: Optional[str] = None


class FeedbackResponse(BaseModel):
    message: str
