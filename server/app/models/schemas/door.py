from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime


class DoorTypeBase(BaseModel):
    model: str
    manufacturer: str
    properties: Optional[Dict[str, Any]] = {}


class DoorTypeCreate(DoorTypeBase):
    pass


class DoorTypeUpdate(BaseModel):
    model: Optional[str] = None
    manufacturer: Optional[str] = None
    properties: Optional[Dict[str, Any]] = None


class DoorTypeInDB(DoorTypeBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DoorPartBase(BaseModel):
    name: str
    function: str
    properties: Optional[Dict[str, Any]] = {}


class DoorPartCreate(DoorPartBase):
    pass


class DoorPartUpdate(BaseModel):
    name: Optional[str] = None
    function: Optional[str] = None
    properties: Optional[Dict[str, Any]] = None


class DoorPartInDB(DoorPartBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EnvironmentBase(BaseModel):
    condition: str
    description: str
    properties: Optional[Dict[str, Any]] = {}


class EnvironmentCreate(EnvironmentBase):
    pass


class EnvironmentUpdate(BaseModel):
    condition: Optional[str] = None
    description: Optional[str] = None
    properties: Optional[Dict[str, Any]] = None


class EnvironmentInDB(EnvironmentBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
