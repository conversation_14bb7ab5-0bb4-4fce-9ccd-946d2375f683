from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime


class FeedbackEntry(BaseModel):
    """Individual feedback entry stored in relationships"""

    type: str  # "positive" or "negative"
    timestamp: datetime
    mechanic_id: Optional[str] = None


class RelationshipFeedbackRequest(BaseModel):
    """Request to add feedback to RESOLVED_BY relationships"""

    relationships: List[Dict[str, str]]  # [{"source_id": "x", "target_id": "y"}]
    feedback_type: str  # "positive" or "negative"


class RelationshipFeedbackResponse(BaseModel):
    """Response after adding feedback"""

    message: str
    updated_relationships: List[Dict[str, Any]]
