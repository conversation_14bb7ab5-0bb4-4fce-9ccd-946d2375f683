from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Dict, Any
from datetime import datetime

from app.models.domain.mechanic import MechanicRole


class MechanicBase(BaseModel):
    name: str
    email: EmailStr
    role: MechanicRole
    experience_years: float = 0.0
    experience_years: int = 0
    properties: Optional[Dict[str, Any]] = {}


class MechanicCreate(MechanicBase):
    password: str = Field(..., min_length=8)


class MechanicUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    role: Optional[MechanicRole] = None
    experience_years: Optional[float] = None
    experience_years: Optional[int] = None
    properties: Optional[Dict[str, Any]] = None
    password: Optional[str] = Field(None, min_length=8)


class MechanicInDB(MechanicBase):
    id: str
    created_at: datetime
    updated_at: datetime
    hashed_password: str

    class Config:
        from_attributes = True


class MechanicResponse(MechanicBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenPayload(BaseModel):
    sub: Optional[str] = None


class EphemeralToken(BaseModel):
    client_secret: str
    session_id: str
    expires_at: datetime
    model: str
    modalities: list
    instructions: str
    voice: str
    input_audio_transcription: dict
    turn_detection: Optional[dict] = None
