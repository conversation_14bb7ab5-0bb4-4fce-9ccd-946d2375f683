from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any


class MechanicRole(str, Enum):
    JUNIOR = "JUNIOR"
    SENIOR = "SENIOR"
    ADMIN = "ADMIN"


class Mechanic:
    def __init__(
        self,
        id: Optional[str] = None,
        name: str = "",
        email: str = "",
        role: MechanicRole = MechanicRole.JUNIOR,
        experience_years: float = 0.0,
        properties: Optional[Dict[str, Any]] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        self.id = id
        self.name = name
        self.email = email
        self.role = role
        self.experience_years = experience_years
        self.experience_years = experience_years
        self.properties = properties or {}
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Neo4j operations."""
        return {
            "id": self.id,
            "name": self.name,
            "email": self.email,
            "role": self.role.value,
            "experience_years": self.experience_years,
            "experience_years": self.experience_years,
            "properties": self.properties,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
