from datetime import datetime
from typing import Optional, Dict, Any


class DoorType:
    def __init__(
        self,
        id: Optional[str] = None,
        model: str = "",
        manufacturer: str = "",
        properties: Optional[Dict[str, Any]] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        self.id = id
        self.model = model
        self.manufacturer = manufacturer
        self.properties = properties or {}
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Neo4j operations."""
        return {
            "id": self.id,
            "model": self.model,
            "manufacturer": self.manufacturer,
            "properties": self.properties,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class DoorPart:
    def __init__(
        self,
        id: Optional[str] = None,
        name: str = "",
        function: str = "",
        properties: Optional[Dict[str, Any]] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        self.id = id
        self.name = name
        self.function = function
        self.properties = properties or {}
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Neo4j operations."""
        return {
            "id": self.id,
            "name": self.name,
            "function": self.function,
            "properties": self.properties,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class Environment:
    def __init__(
        self,
        id: Optional[str] = None,
        condition: str = "",
        description: str = "",
        properties: Optional[Dict[str, Any]] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
    ):
        self.id = id
        self.condition = condition
        self.description = description
        self.properties = properties or {}
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Neo4j operations."""
        return {
            "id": self.id,
            "condition": self.condition,
            "description": self.description,
            "properties": self.properties,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
