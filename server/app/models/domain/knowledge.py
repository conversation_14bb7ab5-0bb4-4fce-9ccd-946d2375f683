from enum import Enum
from datetime import datetime
from typing import Optional, List, Dict, Any
import json
import uuid


class KnowledgeType(str, Enum):
    DOOR_TYPE = "DOOR_TYPE"
    DOOR_PART = "DOOR_PART"
    OBSERVATION = "OBSERVATION"
    CAUSE = "CAUSE"
    SOLUTION = "SOLUTION"
    GENERAL = "GENERAL"
    TECHNIQUE = "TECHNIQUE"
    SHORTCUT = "SHORTCUT"


class RelationshipType(str, Enum):
    OBSERVED_WITH = "OBSERVED_WITH"  # New relationship from Observation to Cause
    RESOLVED_BY = "RESOLVED_BY"  # Existing relationship from Cause to Solution
    APPLIES_TO = "APPLIES_TO"  # Existing relationship from Solution to Door
    INVOLVES = "INVOLVES"
    AFFECTS = "AFFECTS"
    CONTRIBUTED = "CONTRIBUTED"
    RELATES_TO = "RELATES_TO"
    ADDRESSES = "ADDRESSES"
    IMPROVES = "IMPROVES"
    NOTES = "NOTES"
    TRACKS = "TRACKS"
    # Removed INDICATES and CAUSED_BY relationships


# Updated KnowledgeNode class with Observation properties
class KnowledgeNode:
    def __init__(
        self,
        id: Optional[str] = None,
        type: KnowledgeType = KnowledgeType.GENERAL,
        name: str = "",
        description: str = "",
        trust_score: Optional[float] = 0,
        properties: Optional[Dict[str, Any]] = None,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
        created_by: Optional[str] = None,
        embedding: Optional[List[float]] = None,
        embedding_updated_at: Optional[datetime] = None,
        # Fields for Observation type
        visual_observation: Optional[str] = None,
        auditory_observation: Optional[str] = None,
        positional_observation: Optional[str] = None,
        error_codes: Optional[List[str]] = None,
        related_parts: Optional[List[str]] = None,
        other_information: Optional[str] = None,
    ):
        self.id = id
        self.type = type
        self.name = name
        self.description = description
        self.trust_score = trust_score
        self.properties = properties or {}
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()
        self.created_by = created_by
        self.embedding = embedding
        self.embedding_updated_at = embedding_updated_at

        # Set observation-specific fields
        if type == KnowledgeType.OBSERVATION:
            self.visual_observation = visual_observation
            self.auditory_observation = auditory_observation
            self.positional_observation = positional_observation
            self.error_codes = error_codes
            self.related_parts = related_parts or []
            self.other_information = other_information

            # Store observation attributes in properties for easy retrieval
            if visual_observation:
                self.properties["visual_observation"] = visual_observation
            if auditory_observation:
                self.properties["auditory_observation"] = auditory_observation
            if positional_observation:
                self.properties["positional_observation"] = positional_observation
            if error_codes:
                self.properties["error_codes"] = error_codes
            if related_parts:
                self.properties["related_parts"] = related_parts
            if other_information:
                self.properties["other_information"] = other_information

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Neo4j operations."""
        result = {
            "id": self.id,
            "type": self.type.value,
            "name": self.name,
            "description": self.description,
            "trust_score": self.trust_score,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "created_by": self.created_by,
        }

        # Convert properties to JSON
        if self.properties:
            result["properties_json"] = json.dumps(self.properties)

        # Include embedding if available
        if self.embedding:
            result["embedding"] = self.embedding
            # NOTE: Currently, we use whole-node embeddings where the entire node's textual
            # content is embedded as a single vector. This approach is simple and effective
            # for our current use cases. Future work may explore per-property embeddings or
            # multi-vector approaches for more granular similarity matching, but this would
            # require significant changes to the embedding generation, storage, and retrieval
            # pipeline. The current approach balances performance and implementation complexity.

        # Include embedding update timestamp if available
        if self.embedding_updated_at:
            result["embedding_updated_at"] = self.embedding_updated_at.isoformat()

        return result


class KnowledgeRelationship:
    def __init__(
        self,
        source_id: str,
        target_id: str,
        type: RelationshipType,
        properties: Optional[Dict[str, Any]] = None,
        success_count: int = 0,
        trust_score: Optional[float] = 0.0,
        created_at: Optional[datetime] = None,
        updated_at: Optional[datetime] = None,
        created_by: Optional[str] = None,
        valid_from: Optional[datetime] = None,
        valid_to: Optional[datetime] = None,
        is_current: bool = True,
    ):
        self.source_id = source_id
        self.target_id = target_id
        self.type = type
        self.properties = properties or {}
        self.trust_score = trust_score
        self.success_count = success_count
        self.created_at = created_at or datetime.utcnow()
        self.updated_at = updated_at or datetime.utcnow()
        self.created_by = created_by
        self.valid_from = valid_from or datetime.utcnow()
        self.valid_to = valid_to
        self.is_current = is_current

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for Neo4j operations."""
        result = {
            "source_id": self.source_id,
            "target_id": self.target_id,
            "type": self.type.value,
            "trust_score": self.trust_score,
            "success_count": self.success_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "created_by": self.created_by,
            "valid_from": self.valid_from.isoformat(),
            "is_current": self.is_current,
        }

        # Convert properties to JSON if they exist
        if self.properties:
            result["properties_json"] = json.dumps(self.properties)

        # Include valid_to if it exists
        if self.valid_to:
            result["valid_to"] = self.valid_to.isoformat()

        return result


# Updated KnowledgePath to represent OCS instead of SPCS
class KnowledgePath:
    def __init__(
        self,
        nodes: List[KnowledgeNode],
        relationships: List[KnowledgeRelationship],
        trust_score: float = 0.0,
    ):
        self.nodes = nodes
        self.relationships = relationships
        self.trust_score = trust_score

    def generate_path_id(self) -> str:
        """
        Generate a deterministic UUID5-based path ID using the structure of nodes and relationships.
        """
        # Sort nodes by type and id to ensure deterministic order
        sorted_nodes = sorted(self.nodes, key=lambda n: (n.type.value, n.id or ""))
        sorted_relationships = sorted(
            self.relationships,
            key=lambda r: (r.type.value, r.source_id, r.target_id),
        )

        # Build a consistent string representation of the path
        path_string = ""
        for node in sorted_nodes:
            path_string += f"{node.type.value}:{node.id}|"

        for rel in sorted_relationships:
            path_string += f"{rel.type.value}:{rel.source_id}->{rel.target_id}|"

        # Use UUID5 with a fixed namespace (could be NAMESPACE_URL or a custom one)
        return str(uuid.uuid5(uuid.NAMESPACE_URL, path_string))

    @property
    def observation_node(self) -> Optional[KnowledgeNode]:
        """Get the observation node from the path if it exists."""
        for node in self.nodes:
            if node.type == KnowledgeType.OBSERVATION:
                return node
        return None

    @property
    def cause_node(self) -> Optional[KnowledgeNode]:
        """Get the cause node from the path if it exists."""
        for node in self.nodes:
            if node.type == KnowledgeType.CAUSE:
                return node
        return None

    @property
    def solution_node(self) -> Optional[KnowledgeNode]:
        """Get the solution node from the path if it exists."""
        for node in self.nodes:
            if node.type == KnowledgeType.SOLUTION:
                return node
        return None

    @property
    def door_node(self) -> Optional[KnowledgeNode]:
        """Get the door type node from the path if it exists."""
        for node in self.nodes:
            if node.type == KnowledgeType.DOOR_TYPE:
                return node
        return None

    def is_complete(self) -> bool:
        """Check if the path has all required components (O, C, S)."""
        return all([self.observation_node, self.cause_node, self.solution_node])
