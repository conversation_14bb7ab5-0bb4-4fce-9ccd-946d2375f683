from collections import defaultdict
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from fastapi import Depends
from pydantic import BaseModel, Field


from app.core.config import get_settings
from app.core.exceptions import QueryProcessingError
from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgePath,
    KnowledgeRelationship,
    KnowledgeType,
    RelationshipType,
)
from app.services.nlp.query.door_service import DoorService
from app.services.nlp.gpt import GptProcessor
from app.services.nlp.query import prompts

from app.core.logging import log_retrieval

logger = log_retrieval()

# Pydantic models for verification
class CauseData(BaseModel):
    text: str


class PathToVerify(BaseModel):
    path_id: int
    causes: List[CauseData]


class PathVerificationModel(BaseModel):
    invalid_path_ids: List[int]


# More detailed verification models if needed
class ModifiedNode(BaseModel):
    type: str
    is_valid: bool = True
    action: Optional[str] = None
    reason: Optional[str] = None


class DetailedVerifiedPath(BaseModel):
    path_id: int
    is_valid: bool = True
    invalid_reason: Optional[str] = None
    modified_nodes: List[ModifiedNode] = Field(default_factory=list)


class DetailedVerificationModel(BaseModel):
    verified_paths: List[DetailedVerifiedPath]




class LLMClient:
    """
    Client for interacting with language models like OpenAI's GPT.
    Handles entity extraction, answer generation, and knowledge need detection.
    """

    def __init__(
        self,
        settings=Depends(get_settings),
        door_service: DoorService = Depends(),
    ):
        self.settings = settings
        self.door_service = door_service
        self.openai_client = None
        self.gpt_processor = GptProcessor(settings=settings)

    async def extract_entities_and_intent(
        self, query_text: str, context_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Extract entities and determine intent from the query text using LLM.

        Args:
            query_text: The user's query text
            context_info: Optional additional context information

        Returns:
            Dictionary of extracted entities and intent
        """
        try:
            logger.info(f"Processing query: {query_text}")

            # Fetch door models from database to help with extraction
            door_models = await self.door_service.get_all_door_models()
            door_models_text = (
                ", ".join([f"{model}" for model in door_models])
                if door_models
                else "unknown"
            )

            # Retrieve the system and user prompts for entity extraction
            system_prompt, user_prompt = prompts.get_query_extraction_prompts(
                query_text=query_text, door_models_text=door_models_text
            )

            # Add context info if available
            if context_info:
                user_prompt += (
                    f"\n\nAdditional context information: {json.dumps(context_info)}"
                )

            extracted_data = self.gpt_processor(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                json_output=True,
                temperature=0.1,
            )

            # Flatten the structure for easier use
            result = {}
            result["query_intent"] = extracted_data.get("query_intent", "general_info")
            result["language"] = extracted_data.get(
                "language", "en"
            )  # Default to English instead of Dutch

            # Add entities
            entities = extracted_data.get("entities", {})
            for key, value in entities.items():
                if value:  # Only add non-empty values
                    result[key] = value

            logger.info(f"Final extracted entities: {result}")
            return result

        except json.JSONDecodeError:
            logger.error("Failed to parse LLM response as JSON")
            return await self.gpt_fallback_entity_extraction(query_text)
        except Exception as e:
            logger.error(f"Entity extraction error: {str(e)}")
            return await self.gpt_fallback_entity_extraction(query_text)

    async def gpt_fallback_entity_extraction(self, query_text: str) -> Dict[str, Any]:
        """
        Use GPT for fallback entity extraction with a simpler prompt.

        Args:
            query_text: The user's query text

        Returns:
            Dictionary of extracted entities and intent
        """
        try:
            # Simple system prompt for fallback
            system_prompt, user_prompt = prompts.gpt_fallback_entity_extraction_prompts(
                query_text=query_text
            )

            try:
                extracted_data = self.gpt_processor(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    json_output=True,
                    temperature=0.1,
                )

                # Ensure basic structure
                result = {
                    "query_intent": extracted_data.get("query_intent", "general_info"),
                    "language": extracted_data.get("language", "nl"),
                }

                # Add available entities
                for key in ["symptom", "problem", "door_model"]:
                    if key in extracted_data and extracted_data[key]:
                        result[key] = extracted_data[key]

                logger.info(f"Fallback extraction results: {result}")
                return result
            except json.JSONDecodeError:
                # Ultimate fallback
                return {
                    "query_intent": "general_info",
                    "language": "nl" if "deur" in query_text.lower() else "en",
                }

        except Exception as e:
            logger.error(f"GPT fallback extraction error: {str(e)}")
            # Ultimate fallback
            return {
                "query_intent": "general_info",
                "language": "nl" if "deur" in query_text.lower() else "en",
            }

    async def generate_answer(
        self,
        query_text: str,
        knowledge_paths: List[KnowledgePath],
        extracted_entities: Dict[str, Any],
        context_info: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Generate a natural language answer using LLM with strict anti-hallucination controls.
        Focuses on the single best OCS path for a clear, direct answer.

        Args:
            query_text: The user's query text
            knowledge_paths: List of knowledge paths to use for generating the answer
            extracted_entities: Dictionary of extracted entities and intent
            context_info: Optional additional context information

        Returns:
            Generated answer as a string, focused on the best single path
        """
        try:
            # Ensure we have paths to work with
            if not knowledge_paths:
                logger.info("No knowledge paths provided to generate_answer")
                return (
                    "Ik kon geen specifieke oplossing vinden voor dit probleem in onze kennisbank. "
                    "Je kunt het beste een senior monteur raadplegen of de handleiding van de fabrikant controleren."
                )

            # Add detailed path structure logging
            logger.info(f"Starting answer generation with {len(knowledge_paths)} paths")

            # Process each path to identify node types and enhance if needed
            enhanced_paths = []
            for path in knowledge_paths:
                # For paths that don't have proper OCS classification, infer types from position
                if all(node.type == KnowledgeType.GENERAL for node in path.nodes):
                    logger.info("Inferring node types for a path with all GENERAL nodes")

                    # Sort nodes to infer types (observation, cause, solution, door_type)
                    nodes = sorted(path.nodes, key=lambda n: n.id)

                    # Override types based on position - an educated guess
                    if len(nodes) >= 4:
                        # Assign types based on position (usually in this order)
                        new_nodes = []

                        # First node usually observation
                        observation_node = KnowledgeNode(
                            id=nodes[0].id,
                            type=KnowledgeType.OBSERVATION,
                            name=nodes[0].name,
                            description=nodes[0].description,
                            properties=nodes[0].properties,
                            created_at=nodes[0].created_at,
                            updated_at=nodes[0].updated_at,
                            created_by=nodes[0].created_by,
                        )
                        new_nodes.append(observation_node)

                        # Second node usually cause
                        cause_node = KnowledgeNode(
                            id=nodes[1].id,
                            type=KnowledgeType.CAUSE,
                            name=nodes[1].name,
                            description=nodes[1].description,
                            properties=nodes[1].properties,
                            created_at=nodes[1].created_at,
                            updated_at=nodes[1].updated_at,
                            created_by=nodes[1].created_by,
                        )
                        new_nodes.append(cause_node)

                        # Third node usually solution
                        solution_node = KnowledgeNode(
                            id=nodes[2].id,
                            type=KnowledgeType.SOLUTION,
                            name=nodes[2].name,
                            description=nodes[2].description,
                            properties=nodes[2].properties,
                            created_at=nodes[2].created_at,
                            updated_at=nodes[2].updated_at,
                            created_by=nodes[2].created_by,
                        )
                        new_nodes.append(solution_node)

                        # Fourth node usually door type
                        door_node = KnowledgeNode(
                            id=nodes[3].id,
                            type=KnowledgeType.DOOR_TYPE,
                            name=nodes[3].name,
                            description=nodes[3].description,
                            properties=nodes[3].properties,
                            created_at=nodes[3].created_at,
                            updated_at=nodes[3].updated_at,
                            created_by=nodes[3].created_by,
                        )
                        new_nodes.append(door_node)

                        # Create proper relationships between the nodes
                        relationships = []
                        relationships.append(
                            KnowledgeRelationship(
                                source_id=new_nodes[0].id,
                                target_id=new_nodes[1].id,
                                type=RelationshipType.OBSERVED_WITH,
                                trust_score=path.trust_score,
                                created_by="system",
                            )
                        )
                        relationships.append(
                            KnowledgeRelationship(
                                source_id=new_nodes[1].id,
                                target_id=new_nodes[2].id,
                                type=RelationshipType.RESOLVED_BY,
                                trust_score=path.trust_score,
                                created_by="system",
                            )
                        )
                        relationships.append(
                            KnowledgeRelationship(
                                source_id=new_nodes[2].id,
                                target_id=new_nodes[3].id,
                                type=RelationshipType.APPLIES_TO,
                                trust_score=path.trust_score,
                                created_by="system",
                            )
                        )

                        # Create a new path with inferred types
                        enhanced_path = KnowledgePath(
                            new_nodes, relationships, path.trust_score
                        )
                        enhanced_paths.append(enhanced_path)

                        logger.info(
                            f"Enhanced a path with inferred types: {[n.type.value for n in new_nodes]}"
                        )
                    else:
                        # Not enough nodes for a complete path
                        enhanced_paths.append(path)
                else:
                    # Path already has proper types, keep as is
                    enhanced_paths.append(path)

            # If we enhanced any paths, use them instead
            if enhanced_paths:
                knowledge_paths = enhanced_paths
                logger.info(
                    f"Using {len(enhanced_paths)} enhanced paths for answer generation"
                )

            # Sort paths by truste
            knowledge_paths.sort(key=lambda p: p.trust_score, reverse=True)

            # --------- FIND THE BEST SINGLE PATH ---------
            # First, look for a complete path with observation, cause, and solution
            best_path = None

            # First priority: Complete paths with all OCS components
            for path in knowledge_paths:
                has_obs = any(
                    node.type == KnowledgeType.OBSERVATION for node in path.nodes
                )
                has_cause = any(node.type == KnowledgeType.CAUSE for node in path.nodes)
                has_sol = any(
                    node.type == KnowledgeType.SOLUTION for node in path.nodes
                )

                if has_obs and has_cause and has_sol:
                    best_path = path
                    logger.info(
                        f"Found complete OCS path with trust: {path.trust_score:.2f}"
                    )
                    break

            # Second priority: If no complete path, use the highest trust path
            if not best_path and knowledge_paths:
                best_path = knowledge_paths[0]
                logger.info(
                    f"No complete paths found, using highest trust path: {best_path.trust_score:.2f}"
                )

            if not best_path:
                return (
                    "Ik kon geen specifieke oplossing vinden voor dit probleem in onze kennisbank. "
                    "Je kunt het beste een senior monteur raadplegen of de handleiding van de fabrikant controleren."
                )

            # Extract key components from the selected path
            observation = None
            cause = None
            solution = None
            door_type = None

            for node in best_path.nodes:
                if node.type == KnowledgeType.OBSERVATION:
                    observation = node
                elif node.type == KnowledgeType.CAUSE:
                    cause = node
                elif node.type == KnowledgeType.SOLUTION:
                    solution = node
                elif node.type == KnowledgeType.DOOR_TYPE:
                    door_type = node

            # Default to position-based inference if proper typing is missing
            if not observation and best_path.nodes:
                # First node is typically observation
                observation = best_path.nodes[0]
                logger.info("Using first node as observation based on position")

            if not cause and len(best_path.nodes) > 1:
                # Second node is typically cause
                cause = best_path.nodes[1]
                logger.info("Using second node as cause based on position")

            if not solution and len(best_path.nodes) > 2:
                # Third node is typically solution
                solution = best_path.nodes[2]
                logger.info("Using third node as solution based on position")

            # Generate answer using only the best path
            try:
                # Initialize system and user prompts focused on a single path
                system_prompt = """
                Je bent een technische ondersteuningsassistent voor draaideuren en roldeurmechanici.

                Geef een duidelijk, praktisch antwoord dat de mechanicus kan gebruiken om het probleem direct op te lossen.
                Gebruik alleen de informatie die beschikbaar is in het kennispad. Volg exact het observatie-oorzaak-oplossing pad.
                """

                user_prompt = f"""
                VRAAG: "{query_text}"

                KENNISPAD (OBSERVATIE-OORZAAK-OPLOSSING):

                """

                # Add observation
                if observation:
                    user_prompt += f"OBSERVATIE: {observation.name}\n"
                    if (
                        observation.description
                        and observation.description != observation.name
                    ):
                        user_prompt += f"Beschrijving: {observation.description}\n"
                else:
                    user_prompt += "OBSERVATIE: Niet beschikbaar\n"

                # Add cause
                if cause:
                    user_prompt += f"\nOORZAAK: {cause.name}\n"
                    if cause.description and cause.description != cause.name:
                        user_prompt += f"Beschrijving: {cause.description}\n"
                else:
                    user_prompt += "\nOORZAAK: Niet beschikbaar\n"

                # Add solution
                if solution:
                    user_prompt += f"\nOPLOSSING: {solution.name}\n"
                    if solution.description and solution.description != solution.name:
                        user_prompt += f"Beschrijving: {solution.description}\n"
                else:
                    user_prompt += "\nOPLOSSING: Niet beschikbaar\n"

                # Add door type if available
                if door_type:
                    user_prompt += f"\nDEURTYPE: {door_type.name}\n"

                # Add trust info
                user_prompt += f"\nBetrouwbaarheid: {best_path.trust_score:.0%}"

                # Add instructions for creating the answer
                user_prompt += """

                INSTRUCTIES:
                1. Geef een praktisch antwoord dat direct bruikbaar is om het probleem op te lossen.
                2. Structureer je antwoord duidelijk in Probleem -> Oorzaak -> Oplossing format.
                3. Beschrijf eerst kort het probleem zoals geobserveerd.
                4. Leg vervolgens de oorzaak uit in 1-2 zinnen.
                5. Geef daarna de stapsgewijze oplossing.
                6. Gebruik technische maar begrijpelijke taal voor een draaideurtechnicus.
                7. Houd het antwoord bondig en direct toepasbaar.
                8. Je mag alleen maar de gegeven informatie gebruiken voor je antwoord. Verzin niet zelf dingen.
                """

                # Call OpenAI API to generate the answer
                logger.info(f"Generating answer with LLM for a single best path")
                content = self.gpt_processor(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt,
                    json_output=False,
                    temperature=0.1,
                )

                if content:
                    logger.info(
                        f"Generated LLM answer (first 100 chars): {content[:100]}..."
                    )
                    return content

            except Exception as e:
                logger.error(f"LLM generation error, falling back to template: {str(e)}")

            # Fallback to template-based answer with just the best path
            answer = []

            # Add trust info
            trust_info = f"(Betrouwbaarheid: {best_path.trust_score:.0%})"

            # Create structured response
            if observation:
                answer.append(f"**Probleem:**")
                answer.append(f"{observation.name}")
                if (
                    observation.description
                    and observation.description != observation.name
                ):
                    answer.append(f"{observation.description}")

            if cause:
                answer.append(f"\n**Oorzaak:**")
                answer.append(f"{cause.name}")
                if cause.description and cause.description != cause.name:
                    answer.append(f"{cause.description}")

            if solution:
                answer.append(f"\n**Oplossing:**")
                answer.append(f"{solution.name}")
                if solution.description and solution.description != solution.name:
                    answer.append(f"{solution.description}")

            # Add trust info at the end
            answer.append(f"\n{trust_info}")

            return "\n".join(answer)

        except Exception as e:
            logger.error(f"LLM answer generation error: {str(e)}")
            return (
                "Er is een fout opgetreden bij het genereren van een antwoord. "
                "Probeer het probleem opnieuw te beschrijven of raadpleeg een senior monteur."
            )

    async def verify_knowledge_paths(
        self,
        paths: List[KnowledgePath],
    ) -> List[KnowledgePath]:
        """
        Verify that each node in the knowledge paths correctly represents its declared type.
        Uses Pydantic models for structured data validation.

        Args:
            paths: List of knowledge paths to verify

        Returns:
            List of verified and potentially modified knowledge paths
        """
        if not paths:
            return []

        try:
            # Prepare a simple structure for verification
            paths_to_verify = []
            for i, path in enumerate(paths):
                path_data = {"path_id": i, "causes": []}

                for node in path.nodes:
                    if node.type == KnowledgeType.CAUSE:
                        cause_data = {
                            "text": (
                                node.name
                                + (f" {node.description}" if node.description else "")
                            )
                        }
                        path_data["causes"].append(cause_data)

                if path_data["causes"]:
                    paths_to_verify.append(path_data)

            # Skip verification if no causes to verify
            if not paths_to_verify:
                return paths

            system_prompt, user_prompt = prompts.get_knowledge_verification_prompts(
                paths_to_verify=json.dumps(paths_to_verify, ensure_ascii=False)
            )

            # Use the GPT processor with Pydantic model for structured validation
            logger.info(
                f"Verifying {len(paths_to_verify)} paths with structured output format"
            )

            # Use the with_pydantic method for better type safety
            verification_result = self.gpt_processor.with_pydantic(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                response_model=PathVerificationModel,
                temperature=0.1,
            )

            # Access attributes directly from the Pydantic model
            invalid_path_ids = verification_result.invalid_path_ids

            # Filter out invalid paths
            valid_paths = [
                path for i, path in enumerate(paths) if i not in invalid_path_ids
            ]

            # Log which paths were removed and why
            for id in invalid_path_ids:
                if id < len(paths):
                    cause_nodes = [
                        node
                        for node in paths[id].nodes
                        if node.type == KnowledgeType.CAUSE
                    ]
                    cause_names = [node.name for node in cause_nodes]
                    logger.info(
                        f"LLM verification removed path {id} with causes: {cause_names}"
                    )

            logger.info(
                f"Verification complete: {len(valid_paths)} valid paths remain out of {len(paths)} original paths"
            )
            return valid_paths

        except Exception as e:
            logger.error(f"Path verification error: {str(e)}")
            # Return original paths on error
            return paths

    def _apply_verification_results(
        self,
        original_paths: List[KnowledgePath],
        verification_results: Dict[str, Any],
    ) -> List[KnowledgePath]:
        """
        Apply verification results to original paths.

        Args:
            original_paths: Original knowledge paths
            verification_results: Verification results from LLM

        Returns:
            Updated knowledge paths
        """
        if "verified_paths" not in verification_results:
            logger.warning("Verification results missing 'verified_paths' key")
            return original_paths

        valid_paths = []

        for result in verification_results.get("verified_paths", []):
            path_id = result.get("path_id")
            if path_id is None or path_id >= len(original_paths):
                continue

            original_path = original_paths[path_id]

            # Check if the entire path is marked invalid
            if not result.get("is_valid", True):
                logger.info(
                    f"Path {path_id} marked invalid: {result.get('invalid_reason', 'No reason provided')}"
                )
                continue

            # Process node modifications
            modified_nodes = result.get("modified_nodes", [])
            should_skip_path = False

            if modified_nodes:
                # Check each node modification to see if it invalidates the path
                for mod in modified_nodes:
                    node_type = mod.get("type")
                    is_valid = mod.get("is_valid", True)

                    if not is_valid:
                        action = mod.get("action")
                        reason = mod.get("reason", "No reason provided")

                        # Log the details of each invalid node
                        logger.info(
                            f"Path {path_id} has invalid {node_type} node: {reason}"
                        )

                        # If PROBLEM or CAUSE is invalid, the path should be skipped
                        if node_type in ["PROBLEM", "CAUSE"] and action == "remove":
                            should_skip_path = True
                            logger.info(
                                f"Path {path_id} will be skipped due to invalid {node_type}"
                            )
                            break

                        # Handle SOLUTION node modification
                        if node_type == "SOLUTION" and action == "modify":
                            for i, node in enumerate(original_path.nodes):
                                if node.type == KnowledgeType.SOLUTION:
                                    # Create a new solution node indicating the issue
                                    new_node = KnowledgeNode(
                                        id=node.id,
                                        type=node.type,
                                        name="No valid solution in the system",
                                        description="The system does not contain a valid solution for this issue.",
                                        properties=node.properties,
                                        created_at=node.created_at,
                                        updated_at=node.updated_at,
                                        created_by=node.created_by,
                                        embedding=node.embedding,
                                        embedding_updated_at=node.embedding_updated_at,
                                    )
                                    original_path.nodes[i] = new_node
                                    logger.info(
                                        f"Path {path_id} solution node modified to indicate invalid solution"
                                    )

            # Skip this path if any critical node is invalid
            if should_skip_path:
                continue

            # Manually check for common invalid cause patterns that might be missed
            invalid_cause_patterns = [
                "gemoderniseerd",
                "modernized",
                "derde melding",
                "third report",
                "geïnstalleerd",
                "installed",
                "keer dat dit probleem",
                "time this problem",
            ]

            for node in original_path.nodes:
                if node.type == KnowledgeType.CAUSE:
                    node_text = (node.name + " " + (node.description or "")).lower()
                    if any(
                        pattern.lower() in node_text
                        for pattern in invalid_cause_patterns
                    ):
                        logger.info(
                            f"Path {path_id} skipped due to manual pattern detection in cause: {node.name}"
                        )
                        should_skip_path = True
                        break

            if should_skip_path:
                continue

            # Add the valid (possibly modified) path to the result list
            valid_paths.append(original_path)
            logger.info(
                f"Path {path_id} passed verification and will be included in results"
            )

        return valid_paths
