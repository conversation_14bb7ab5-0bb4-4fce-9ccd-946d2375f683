import logging
from typing import Optional, List, Dict, Any
from fastapi import Depends

from app.services.database.repositories.door_repo import DoorRepository
from app.services.cache.door_cache import DoorTypeCache, get_door_cache

from app.core.logging import log_retrieval

logger = log_retrieval()

class DoorService:
    """
    Service for door-related operations, including model retrieval and type identification.
    Uses caching to improve performance for frequently requested door types.
    """

    def __init__(
        self,
        door_repo: DoorRepository,
        door_cache: DoorTypeCache = Depends(get_door_cache),
    ):
        self.door_repo = door_repo
        self.door_cache = door_cache

    async def get_all_door_models(self) -> List[str]:
        """
        Retrieve all door models from the database to help with entity extraction.

        Returns:
            List of door model strings
        """
        try:
            query = """
            MATCH (d:DOOR_TYPE)
            RETURN d.model as model, d.manufacturer as manufacturer
            """
            results = await self.door_repo.db.execute_query(query, {})

            door_models = []
            for result in results:
                manufacturer = result.get("manufacturer", "")
                model = result.get("model", "")
                if manufacturer and model:
                    door_models.append(f"{manufacturer} {model}")
                elif model:
                    door_models.append(model)

            return door_models
        except Exception as e:
            logger.error(f"Error retrieving door models: {str(e)}")
            return []

    async def get_door_type_by_model(self, model: str) -> Optional[Any]:
        """
        Get door type by model name from the database with caching.

        Args:
            model: Door model name

        Returns:
            Door type object if found, None otherwise
        """
        if not model:
            return None

        try:
            # Check cache first
            cached_door_type = self.door_cache.get(model)
            if cached_door_type:
                logger.info(f"Cache hit for door model: {model}")
                return cached_door_type

            # Try exact match first
            door_type = await self.door_repo.get_door_type_by_model(model)
            if door_type:
                logger.info(f"Found exact match for door model: {model}")
                self.door_cache.set(model, door_type)  # Cache the result
                return door_type

            # If exact match fails, try case-insensitive match
            query = """
            MATCH (d:DOOR_TYPE)
            WHERE toLower(d.model) = toLower($model)
            RETURN d
            """

            result = await self.door_repo.db.execute_query(
                query, {"model": model}
            )
            if result:
                logger.info(
                    f"Found case-insensitive match for door model: {model}"
                )
                door_type = self.door_repo._dict_to_door_type(result[0]["d"])
                self.door_cache.set(model, door_type)  # Cache the result
                return door_type

            # Try partial match as last resort
            query = """
            MATCH (d:DOOR_TYPE)
            WHERE toLower(d.model) CONTAINS toLower($model) OR $model CONTAINS toLower(d.model)
            RETURN d
            """

            result = await self.door_repo.db.execute_query(
                query, {"model": model}
            )
            if result:
                logger.info(
                    f"Found partial match for door model: {model} -> {result[0]['d'].get('model')}"
                )
                door_type = self.door_repo._dict_to_door_type(result[0]["d"])
                self.door_cache.set(model, door_type)  # Cache the result
                return door_type

            logger.warning(f"No door type found for model: {model}")
            self.door_cache.set(model, None)  # Cache the negative result too
            return None

        except Exception as e:
            logger.warning(f"Error getting door type by model: {str(e)}")
            return None
