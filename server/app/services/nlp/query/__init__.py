"""
Query Processing package for natural language understanding of door maintenance queries.

This package provides a modular, extensible system for processing natural language
maintenance queries about revolving doors, finding relevant knowledge paths,
and generating informative answers.
"""

from app.services.nlp.query.query_service import QueryProcessingService

# Re-export the main service class for easier imports
__all__ = ["QueryProcessingService"]
