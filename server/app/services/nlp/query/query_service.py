import logging
from typing import Dict, List, Any, Optional, Tuple
from fastapi import Depends

from app.core.config import get_settings
from app.core.exceptions import QueryProcessingError
from app.models.domain.knowledge import KnowledgePath, KnowledgeType
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)
from app.services.database.repositories.mechanic_repo import MechanicRepository
from app.services.database.repositories.door_repo import DoorRepository
from app.services.embeddings.embedding import EmbeddingService
from app.services.cache.door_cache import DoorTypeCache, get_door_cache
from app.services.nlp.query.llm_client import LLMClient
from app.services.nlp.query.search_service import SearchService
from app.services.nlp.query.path_processor import PathProcessor
from app.services.nlp.query.door_service import DoorService

from app.core.logging import log_retrieval
from app.core.session_logging import log_query_session, get_current_session_id

logger = log_retrieval()


class QueryProcessingService:
    """
    Service for processing natural language queries from junior mechanics.
    Coordinates the various specialized services to extract intent,
    search for relevant knowledge, and generate answers.

    Updated to work with the OCS (Observation -> Cause -> Solution) structure
    instead of the previous SPCS structure.
    """

    def __init__(
        self,
        settings=Depends(get_settings),
        knowledge_repo: KnowledgeRepository = Depends(),
        mechanic_repo: MechanicRepository = Depends(),
        door_repo: DoorRepository = Depends(),
        embedding_service: EmbeddingService = Depends(),
        door_cache: DoorTypeCache = Depends(get_door_cache),
    ):
        """
        Initialize the QueryProcessingService with its dependencies.

        Args:
            settings: Application settings
            knowledge_repo: Repository for knowledge graph operations
            mechanic_repo: Repository for mechanic data
            door_repo: Repository for door data
            embedding_service: Service for text embeddings
            door_cache: Cache for door types
        """
        self.settings = settings
        self.knowledge_repo = knowledge_repo
        self.mechanic_repo = mechanic_repo
        self.door_repo = door_repo
        self.embedding_service = embedding_service
        self.door_cache = door_cache

        # Initialize sub-services
        self.door_service = DoorService(door_repo, door_cache)
        self.path_processor = PathProcessor(
            knowledge_repo, mechanic_repo, embedding_service
        )
        self.llm_client = LLMClient(settings, self.door_service)
        self.search_service = SearchService(
            knowledge_repo, embedding_service, self.path_processor
        )

    async def process_query(
        self,
        query_text: str,
        door_type_id: Optional[str] = None,
        context_info: Optional[Dict[str, Any]] = None,
        answer_desired: Optional[bool] = True,
    ) -> Tuple[List[KnowledgePath], str]:
        """
        Process a natural language query and return knowledge paths.
        Updated for OCS structure with optimized door type handling and path verification.

        Args:
            query_text: The user's query text
            door_type_id: Optional door type ID for filtering
            context_info: Optional additional context information

        Returns:
            Tuple of (knowledge_paths, answer_text)
        """
        logger.info(
            f"Processing query with door_type_id: {door_type_id}, query text: {query_text}, context_info: {context_info}"
        )
        try:
            # Extract entities and query intent
            extracted_entities = await self.llm_client.extract_entities_and_intent(
                query_text, context_info
            )

            log_query_session(
                session_id=get_current_session_id(),
                extracted_entities=extracted_entities,
            )

            logger.info(
                f"Extracted entities: {extracted_entities}, door_type_id: {door_type_id}"
            )
            logger.debug(f"Passing LLMClient to search service for path verification")

            # Find relevant knowledge paths based on extracted entities and intent
            # Now searching for Observation nodes instead of Symptom nodes
            paths = await self.search_service.find_knowledge_paths(
                extracted_entities, door_type_id, self.llm_client
            )

            logger.info(f"Found {len(paths)} verified paths from primary search")

            if not paths:
                # If no paths found, try a more general search
                logger.info("No specific paths found")
                raise QueryProcessingError(
                    "I couldn't find a specific solution for this issue in our knowledge base. "
                    "You might want to consult with a senior mechanic or check the manufacturer's manual."
                )

            answer = None
            if answer_desired:
                # Generate a natural language answer
                answer = await self._generate_answer(
                    query_text, paths, extracted_entities, context_info
                )

            if paths:
                logger.info(f"Returning {len(paths)} paths with answer")

            return paths, answer

        except Exception as e:
            logger.error(f"Query processing error: {str(e)}")
            raise QueryProcessingError(f"Failed to process query: {str(e)}")

    async def process_observation_query(
        self,
        extracted_entities: Dict[str, Any],
        door_type_id: Optional[str] = None,
        context_info: Optional[Dict[str, Any]] = None,
        answer_desired: Optional[bool] = True,
    ) -> Tuple[List[KnowledgePath], str]:

        try:
            # Only try to extract door model if not already provided
            if not door_type_id and "door_model" in extracted_entities:
                door_model = extracted_entities["door_model"]
                door_type = await self.door_service.get_door_type_by_model(door_model)
                if door_type:
                    door_type_id = door_type.id
                    logger.debug(
                        f"Using door type ID {door_type_id} from extracted model {door_model}"
                    )

            logger.info(
                f"Extracted entities: {extracted_entities}, door_type_id: {door_type_id}"
            )
            logger.debug(f"Passing LLMClient to search service for path verification")

            # Find relevant knowledge paths based on extracted entities and intent
            # Now searching for Observation nodes instead of Symptom nodes
            paths = await self.search_service.find_knowledge_paths(
                extracted_entities, door_type_id, self.llm_client
            )

            logger.info(f"Found {len(paths)} verified paths from primary search")

            if not paths:
                # If no paths found, try a more general search
                logger.info("No specific paths found")
                raise QueryProcessingError(
                    "I couldn't find a specific solution for this issue in our knowledge base. "
                    "You might want to consult with a senior mechanic or check the manufacturer's manual."
                )

            answer = None
            if answer_desired:
                # Generate a natural language answer
                answer = await self._generate_answer(
                    query_text, paths, extracted_entities, context_info
                )

            if paths:
                logger.info(f"Returning {len(paths)} paths with answer")

            return paths, answer

        except Exception as e:
            logger.error(f"Query processing error: {str(e)}")
            raise QueryProcessingError(f"Failed to process query: {str(e)}")

    async def _generate_answer(
        self,
        query_text: str,
        knowledge_paths: List[KnowledgePath],
        extracted_entities: Dict[str, Any],
        context_info: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Generate a natural language answer based on knowledge paths.
        Updated for OCS structure.
        Attempts to use LLM first, falls back to template-based answer if needed.

        Args:
            query_text: The user's query text
            knowledge_paths: List of knowledge paths to use in answering
            extracted_entities: Dictionary of extracted entities and intent
            context_info: Optional additional context information

        Returns:
            Answer text as a string
        """
        try:
            # If no paths found, provide a GPT-enhanced fallback response
            if not knowledge_paths:
                logger.info("No knowledge paths or related nodes found")
                return (
                    "I couldn't find a specific solution for this issue in our knowledge base. "
                    "You might want to consult with a senior mechanic or check the manufacturer's manual."
                )

            # Try to use LLM for answer generation for found paths
            answer = await self.llm_client.generate_answer(
                query_text, knowledge_paths, extracted_entities, context_info
            )
            if answer:
                return answer

            # Fallback to template-based answer generation
            from app.models.domain.knowledge import KnowledgeType

            top_path = knowledge_paths[0]

            # Extract key nodes from the path - remains the same for OCS
            observation = None
            cause = None
            solution = None

            for node in top_path.nodes:
                if node.type == KnowledgeType.OBSERVATION:
                    observation = node
                elif node.type == KnowledgeType.CAUSE:
                    cause = node
                elif node.type == KnowledgeType.SOLUTION:
                    solution = node

            # Generate answer based on available nodes
            trust_note = f"(Trust: {top_path.trust_score:.0%})"

            # Determine query intent
            query_intent = extracted_entities.get("query_intent", "general_info")

            # Template responses - Updated for OCS terminology
            if query_intent == "observation_analysis" and observation and cause:
                return (
                    f"Based on the observation '{observation.name}', "
                    f"the likely cause is {cause.name}. {trust_note}\n\n"
                    f"{cause.description}"
                )

            elif query_intent == "cause_identification" and cause and solution:
                return (
                    f"The cause '{cause.name}' is typically "
                    f"resolved by {solution.name}. {trust_note}\n\n"
                    f"{solution.description}"
                )

            # Comprehensive answer with all available information
            answer_parts = []

            if observation:
                answer_parts.append(f"Observation: {observation.name}")
                answer_parts.append(f"{observation.description}\n")

                # Include observation attributes if available
                obs_attributes = []
                if getattr(observation, "visual_observation", None):
                    obs_attributes.append(f"Visual: {observation.visual_observation}")
                if getattr(observation, "auditory_observation", None):
                    obs_attributes.append(f"Sound: {observation.auditory_observation}")
                if getattr(observation, "positional_observation", None):
                    obs_attributes.append(
                        f"Position: {observation.positional_observation}"
                    )
                if getattr(observation, "error_codes", None):
                    obs_attributes.append(f"Error code: {observation.error_codes}")
                if (
                    getattr(observation, "related_parts", None)
                    and observation.related_parts
                ):
                    obs_attributes.append(
                        f"Related parts: {', '.join(observation.related_parts)}"
                    )

                if obs_attributes:
                    answer_parts.append(
                        "Details:\n- " + "\n- ".join(obs_attributes) + "\n"
                    )

            if cause:
                answer_parts.append(f"Cause: {cause.name}")
                answer_parts.append(f"{cause.description}\n")

            if solution:
                answer_parts.append(f"Solution: {solution.name}")
                answer_parts.append(f"{solution.description}")

                # Add parts information
                if solution.properties.get("parts_involved"):
                    parts_str = ", ".join(solution.properties.get("parts_involved", []))
                    answer_parts.append(f"Parts involved: {parts_str}")

                if solution.properties.get("part_numbers"):
                    numbers_str = ", ".join(solution.properties.get("part_numbers", []))
                    answer_parts.append(f"Part numbers: {numbers_str}")

            # Add trust note
            answer_parts.append(f"\n{trust_note}")

            return "\n".join(answer_parts)

        except Exception as e:
            logger.error(f"Answer generation error: {str(e)}")
            raise QueryProcessingError(f"Failed to generate answer: {str(e)}")

    async def detect_knowledge_need_and_fetch_info(
        self, transcript: str
    ) -> Dict[str, Any]:
        """
        Detect knowledge need and extract component from transcript.
        Unchanged as this functionality doesn't depend on the OCS/SPCS structure.

        Args:
            transcript: The user's transcript

        Returns:
            Dictionary with knowledge need information
        """
        return await self.llm_client.detect_knowledge_need_and_fetch_info(transcript)
