def get_query_extraction_prompts(
    query_text: str,
    door_models_text: list[str],
) -> str:

    system_prompt = f"""
        You are a knowledge extraction system for revolving door maintenance queries.
        Extract entities and intent from junior mechanic queries.
        Return a JSON object with extracted entities and query intent.

        IMPORTANT CONTEXT:
        - The query may be in Dutch
        - YOU ARE ONLY ALLOWED TO ANSWER IN DUTCH
        - Available door models in our database: {door_models_text}
        """

    # User prompt - updated for OCS structure
    user_prompt = f"""
    Extract entities and intent from this revolving door maintenance query:

    ```
    {query_text}
    ```
    Return a JSON object with the following structure:
    {{
        "query_intent": "observation_analysis", // One of: observation_analysis, cause_identification, solution_finding, general_info
        "entities": {{
            "observation": "", // A summary of all observations (symptoms/problems) related to the door
            "visual_observation": "", // Just what is visually observed about the door
            "auditory_observation": "", // What sounds are heard from the door
            "positional_observation": "", // Where on the door is the issue located
            "error_codes": "", // Any error codes mentioned
            "related_parts": [], // Parts of the door related to the issue. should start with a capital letter (e.g. Deurmotor)
            "cause": "", // Any cause mentioned
            "door_model": "", // Any door model mentioned - match to one in our database if possible
            "environment": "" // Any environment condition mentioned
        }},
        "language": "nl"
    }}

    IMPORTANT NOTES:
    - Match door models to our database when possible
    - Be flexible with observation descriptions - people describe problems in many different ways
    - Only include entities that are explicitly mentioned or strongly implied in the query
    """
    return system_prompt, user_prompt


def gpt_fallback_entity_extraction_prompts(query_text: str) -> str:
    system_prompt = """
        Je bent een technische assistent voor deurmonteurs. Haal belangrijke informatie uit deze vraag.
        Wees flexibel en probeer te begrijpen waar de monteur naar vraagt.
        """

    # Updated for OCS structure
    user_prompt = f"""
        Haal basisinformatie uit deze vraag over een deurprobleem:
        "{query_text}"
        Retourneer alleen een JSON-object met deze velden:
        - query_intent: Een van [observation_analysis, cause_identification, solution_finding, general_info]
        - observation: De hoofdobservatie (symptoom/probleem) van de deur
        - visual_observation: Wat er visueel wordt waargenomen
        - auditory_observation: Welke geluiden worden gehoord
        - cause: Mogelijke oorzaken genoemd
        - door_model: Elk genoemd deurmodel
        - language: Retourneer altijd in het Nederlands
        """

    return system_prompt, user_prompt


def get_generate_answer_promps(
    query_text: str, context_info: dict, extracted_entities: dict
) -> str:
    # Strict system prompt with anti-hallucination instructions - updated for OCS
    system_prompt = """
    You are a technical support assistant for revolving door mechanics.

    CRITICAL INSTRUCTIONS:
    1. ONLY use information explicitly provided in the knowledge paths below
    2. DO NOT invent, assume, or hallucinate ANY details not present in the provided information
    3. If a component (observation, cause, or solution) is missing, acknowledge the gap
    4. If no complete solution is provided, DO NOT create one - clearly state that the information is incomplete
    5. Use ONLY the observations, causes, and solutions mentioned in the knowledge paths
    6. Answer in the same language as the query
    7. If you don't have enough information to fully answer, say so explicitly

    Use technical terminology appropriately and provide specific information from the knowledge paths.
    """

    # Detailed user prompt with knowledge path components - updated for OCS
    user_prompt = f"""
    QUERY: "{query_text}"

    LANGUAGE: {extracted_entities.get('language', 'nl')}

    DOOR TYPE: {context_info.get('door_model', 'Not specified')}

    KNOWLEDGE PATHS (in order of relevance):
    """

    return system_prompt, user_prompt



def get_knowledge_verification_prompts(paths_to_verify: str) -> str:
    # Updated for OCS structure
    system_prompt = """
    You are verifying cause nodes in revolving door maintenance knowledge paths.

    A valid cause must explain WHY a problem occurs, such as:
    - "Worn bearings causing friction"
    - "Sensor misalignment"
    - "Power supply fluctuation"

    An invalid cause typically describes:
    - Maintenance history (e.g., "Door was modernized in February")
    - Frequency of reports (e.g., "This is the third report")
    - Installation dates (e.g., "Door was installed last year")

    For each path, determine if all cause nodes are valid. If any cause is invalid, the entire path is invalid.
    """

    user_prompt = f"""
    Review these paths with their cause nodes:

    {paths_to_verify}

    For each path, determine if all causes are valid (explain WHY something happens)
    or if any cause is invalid (states maintenance history, frequency, etc.).

    EXAMPLES OF INVALID CAUSES:
    - "Door is in februari gemoderniseerd, dit is de derde melding tot nu toe."
    - "Dit is al de vierde keer dat dit probleem is gemeld deze maand"
    - "Deur is vorig jaar geïnstalleerd"

    Provide your verification results in the specified format.
    """

    return system_prompt, user_prompt
