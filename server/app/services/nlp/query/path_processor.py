import logging
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
import uuid
import numpy as np
from fastapi import Depends

from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgePath,
    KnowledgeType,
    RelationshipType,
)
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)
from app.services.database.repositories.mechanic_repo import MechanicRepository
from app.services.embeddings.embedding import EmbeddingService
from app.core.logging import log_retrieval

logger = log_retrieval()

class PathProcessor:
    """
    Processes knowledge paths including extraction, finding paths for nodes, and ranking.
    Updated to work with the OCS structure instead of SPCS.
    """

    def __init__(
        self,
        knowledge_repo: KnowledgeRepository,
        mechanic_repo: MechanicRepository,
        embedding_service: EmbeddingService,
    ):
        self.knowledge_repo = knowledge_repo
        self.mechanic_repo = mechanic_repo
        self.embedding_service = embedding_service

    def extract_path_components(self, path_data):
        """
        Extract nodes and relationships from a Neo4j path.
        Enhanced to handle different formats of path data with better error handling.
        """
        try:
            # Determine the format type and use the appropriate extractor
            if (
                isinstance(path_data, dict)
                and "path_nodes" in path_data
                and "path_rels" in path_data
            ):
                return self._extract_from_explicit_format(path_data)
            elif isinstance(path_data, list):
                return self._extract_from_list_format(path_data)
            elif hasattr(path_data, "nodes"):
                return self._extract_from_neo4j_path(path_data)
            elif isinstance(path_data, dict) and "nodes" in path_data:
                return self._extract_from_nodes_dict(path_data)
            else:
                # Log unsupported format and return empty results
                logger.error(f"Unsupported path_data type: {type(path_data)}")
                return [], []
        except Exception as e:
            logger.error(f"Error extracting path components: {str(e)}")
            return (
                [],
                [],
            )  # Return empty lists on error for graceful degradation

    def _extract_from_explicit_format(self, path_data):
        """
        Extract nodes and relationships from explicit path_nodes/path_rels format.

        Args:
            path_data: Dictionary with 'path_nodes' and 'path_rels' keys

        Returns:
            Tuple of (nodes, relationships)
        """
        nodes = []
        relationships = []

        try:
            # Extract nodes
            for node in path_data["path_nodes"]:
                nodes.append(self.knowledge_repo._dict_to_knowledge_node(node))

            # Extract relationships
            for rel in path_data["path_rels"]:
                source_id = rel.start_node.id
                target_id = rel.end_node.id
                rel_type = RelationshipType(rel.type)
                relationships.append(
                    self.knowledge_repo._dict_to_knowledge_relationship(
                        rel, source_id, target_id, rel_type
                    )
                )

            logger.info(
                f"Extracted {len(nodes)} nodes and {len(relationships)} relationships from explicit format"
            )
            return nodes, relationships
        except Exception as e:
            logger.error(f"Error extracting from explicit format: {str(e)}")
            return [], []

    def _extract_from_list_format(self, path_data):
        """
        Extract nodes and relationships from list format with mixed content.

        Args:
            path_data: List containing node dictionaries and relationship strings

        Returns:
            Tuple of (nodes, relationships)
        """
        nodes = []
        relationships = []

        try:
            # Separate nodes and relationship strings
            node_items = []
            rel_items = []

            for item in path_data:
                if isinstance(item, dict):
                    node_items.append(item)
                elif isinstance(item, str) and any(
                    rel_type in item
                    for rel_type in [
                        "OBSERVED_WITH",
                        "RESOLVED_BY",
                        "APPLIES_TO",
                    ]
                ):
                    rel_items.append(item)
                    logger.info(f"Found relationship string: {item[:20]}...")

            logger.info(
                f"Extracted {len(node_items)} nodes and {len(rel_items)} relationship strings"
            )

            # Process nodes
            for node_dict in node_items:
                try:
                    # Determine node type - use OBSERVATION as default
                    node_type = KnowledgeType.OBSERVATION
                    if "type" in node_dict:
                        try:
                            node_type = KnowledgeType(node_dict["type"])
                        except ValueError:
                            logger.warning(
                                f"Invalid node type: {node_dict['type']}, using OBSERVATION"
                            )

                    # Use id from dictionary or generate a new one
                    node_id = node_dict.get("id", str(uuid.uuid4()))
                    # Use name and description from dictionary or default values
                    name = node_dict.get("name", "")
                    description = node_dict.get("description", "")

                    # Create a node
                    node = KnowledgeNode(
                        id=node_id,
                        type=node_type,
                        name=name,
                        description=description,
                        properties=node_dict,
                        created_at=datetime.fromisoformat(
                            node_dict.get(
                                "created_at", datetime.now().isoformat()
                            )
                        ),
                        updated_at=datetime.fromisoformat(
                            node_dict.get(
                                "updated_at", datetime.now().isoformat()
                            )
                        ),
                        created_by=node_dict.get("created_by", "system"),
                    )
                    nodes.append(node)
                except Exception as e:
                    logger.error(f"Error creating node from dict: {str(e)}")

            # Note: Relationship processing would be added here for completeness

            return nodes, relationships
        except Exception as e:
            logger.error(f"Error extracting from list format: {str(e)}")
            return [], []

    def _extract_from_neo4j_path(self, path_data):
        """
        Extract nodes and relationships from Neo4j Path objects.

        Args:
            path_data: Neo4j Path object with nodes and relationships attributes

        Returns:
            Tuple of (nodes, relationships)
        """
        nodes = []
        relationships = []

        try:
            # Extract nodes
            for node in path_data.nodes:
                nodes.append(self.knowledge_repo._dict_to_knowledge_node(node))

            # Extract relationships
            for rel in path_data.relationships:
                source_id = rel.start_node.id
                target_id = rel.end_node.id
                rel_type = RelationshipType(rel.type)
                relationships.append(
                    self.knowledge_repo._dict_to_knowledge_relationship(
                        rel, source_id, target_id, rel_type
                    )
                )

            logger.info(
                f"Extracted {len(nodes)} nodes and {len(relationships)} relationships from Neo4j Path"
            )
            return nodes, relationships
        except Exception as e:
            logger.error(f"Error extracting from Neo4j Path: {str(e)}")
            return [], []

    def _extract_from_nodes_dict(self, path_data):
        """
        Extract from dictionary with 'nodes' key.

        Args:
            path_data: Dictionary with 'nodes' and optionally 'relationships' keys

        Returns:
            Tuple of (nodes, relationships)
        """
        nodes = []
        relationships = []

        try:
            # Extract nodes
            for node in path_data["nodes"]:
                nodes.append(self.knowledge_repo._dict_to_knowledge_node(node))

            # Extract relationships if present
            if "relationships" in path_data:
                for rel in path_data["relationships"]:
                    source_id = rel.get("start_node_id")
                    target_id = rel.get("end_node_id")
                    rel_type = RelationshipType(rel.get("type"))
                    relationships.append(
                        self.knowledge_repo._dict_to_knowledge_relationship(
                            rel, source_id, target_id, rel_type
                        )
                    )

            logger.info(
                f"Extracted {len(nodes)} nodes and {len(relationships)} relationships from nodes dictionary"
            )
            return nodes, relationships
        except Exception as e:
            logger.error(f"Error extracting from nodes dictionary: {str(e)}")
            return [], []

    async def find_paths_for_nodes(
        self,
        nodes: List[KnowledgeNode],
        node_type: str,
        door_type_id: Optional[str] = None,
    ) -> List[KnowledgePath]:
        """
        Find knowledge paths for a list of nodes.
        Updated to work with OCS structure.

        Args:
            nodes: List of nodes to find paths for
            node_type: Type of node
            door_type_id: Optional door type ID for filtering

        Returns:
            List of knowledge paths
        """
        paths = []
        for node in nodes:
            try:
                logger.info(f"Finding paths for node: {node.name} ({node.type})")

                if node.type == KnowledgeType.OBSERVATION:
                    # For observations, get complete OCS paths
                    observation_paths = (
                        await self.knowledge_repo.get_knowledge_paths(
                            observation_name=node.name,
                            door_type_id=door_type_id,
                            limit=3,
                        )
                    )
                    if observation_paths:
                        paths.extend(observation_paths)
                        logger.info(
                            f"Found {len(observation_paths)} paths for observation: {node.name}"
                        )

                elif node.type == KnowledgeType.CAUSE:
                    # For causes, find paths with the generic search method
                    try:
                        cause_paths = await self.search_by_node(
                            node.type.value,
                            node.name,
                            door_type_id,
                            search_relationship_type="RESOLVED_BY",
                        )
                        if cause_paths:
                            paths.extend(cause_paths)
                            logger.info(
                                f"Found {len(cause_paths)} paths for cause: {node.name}"
                            )
                    except Exception as e:
                        logger.error(
                            f"Error finding paths for cause {node.name}: {str(e)}"
                        )

                elif node.type == KnowledgeType.SOLUTION:
                    # For solutions, find paths using observation -> cause -> solution
                    try:
                        solution_paths = await self.search_by_solution(
                            node.name,
                            door_type_id,
                        )
                        if solution_paths:
                            paths.extend(solution_paths)
                            logger.info(
                                f"Found {len(solution_paths)} paths for solution: {node.name}"
                            )
                    except Exception as e:
                        logger.error(
                            f"Error finding paths for solution {node.name}: {str(e)}"
                        )

            except Exception as e:
                logger.error(f"Error processing node {node.name}: {str(e)}")

        return paths

    async def search_by_node(
        self,
        node_type: str,
        node_name: str,
        door_type_id: Optional[str] = None,
        search_relationship_type: Optional[str] = None,
    ) -> List[KnowledgePath]:
        """
        Generic method to search knowledge paths starting from any node type.
        Updated to work with OCS structure.

        Args:
            node_type: Type of node to search for
            node_name: Name of the node
            door_type_id: Optional door type ID for filtering
            search_relationship_type: Optional relationship type for filtering

        Returns:
            List of knowledge paths
        """
        try:
            # Build query based on node type and relationship type
            if (
                node_type == "CAUSE"
                and search_relationship_type == "RESOLVED_BY"
            ):
                query = """
                MATCH path = (c:CAUSE)-[r1:RESOLVED_BY]->(sol:SOLUTION)
                WHERE c.name CONTAINS $node_name
                WITH path, c, sol,
                     r1.trust_score as trust1
                """
            elif node_type == "SOLUTION":
                query = """
                MATCH (s:SOLUTION)
                WHERE
                    (s.properties_json CONTAINS $node_name OR
                     s.description CONTAINS $node_name OR
                     s.name CONTAINS $node_name)
                """
                # Find paths leading to this solution
                path_query = """
                MATCH path = (o:OBSERVATION)-[r1:OBSERVED_WITH]->(c:CAUSE)-[r2:RESOLVED_BY]->(sol:SOLUTION)
                WHERE sol.id = $solution_id
                WITH path, r1.trust_score as conf1, r2.trust_score as conf2
                WITH path, (conf1 + conf2)/2 as path_trust
                ORDER BY path_trust DESC
                LIMIT 3
                RETURN path, path_trust
                """
                # Special handling for SOLUTION nodes
                params = {"node_name": node_name}
                if door_type_id:
                    query += """
                    MATCH (s)-[r:APPLIES_TO]->(dt:DOOR_TYPE {id: $door_type_id})
                    """
                    params["door_type_id"] = door_type_id

                query += """
                RETURN s LIMIT 10
                """

                # Execute query to find solutions
                solution_results = await self.knowledge_repo.db.execute_query(
                    query, params
                )

                # For each solution, find complete paths
                paths = []
                for solution_result in solution_results:
                    solution = self.knowledge_repo._dict_to_knowledge_node(
                        solution_result["s"]
                    )

                    path_results = await self.knowledge_repo.db.execute_query(
                        path_query, {"solution_id": solution.id}
                    )

                    # Parse paths
                    for path_result in path_results:
                        path_data = path_result["path"]
                        trust = path_result["path_trust"]

                        # Extract nodes and relationships
                        nodes, relationships = self.extract_path_components(
                            path_data
                        )
                        paths.append(
                            KnowledgePath(nodes, relationships, trust)
                        )

                return paths
            else:
                # Default generic query for any node type
                query = f"""
                MATCH path = (n:{node_type})-[r]->(m)
                WHERE n.name CONTAINS $node_name
                WITH path, r.trust_score as trust1
                """

            # Add door type filter if specified
            params = {"node_name": node_name}
            if door_type_id:
                if "SOLUTION" in query:
                    # Already handled for SOLUTION type
                    pass
                else:
                    query += """
                    MATCH (sol:SOLUTION)-[r_dt:APPLIES_TO]->(dt:DOOR_TYPE {id: $door_type_id})
                    WHERE sol IN nodes(path)
                    WITH path, trust1, r_dt.trust_score as trust2
                    """
                    params["door_type_id"] = door_type_id

            # Calculate trust score and return
            if "SOLUTION" in query:
                # Already handled for SOLUTION type
                pass
            elif door_type_id and "trust2" in query:
                query += """
                WITH path, (trust1 + trust2)/2 as path_trust
                ORDER BY path_trust DESC
                LIMIT 10
                RETURN path, path_trust
                """
            else:
                query += """
                WITH path, trust1 as path_trust
                ORDER BY path_trust
                LIMIT 10
                RETURN path, path_trust
                """

            # Execute query for non-SOLUTION node types
            if node_type != "SOLUTION":
                results = await self.knowledge_repo.db.execute_query(
                    query, params
                )
                results = await self.knowledge_repo.db.execute_query(
                    query, params
                )

                # Parse results into KnowledgePath objects
                paths = []
                for result in results:
                    path_data = result["path"]
                    trust = result["path_trust"]

                    # Extract nodes and relationships
                    nodes, relationships = self.extract_path_components(
                        path_data
                    )
                    paths.append(KnowledgePath(nodes, relationships, trust))

                return paths
            else:
                # Already returned for SOLUTION type
                return []

        except Exception as e:
            logger.error(f"Error in search by node: {str(e)}")
            return []

    async def search_by_solution(
        self,
        solution_name: str,
        door_type_id: Optional[str] = None,
    ) -> List[KnowledgePath]:
        """
        Search for paths that include a specific solution.

        Args:
            solution_name: Name of the solution
            door_type_id: Optional door type ID for filtering

        Returns:
            List of knowledge paths
        """
        try:
            # Find solutions first
            query = """
            MATCH (s:SOLUTION)
            WHERE s.name CONTAINS $solution_name OR s.description CONTAINS $solution_name
            """

            params = {"solution_name": solution_name}

            if door_type_id:
                query += """
                MATCH (s)-[r:APPLIES_TO]->(dt:DOOR_TYPE {id: $door_type_id})
                """
                params["door_type_id"] = door_type_id

            query += "RETURN s LIMIT 5"

            solution_results = await self.knowledge_repo.db.execute_query(
                query, params
            )
            solution_results = await self.knowledge_repo.db.execute_query(
                query, params
            )

            paths = []
            for solution_result in solution_results:
                solution = self.knowledge_repo._dict_to_knowledge_node(
                    solution_result["s"]
                )

                # Find paths leading to this solution
                path_query = """
                MATCH path = (o:OBSERVATION)-[r1:OBSERVED_WITH]->(c:CAUSE)-[r2:RESOLVED_BY]->(s:SOLUTION)
                WHERE s.id = $solution_id
                """

                if door_type_id:
                    path_query += """
                    MATCH (s)-[r3:APPLIES_TO]->(dt:DOOR_TYPE {id: $door_type_id})
                    WITH path, r1.trust_score as conf1, r2.trust_score as conf2, r3.trust_score as conf3
                    WITH path, (conf1 + conf2 + conf3)/3 as path_trust
                    """
                    path_params = {
                        "solution_id": solution.id,
                        "door_type_id": door_type_id,
                    }
                else:
                    path_query += """
                    WITH path, r1.trust_score as conf1, r2.trust_score as conf2
                    WITH path, (conf1 + conf2)/2 as path_trust
                    """
                    path_params = {"solution_id": solution.id}

                path_query += """
                ORDER BY path_trust DESC
                LIMIT 3
                RETURN path, path_trust
                """

                path_results = await self.knowledge_repo.db.execute_query(
                    path_query, path_params
                )

                for path_result in path_results:
                    path_data = path_result["path"]
                    trust = path_result["path_trust"]

                    # Extract nodes and relationships
                    nodes, relationships = self.extract_path_components(
                        path_data
                    )
                    paths.append(KnowledgePath(nodes, relationships, trust))

            return paths

        except Exception as e:
            logger.error(f"Error in search by solution: {str(e)}")
            return []

    async def find_related_nodes(
        self, door_model: str = "", observation: str = "", cause: str = ""
    ) -> List[Dict]:
        """
        Find related nodes even when complete paths aren't available.
        Updated to work with OCS structure instead of SPCS.

        Args:
            door_model: Optional door model for filtering
            observation: Optional observation for filtering
            cause: Optional cause for filtering

        Returns:
            List of related nodes
        """
        try:
            related_nodes = []

            # Prepare Cypher query to find relevant nodes
            query = """
            MATCH (n)
            WHERE (n:OBSERVATION OR n:CAUSE OR n:SOLUTION)
            """

            params = {}

            # Add filters based on available information
            where_clauses = []
            if door_model:
                where_clauses.append(
                    """
                (toLower(n.name) CONTAINS toLower($door_model) OR
                toLower(n.description) CONTAINS toLower($door_model) OR
                EXISTS((n)-[:APPLIES_TO]->(:DOOR_TYPE {model: $door_model})))
                """
                )
                params["door_model"] = door_model

            if observation:
                where_clauses.append(
                    """
                (toLower(n.name) CONTAINS toLower($observation) OR
                toLower(n.description) CONTAINS toLower($observation))
                """
                )
                params["observation"] = observation

            if cause:
                where_clauses.append(
                    """
                (toLower(n.name) CONTAINS toLower($cause) OR
                toLower(n.description) CONTAINS toLower($cause))
                """
                )
                params["cause"] = cause

            # Add WHERE clause if we have conditions
            if where_clauses:
                query += " AND (" + " OR ".join(where_clauses) + ")"

            query += " RETURN n, labels(n) as labels LIMIT 10"

            # Execute query
            results = await self.knowledge_repo.db.execute_query(query, params)

            # Process results
            for result in results:
                node_data = result["n"]
                node_data["labels"] = result["labels"]
                node = self.knowledge_repo._dict_to_knowledge_node(node_data)

                related_nodes.append(
                    {
                        "type": node.type.value,
                        "name": node.name,
                        "description": node.description,
                    }
                )

            return related_nodes

        except Exception as e:
            logger.error(f"Error finding related nodes: {str(e)}")
            return []

    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate semantic similarity between two text strings.

        Args:
            text1: First text
            text2: Second text

        Returns:
            Similarity score between 0 and 1
        """
        try:
            return self.embedding_service.calculate_similarity(text1, text2)
        except Exception as e:
            logger.error(f"Error calculating text similarity: {str(e)}")
            # Fall back to simple substring matching
            text1_lower = text1.lower()
            text2_lower = text2.lower()
            return (
                1.0
                if text1_lower in text2_lower or text2_lower in text1_lower
                else 0.0
            )
