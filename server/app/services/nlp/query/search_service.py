from datetime import datetime
import logging
import json
import uuid
import numpy as np
import traceback
from typing import Dict, List, Any, Optional, Tuple, Callable
from fastapi import Depends
import asyncio
import itertools

from app.core.exceptions import QueryProcessingError
from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgePath,
    KnowledgeRelationship,
    KnowledgeType,
    RelationshipType,
)
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)
from app.services.embeddings.embedding import EmbeddingService
from app.services.nlp.query.path_processor import PathProcessor
from app.core.decorators import timer

from typing import List, Dict
from app.core.logging import log_retrieval

from app.services.nlp.extraction.constants import TrustValues, SearchConstants

# Setup logging for retrieval pipeline
logger = log_retrieval()


class SearchService:
    """
    Service for searching the knowledge graph using semantic similarity and other methods.
    This is the optimized version with improved multi-embedding search.
    """

    def __init__(
        self,
        knowledge_repo: KnowledgeRepository,
        embedding_service: EmbeddingService,
        path_processor: PathProcessor,
    ):
        self.knowledge_repo = knowledge_repo
        self.embedding_service = embedding_service
        self.path_processor = path_processor
        self.constants = SearchConstants

        logger.info("SearchService initialized")

    @timer()
    async def find_knowledge_paths(
        self,
        extracted_entities: Dict[str, Any],
        door_type_id: Optional[str] = None,
        llm_client=None,  # LLMClient instance
    ) -> List[KnowledgePath]:
        """
        Find knowledge paths based on extracted entities and verify their validity.
        Combines embedding search with attribute matching for observations.
        Optimized to return only top paths with improved performance.
        """

        logger.debug(
            f"Searching for knowledge paths with door_type_id: {door_type_id} and extracted entities: {extracted_entities}"
        )

        #  Generate embeddings and extract observation attributes as class attributes
        await self._prepare_search_data(extracted_entities, door_type_id)

        paths = await self.tiered_observation_search()

        self._remove_embeddings_from_paths(paths)
        self._analyze_path_structure(paths)

        if paths and llm_client and self.constants.ENABLE_LLM_VERIFICATION:
            try:
                logger.debug(
                    f"Verifying {len(paths)} knowledge paths with structured output"
                )
                verified_paths = await llm_client.verify_knowledge_paths(paths)
                self._analyze_verification_results(
                    verified_paths, original_count=len(paths)
                )
                return verified_paths[: self.constants.MAX_PATHS]
            except Exception as e:
                logger.error(f"Path verification failed: {str(e)}")
                return paths[: self.constants.MAX_PATHS]

        return paths[: self.constants.MAX_PATHS]

    @timer()
    async def _prepare_search_data(
        self,
        extracted_entities: Dict[str, Any],
        door_type_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Extract observation attributes from entities and generate embeddings."""
        logger.debug(f"Observation attributes before extraction: {extracted_entities}")
        observation_attributes = {}
        logger.debug(f"Extracted entities: {extracted_entities}")

        # Use the actual attribute names from constants that match the extracted entities
        logger.debug(f"Checking for present attributes in extracted entities")
        # filter out any attributes that are not in the observation attributes
        for attr in self.constants.OBSERVATION_ATTRIBUTES:
            if attr in extracted_entities and extracted_entities[attr]:
                observation_attributes[attr] = extracted_entities[attr]
                logger.debug(f"Extracted {attr}: {extracted_entities[attr]}")
            else:
                logger.debug(f"Did not find {attr} in extracted entities")

        logger.info(f"Extracted observation attributes: {observation_attributes}")

        # Extract observation text and generate embeddings only for provided attributes
        observation_text = extracted_entities.get("observation", "")

        # Build embedding tasks only for attributes that have content
        embedding_tasks = []
        embedding_keys = []

        # Always include observation if it exists
        if observation_text:
            embedding_tasks.append(self._generate_embedding_async(observation_text))
            embedding_keys.append("observation")

        # Only add tasks for attributes that were extracted
        attribute_mapping = {
            "visual_observation": "visual_embedding",
            "auditory_observation": "auditory_embedding",
            "positional_observation": "positional_embedding",
        }

        for attr_name, embedding_key in attribute_mapping.items():
            if attr_name in observation_attributes:
                logger.info("Generating embedding for attribute: %s", attr_name)
                embedding_tasks.append(
                    self._generate_embedding_async(observation_attributes[attr_name])
                )
                embedding_keys.append(embedding_key)

        # Generate embeddings in parallel (only for provided attributes)
        if embedding_tasks:
            embeddings = await asyncio.gather(*embedding_tasks)
            # Map results back to their corresponding keys
            embedding_results = dict(zip(embedding_keys, embeddings))
        else:
            embedding_results = {}

        # Set instance variables
        self.door_type_id = door_type_id
        self.observation_text = observation_text
        self.observation_embedding = embedding_results.get("observation")

        # Only set embeddings for attributes that were provided
        self.attribute_embeddings = {
            key: embedding_results.get(key)
            for key in [
                "visual_embedding",
                "auditory_embedding",
                "positional_embedding",
            ]
            if key in embedding_results
        }

        self.error_codes = observation_attributes.get("error_codes", "")
        self.related_parts = observation_attributes.get("related_parts", "")
        if not isinstance(self.related_parts, list):
            logger.warning(f"Related parts is not a list: {self.related_parts}")

        logger.info(f"Finished preparing search data")
        logger.info(f"Observation text: {self.observation_text}")
        logger.info(f"Observation embedding: {str(self.observation_embedding)[:100]}")
        logger.info(f"Attribute embeddings: {str(self.attribute_embeddings)[:100]}")
        # check if attribute embeddings are not None
        for attr_name, embedding in self.attribute_embeddings.items():
            if embedding is None:
                logger.debug(f"Embedding for {attr_name} is None")
            else:
                logger.debug(f"Embedding for {attr_name} is not None")
        logger.debug(f"Error code: {self.error_codes}")
        logger.debug(f"Related parts: {self.related_parts}")

    async def _generate_embedding_async(self, text: str) -> np.ndarray:
        """Generate embedding asynchronously."""
        return self.embedding_service.generate_embedding(text).tolist()

    def _remove_embeddings_from_paths(self, paths: List[KnowledgePath]) -> None:
        """Remove embeddings from all nodes in paths to make output cleaner to display in frontend."""
        for path in paths:
            for node in path.nodes:
                # Remove embedding-related properties from properties dict
                if hasattr(node, "properties") and isinstance(node.properties, dict):
                    for prop_key in list(node.properties.keys()):
                        if "embedding" in prop_key.lower():
                            node.properties[prop_key] = "N/A"

                # Remove embedding-related direct attributes
                for attr_name in dir(node):
                    if "embedding" in attr_name.lower() and not attr_name.startswith(
                        "_"
                    ):
                        try:
                            setattr(node, attr_name, "N/A")
                        except (AttributeError, TypeError):
                            # Skip if attribute is read-only or can't be set
                            pass

    def _analyze_path_structure(self, paths: List[KnowledgePath]) -> None:
        """Analyze and log the structure of found paths for debugging."""
        if not paths:
            logger.info("No paths found to analyze")
            return
        logger.info(f"Path structure analysis for {len(paths)} found paths:")
        for i, path in enumerate(paths[:3]):  # Analyze first 3 paths
            logger.info(f"Path {i+1} structure:")
            logger.info(f"  Total nodes: {len(path.nodes)}")
            node_types = [node.type.value for node in path.nodes]
            logger.info(f"  Node types: {node_types}")
            type_counts = {}
            for node in path.nodes:
                node_type = node.type.value
                type_counts[node_type] = type_counts.get(node_type, 0) + 1
            logger.info(f"  Node type counts: {type_counts}")
            completeness_info = self._check_path_completeness(path)
            logger.info(f"  Has observation: {completeness_info['has_observation']}")
            logger.info(f"  Has cause: {completeness_info['has_cause']}")
            logger.info(f"  Has solution: {completeness_info['has_solution']}")
            logger.info(f"  Path completeness: {completeness_info['status']}")
            self._log_node_content(path)

    def _check_path_completeness(self, path: KnowledgePath) -> Dict[str, Any]:
        """Check if a path has complete OCS (Observation-Cause-Solution) structure."""
        has_observation = any(
            node.type == KnowledgeType.OBSERVATION for node in path.nodes
        )
        has_cause = any(node.type == KnowledgeType.CAUSE for node in path.nodes)
        has_solution = any(node.type == KnowledgeType.SOLUTION for node in path.nodes)
        return {
            "has_observation": has_observation,
            "has_cause": has_cause,
            "has_solution": has_solution,
            "status": (
                "COMPLETE"
                if (has_observation and has_cause and has_solution)
                else "INCOMPLETE"
            ),
        }

    def _log_node_content(self, path: KnowledgePath) -> None:
        """Log content of the first few nodes in a path for debugging."""
        for j, node in enumerate(path.nodes[:2]):
            node_name = node.name[:50] + ("..." if len(node.name) > 50 else "")
            logger.info(f"  Node {j+1} ({node.type.value}): {node_name}")

    def _analyze_verification_results(
        self, verified_paths: List[KnowledgePath], original_count: int
    ) -> None:
        """Analyze and log the results of path verification."""
        if verified_paths:
            logger.info(
                f"Verification kept {len(verified_paths)}/{original_count} paths"
            )
            complete_verified_paths = 0
            for path in verified_paths[:3]:
                if self._check_path_completeness(path)["status"] == "COMPLETE":
                    complete_verified_paths += 1
            logger.info(
                f"Found {complete_verified_paths}/3 complete verified paths in first 3"
            )
            self._log_verification_quality_metrics(verified_paths)
        else:
            logger.info("No paths remained after verification")

    def _log_verification_quality_metrics(
        self, verified_paths: List[KnowledgePath]
    ) -> None:
        """Log quality metrics for verified paths."""
        if not verified_paths:
            return
        avg_confidence = sum(path.confidence_score for path in verified_paths) / len(
            verified_paths
        )
        logger.info(f"Average confidence of verified paths: {avg_confidence:.3f}")
        source_counts = {}
        for path in verified_paths:
            source = "Unknown"
            if hasattr(path, "properties") and path.properties:
                source = path.properties.get("search_source", "Unknown")
            source_counts[source] = source_counts.get(source, 0) + 1
        logger.info(f"Verified paths source distribution: {source_counts}")
        complete_count = sum(
            1
            for path in verified_paths
            if self._check_path_completeness(path)["status"] == "COMPLETE"
        )
        completion_rate = (complete_count / len(verified_paths)) * 100
        logger.info(
            f"Path completion rate: {completion_rate:.1f}% ({complete_count}/{len(verified_paths)})"
        )

    @timer()
    async def tiered_observation_search(
        self,
    ) -> List[KnowledgePath]:
        """
        New 2-Tier search strategy with permutation and semantic search.
        """

        # Using the new 2-tier structure
        tiers = [
            ("T1_PermutationSearch", self._tier_1_permutation_search),
            ("T2_SemanticSearch", self._tier_2_semantic_search),
        ]
        all_observations = []

        if self.door_type_id:
            logger.info("=== PHASE 1: Door-specific search ===")
            for tier_name, tier_func in tiers:
                observations = await tier_func(
                    self.door_type_id,
                    "DoorSpecific",
                )
                if observations:
                    all_observations.extend(observations)
                    logger.info(
                        f"{tier_name}_DoorSpecific: +{len(observations)} observations"
                    )
                if len(all_observations) >= self.constants.DEFAULT_SEARCH_LIMIT:
                    logger.info(
                        f"Early termination: Found enough observations from {tier_name}"
                    )
                    break

        if len(all_observations) < self.constants.DEFAULT_SEARCH_LIMIT:
            logger.info("=== PHASE 2: All-models search ===")
            for tier_name, tier_func in tiers:
                observations = await tier_func(
                    None,
                    "AllModels",
                )
                if observations:
                    all_observations.extend(observations)
                    logger.info(
                        f"{tier_name}_AllModels: +{len(observations)} observations"
                    )
                if len(all_observations) >= self.constants.DEFAULT_SEARCH_LIMIT:
                    logger.info(
                        f"Early termination: Found enough observations from {tier_name}"
                    )
                    break

        unique_observations = self._deduplicate_by_priority(all_observations)
        logger.info(f"Total unique observations: {len(unique_observations)}")

        sorted_observations = self._sort_by_priority(unique_observations)
        logger.info(f"Sorted observations by priority")

        final_observations = sorted_observations[
            : self.constants.MAX_OBSERVATIONS_PER_QUERY
        ]
        logger.info(
            f"Final observations after limiting to {self.constants.MAX_OBSERVATIONS_PER_QUERY}: {len(final_observations)}"
        )

        if final_observations:
            paths = await self._convert_observations_to_paths(
                final_observations,
            )
            self._log_source_distribution(paths)
            return paths

        logger.info("No observations found")
        return []

    def _sort_by_priority(
        self, observations: List[KnowledgeNode]
    ) -> List[KnowledgeNode]:
        """
        Sort observations by priority based on scope and similarity scores.
        Priority order:
        1. Scope (DoorSpecific > AllModels)
        2. Tier (T1 > T2)
        3. Best available similarity score (higher is better)
        """

        def get_priority_score(obs: KnowledgeNode) -> tuple:
            if not obs.properties:
                logger.error(f"Observation has no properties: {obs.name}")
                raise ValueError(f"Observation has no properties: {obs.name}")

            # 0. Tier priority (T1 > T2)
            tier = obs.properties.get("tier", "")
            if tier == "T1_PermutationSearch":
                tier_priority = 1
            elif tier == "T2_SemanticSearch":
                tier_priority = 2
            else:
                logger.warning(f"Unknown tier: {tier} for observation: {obs.name}")
                tier_priority = 3

            # 1. Scope priority (DoorSpecific is better than AllModels)
            scope = obs.properties.get("scope", "")
            if scope == "DoorSpecific":
                scope_priority = 1
            elif scope == "AllModels":
                scope_priority = 2
            else:
                logger.warning(f"Unknown scope: {scope} for observation: {obs.name}")
                scope_priority = 3

            # 2. Get the best similarity score available
            # Priority order: avg_similarity_score or similarity_score
            similarity_score = 0.0

            # Try avg_similarity_score first (from unified_search)
            if "avg_similarity_score" in obs.properties:
                similarity_score = obs.properties["avg_similarity_score"]

            # Fallback to similarity_score (from semantic search)
            elif "similarity_score" in obs.properties:
                similarity_score = obs.properties["similarity_score"]

            else:
                logger.warning(f"No similarity score found for observation: {obs.name}")

            # TODO: add source sorting as well (error codes...)
            return (scope_priority, tier_priority, similarity_score)

        sorted_obs = sorted(observations, key=get_priority_score, reverse=True)
        logger.info(f"Observations sorted by priority")
        return sorted_obs

    def _deduplicate_by_priority(
        self, observations: List[KnowledgeNode]
    ) -> List[KnowledgeNode]:
        """Remove duplicates, keeping the first occurrence to maintain priority."""
        seen_ids, unique_observations = set(), []
        for obs in observations:
            if obs.id not in seen_ids:
                unique_observations.append(obs)
                seen_ids.add(obs.id)
        return unique_observations

    # TIER IMPLEMENTATIONS
    @timer()
    async def _tier_1_permutation_search(
        self, door_type_id: Optional[str], scope: str
    ) -> List[KnowledgeNode]:
        """
        Decreasing permutation search by order of importance.
        Order: error_codes -> related_parts -> visual -> auditory -> positional
        Stops once we find enough observations.
        """
        for attr, value in self.attribute_embeddings.items():
            logger.info(f"Attribute embeddings '{attr}' value: {str(value)[:100]}")
        # Define attribute order by importance
        attribute_map = {
            "error_codes": self.error_codes,
            "related_parts": self.related_parts,
            "visual_observation": self.attribute_embeddings.get("visual_embedding"),
            "auditory_observation": self.attribute_embeddings.get("auditory_embedding"),
            "positional_observation": self.attribute_embeddings.get(
                "positional_embedding"
            ),
        }
        for attr, value in attribute_map.items():
            logger.info(f"Attribute '{attr}' value: {str(value)[:100]}")

        attribute_order = [
            (attr, attribute_map[attr])
            for attr in self.constants.ATTR_IMPORTANCE_ORDER
            if attr in attribute_map
        ]

        # logger.debug(f"Attribute order: {attribute_order}")

        # logger.debug(f"Attribute map: {attribute_map}")

        available_attributes = []
        for attr_name, attr_value in attribute_order:
            if attr_value is not None and attr_value != "" and attr_value != []:
                logger.debug(f"Available attribute added: {attr_name}")
                available_attributes.append((attr_name, attr_value))
            else:
                logger.debug(f"Skipped attribute {attr_name}")

        logger.info(
            f"T1_PermutationSearch: Available attributes: {[attr[0] for attr in available_attributes]}"
        )

        all_observations = []

        # Try all combinations, starting with longest (most specific) first
        for combo_length in range(len(available_attributes), 0, -1):
            for combo in itertools.combinations(available_attributes, combo_length):
                current_attributes = list(combo)

                attr_names = [attr[0] for attr in current_attributes]

                # Build source tracking string
                attr_string = "_".join(attr_names)

                logger.info(f"T1_PermutationSearch: Trying combination: {attr_names}")

                # Prepare parameters for unified_search
                search_params = {
                    "door_type_id": door_type_id,
                }

                logger.debug(f"Current attributes: {current_attributes}")

                # Map attributes to unified_search parameters
                for attr_name, attr_value in current_attributes:
                    logger.info(
                        f"T1_PermutationSearch: Setting search parameter for {attr_name}: {str(attr_value)[:100]}"
                    )
                    if attr_name == "error_codes":
                        search_params["error_codes"] = attr_value
                    elif attr_name == "related_parts":
                        search_params["related_parts"] = attr_value
                    elif attr_name == "visual_observation":
                        search_params["visual_embedding"] = attr_value
                    elif attr_name == "auditory_observation":
                        search_params["auditory_embedding"] = attr_value
                    elif attr_name == "positional_observation":
                        search_params["positional_embedding"] = attr_value

                logger.debug(f"Unified search params: {search_params}")
                for attr_name, attr_value in search_params.items():
                    logger.debug(f"Search param {attr_name}: {str(attr_value)[:100]}")
                try:
                    observations = await self.unified_search(**search_params)

                    if observations:
                        # Add source tracking and scores
                        for obs in observations:
                            if not obs.properties:
                                obs.properties = {}
                            obs.properties["tier"] = "T1_PermutationSearch"
                            obs.properties["source"] = (
                                attr_string  # e.g. error_code_related_parts_visual
                            )
                            obs.properties["scope"] = scope  # DoorSpecific or AllModels

                            # Calculate average similarity score from available scores
                            similarity_scores = []
                            for attr_name, _ in current_attributes:
                                if (
                                    attr_name == "visual_observation"
                                    and "visual_score" in obs.properties
                                ):
                                    similarity_scores.append(
                                        obs.properties["visual_score"]
                                    )
                                elif (
                                    attr_name == "auditory_observation"
                                    and "auditory_score" in obs.properties
                                ):
                                    similarity_scores.append(
                                        obs.properties["auditory_score"]
                                    )
                                elif (
                                    attr_name == "positional_observation"
                                    and "positional_score" in obs.properties
                                ):
                                    similarity_scores.append(
                                        obs.properties["positional_score"]
                                    )

                            if similarity_scores:
                                obs.properties["avg_similarity_score"] = sum(
                                    similarity_scores
                                ) / len(similarity_scores)
                            else:
                                obs.properties["avg_similarity_score"] = (
                                    self.constants.DEFAULT_SIMILARITY_FOR_EXACT_MATCH  # Default for exact matches
                                )

                        all_observations.extend(observations)
                        logger.info(
                            f"T1_PermutationSearch: Found {len(observations)} observations with {attr_names}"
                        )

                        # Stop if we have enough observations
                        if (
                            len(all_observations)
                            >= self.constants.REQUIRED_PATHS_PER_TIER
                        ):
                            logger.info(
                                f"T1_PermutationSearch: Early termination - found {len(all_observations)} observations"
                            )
                            break
                    else:
                        logger.info(
                            f"T1_PermutationSearch: No observations found with {attr_names}"
                        )

                except Exception as e:
                    logger.error(
                        f"T1_PermutationSearch: Error with combination {attr_names}: {str(e)}"
                    )
                    continue

        # Remove duplicates while preserving order (earlier combinations have priority)
        unique_observations = []
        seen_ids = set()
        for obs in all_observations:
            if obs.id not in seen_ids:
                unique_observations.append(obs)
                seen_ids.add(obs.id)

        logger.info(
            f"T1_PermutationSearch: Total unique observations: {len(unique_observations)}"
        )
        return unique_observations

    @timer()
    async def _tier_2_semantic_search(
        self, door_type_id: Optional[str], scope: str
    ) -> List[KnowledgeNode]:
        """
        Simple semantic search on observation text.
        """
        if not self.observation_text:
            logger.info("T2_SemanticSearch: No observation text available")
            return []

        if not self.observation_embedding:
            logger.info("T2_SemanticSearch: No observation embedding available")
            return []

        logger.info(
            f"T2_SemanticSearch: Searching with observation text: '{self.observation_text[:100]}...'"
        )

        try:
            observations = await self._semantic_search_by_text(
                self.observation_embedding,
                door_type_id,
            )

            # Add source tracking
            for obs in observations:
                if not obs.properties:
                    obs.properties = {}
                obs.properties["tier"] = "T2_SemanticSearch"
                obs.properties["source"] = "text_search"
                obs.properties["scope"] = scope

            logger.info(f"T2_SemanticSearch: Found {len(observations)} observations")
            return observations

        except Exception as e:
            logger.error(f"T2_SemanticSearch: Error during search: {str(e)}")
            return []

    @timer()
    async def _semantic_search_by_text(
        self,
        text_embedding: Optional[List[float]],
        door_type_id: Optional[str] = None,
        similarity_threshold: float = 0.5,
    ) -> List[KnowledgeNode]:
        """
        Helper function for semantic search on text using observation embeddings.
        """
        if not text_embedding:
            logger.warning("No embedding provided for semantic text search")
            return []

        try:
            # Build base query #TODO: should this be multiplied?
            limit_param = (
                self.constants.DEFAULT_SEARCH_LIMIT
                * self.constants.SEMANTIC_SEARCH_KNN_MULTIPLIER
            )

            query = """
                CALL db.index.vector.queryNodes('observation_embedding', $limit_param, $embedding)
                YIELD node, score
                WHERE score >= $similarity_threshold"""

            # Add door filter if provided
            if door_type_id:
                query += """
                    AND EXISTS((node)-[:OBSERVED_WITH]->(:CAUSE)-[:RESOLVED_BY]->(:SOLUTION)-[:APPLIES_TO]->(:DOOR_TYPE {model: $door_type_id}))"""

            query += """
                RETURN
                    node.id AS id,
                    node.name AS name,
                    node.description AS description,
                    node.visual_observation AS visual_observation,
                    node.auditory_observation AS auditory_observation,
                    node.positional_observation AS positional_observation,
                    node.error_codes AS error_codes,
                    node.related_parts AS related_parts,
                    node.created_at AS created_at,
                    node.updated_at AS updated_at,
                    score
                ORDER BY score DESC
                LIMIT $limit"""

            # Build parameters
            params = {
                "embedding": text_embedding,
                "similarity_threshold": similarity_threshold,
                "limit": self.constants.DEFAULT_SEARCH_LIMIT,
                "limit_param": limit_param,
            }

            if door_type_id:
                params["door_type_id"] = door_type_id

            logger.debug(f"Semantic search query: {query}")
            # logger.debug(f"Query parameters: {params}")

            results = await self.knowledge_repo.execute_read_query(query, params)
            logger.info(f"Found {len(results)} results from semantic text search")

            # Convert results to KnowledgeNode objects
            observations = []
            for result in results:
                observation_node = self._convert_result_to_observation_node(result)

                # Add similarity score to properties
                if not observation_node.properties:
                    observation_node.properties = {}
                observation_node.properties["similarity_score"] = result.get(
                    "score", 0.0
                )

                observations.append(observation_node)

            return observations

        except Exception as e:
            logger.error(f"Error in semantic text search: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    # CORE SEARCH METHODS
    @timer()
    async def unified_search(
        self,
        error_codes: Optional[str] = None,
        related_parts: Optional[List[str]] = None,
        visual_embedding: Optional[List[float]] = None,
        auditory_embedding: Optional[List[float]] = None,
        positional_embedding: Optional[List[float]] = None,
        door_type_id: Optional[str] = None,
    ) -> List[KnowledgeNode]:
        """
        Unified search for Observation nodes that match ALL provided criteria:
        - Exact match on error_codes
        - Contains ANY of the related_parts (OR logic)
        - Above threshold similarity for each embedding type
        """
        try:
            logger.info(
                f"Unified search with criteria: error_codes={error_codes}, "
                f"related_parts={related_parts}, visual={visual_embedding is not None}, "
                f"auditory={auditory_embedding is not None}, positional={positional_embedding is not None}"
            )

            # Validate we have at least one search criterion
            if not any(
                [
                    error_codes,
                    related_parts,
                    visual_embedding,
                    auditory_embedding,
                    positional_embedding,
                ]
            ):
                logger.warning("No search criteria provided for unified search")
                return []

            # Build the query parts
            params = {"limit": self.constants.DEFAULT_SEARCH_LIMIT}
            where_conditions = []
            embedding_searches = []

            # Add exact match conditions
            if error_codes:
                where_conditions.append("node.error_codes = $error_codes")
                params["error_codes"] = error_codes

            if related_parts:
                if len(related_parts) == 1:
                    where_conditions.append("node.related_parts CONTAINS $related_part")
                    params["related_part"] = related_parts[0]
                else:
                    part_conditions = []
                    for i, part in enumerate(related_parts):
                        param_name = f"related_part_{i}"
                        part_conditions.append(
                            f"node.related_parts CONTAINS ${param_name}"
                        )
                        params[param_name] = part
                    where_conditions.append(f"({' OR '.join(part_conditions)})")

            # Add door type filter if specified
            if door_type_id:
                where_conditions.append(
                    "EXISTS((node)-[:OBSERVED_WITH]->(:CAUSE)-[:RESOLVED_BY]->(:SOLUTION)-[:APPLIES_TO]->(:DOOR_TYPE {model: $door_type_id}))"
                )
                params["door_type_id"] = door_type_id

            # Build embedding search configurations
            embedding_configs = [
                (
                    "visual",
                    visual_embedding,
                    self.constants.SIMILARITY_THRESHOLD_VISUAL,
                ),
                (
                    "auditory",
                    auditory_embedding,
                    self.constants.SIMILARITY_THRESHOLD_AUDITORY,
                ),
                (
                    "positional",
                    positional_embedding,
                    self.constants.SIMILARITY_THRESHOLD_POSITIONAL,
                ),
            ]

            for embedding_type, embedding_data, threshold in embedding_configs:
                if embedding_data:
                    embedding_searches.append(
                        {
                            "type": embedding_type,
                            "index": f"observation_{embedding_type}_embedding",
                            "embedding_param": f"{embedding_type}_embedding",
                            "threshold_param": f"{embedding_type}_threshold",
                            "score_alias": f"{embedding_type}_score",
                        }
                    )
                    params[f"{embedding_type}_embedding"] = embedding_data
                    params[f"{embedding_type}_threshold"] = threshold

            # Build the complete query
            if embedding_searches:
                if len(embedding_searches) == 1:
                    # Single embedding search
                    search = embedding_searches[0]
                    query = f"""
                        CALL db.index.vector.queryNodes('{search["index"]}', 1000, ${search["embedding_param"]})
                        YIELD node, score as {search["score_alias"]}
                        WHERE {search["score_alias"]} >= ${search["threshold_param"]}
                    """

                    # Add WHERE conditions for exact matches
                    if where_conditions:
                        query += f" AND {' AND '.join(where_conditions)}"

                    # Keep both the individual score AND avg_score
                    query += f"""
                        WITH node, {search["score_alias"]}, {search["score_alias"]} as avg_score
                    """

                else:
                    # Multiple embedding searches - use sequential filtering
                    query = """
                        MATCH (node:OBSERVATION)
                    """

                    # Add exact match conditions first if they exist
                    if where_conditions:
                        query += f"""
                        WHERE {' AND '.join(where_conditions)}
                        """

                    # Now filter by each embedding search sequentially
                    for i, search in enumerate(embedding_searches):
                        query += f"""
                        WITH node{', ' + ', '.join([s['score_alias'] for s in embedding_searches[:i]]) if i > 0 else ''}
                        CALL db.index.vector.queryNodes('{search["index"]}', 1000, ${search["embedding_param"]})
                        YIELD node as emb_node, score as {search["score_alias"]}
                        WHERE emb_node = node AND {search["score_alias"]} >= ${search["threshold_param"]}
                        """

                    # Calculate average score from all embedding scores
                    score_list = [
                        f"COALESCE({s['score_alias']}, 0)" for s in embedding_searches
                    ]
                    all_scores = [s["score_alias"] for s in embedding_searches]
                    query += f"""
                        WITH node, {', '.join(all_scores)},
                            ({' + '.join(score_list)}) / {len(embedding_searches)} as avg_score
                    """
            else:
                # No embedding searches, just use exact matches
                query = """
                    MATCH (node:OBSERVATION)
                """
                if where_conditions:
                    query += f"""
                    WHERE {' AND '.join(where_conditions)}
                    """
                query += f"""
                    WITH node, {self.constants.DEFAULT_SIMILARITY_FOR_EXACT_MATCH} as avg_score
                """

            logger.info("Managed to get here.")
            # Add the return clause with proper score mapping
            visual_score_expr = "0 as visual_score"
            auditory_score_expr = "0 as auditory_score"
            positional_score_expr = "0 as positional_score"

            # Map the actual scores to the right variables
            for search in embedding_searches:
                logger.info(f"Processing search: {search}")
                if search["type"] == "visual":
                    visual_score_expr = (
                        f"COALESCE({search['score_alias']}, 0) as visual_score"
                    )
                elif search["type"] == "auditory":
                    auditory_score_expr = (
                        f"COALESCE({search['score_alias']}, 0) as auditory_score"
                    )
                elif search["type"] == "positional":
                    positional_score_expr = (
                        f"COALESCE({search['score_alias']}, 0) as positional_score"
                    )

            query += f"""
                RETURN
                    node.id AS id,
                    node.name AS name,
                    node.description AS description,
                    node.visual_observation AS visual_observation,
                    node.auditory_observation AS auditory_observation,
                    node.positional_observation AS positional_observation,
                    node.error_codes AS error_codes,
                    node.related_parts AS related_parts,
                    node.created_at AS created_at,
                    node.updated_at AS updated_at,
                    {visual_score_expr},
                    {auditory_score_expr},
                    {positional_score_expr},
                    avg_score
                ORDER BY avg_score DESC
                LIMIT $limit
            """

            logger.info(f"Unified search query: {query}")
            # logger.info(f"Query parameters: {params}")

            results = await self.knowledge_repo.execute_read_query(query, params)
            logger.info(f"Found {len(results)} results from unified search")

            # Convert results to KnowledgeNode objects
            observations = []
            for result in results:
                observation_node = self._convert_result_to_observation_node(result)

                # Add scores to properties
                if not observation_node.properties:
                    observation_node.properties = {}

                observation_node.properties["visual_score"] = result.get(
                    "visual_score", 0
                )
                observation_node.properties["auditory_score"] = result.get(
                    "auditory_score", 0
                )
                observation_node.properties["positional_score"] = result.get(
                    "positional_score", 0
                )
                observation_node.properties["avg_similarity_score"] = result.get(
                    "avg_score", 0
                )

                observations.append(observation_node)

            return observations

        except Exception as e:
            logger.error(f"Error in unified search: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def _convert_result_to_observation_node(
        self, result: Dict[str, Any]
    ) -> KnowledgeNode:
        """Convert a database query result to an observation KnowledgeNode."""
        related_parts_json = result.get("related_parts", "[]")
        try:
            related_parts = json.loads(related_parts_json) if related_parts_json else []
        except (json.JSONDecodeError, TypeError):
            related_parts = []

        return KnowledgeNode(
            id=result.get("id"),
            type=KnowledgeType.OBSERVATION,
            name=result.get("name", ""),
            description=result.get("description", ""),
            properties={
                "visual_observation": result.get("visual_observation", ""),
                "auditory_observation": result.get("auditory_observation", ""),
                "positional_observation": result.get("positional_observation", ""),
                "error_codes": result.get("error_codes", ""),
                "related_parts": related_parts,
            },
            created_at=datetime.fromisoformat(
                result.get("created_at", datetime.now().isoformat())
            ),
            updated_at=datetime.fromisoformat(
                result.get("updated_at", datetime.now().isoformat())
            ),
            created_by="system",
        )

    # PATH CONVERSION AND LOGGING

    @timer()
    async def _get_ranked_causes_with_solutions(
        self,
        observation_node: KnowledgeNode,
    ) -> List[KnowledgePath]:
        """
        For a given OBSERVATION:
        - Get all CAUSE nodes
        - For each CAUSE, get all SOLUTIONs and compute average RESOLVED_BY trust
        - Return top N CAUSEs by average trust
        - For each CAUSE, return top M SOLUTIONs by trust
        """
        try:
            logger.info(
                f"Starting _get_ranked_causes_with_solutions for observation: {observation_node.id} (name: {observation_node.name})"
            )
            logger.info(
                f"Parameters - cause_limit: {self.constants.MAX_CAUSE_PER_OBSERVATION}, solution_limit: {self.constants.MAX_SOLUTIONS_PER_CAUSE}"
            )

            # Execute main query
            results = await self._execute_causes_solutions_query(observation_node)

            # Process results into grouped data
            grouped = self._process_query_results(results, observation_node)

            # Get additional trust data for verification
            await self._log_trust_verification(observation_node, grouped)

            # Create and return knowledge paths
            paths = self._create_knowledge_paths(grouped)

            logger.info(f"Returning {len(paths)} knowledge paths")
            return paths

        except Exception as e:
            logger.error(f"Error processing ranked causes/solutions: {str(e)}")
            return []

    async def _execute_causes_solutions_query(self, observation_node: KnowledgeNode):
        """Execute the main Cypher query to get ranked causes with solutions."""
        query = """
        MATCH (o:OBSERVATION {id: $observation_id})-[:OBSERVED_WITH]->(c:CAUSE)
        MATCH (c)-[r:RESOLVED_BY]->(s:SOLUTION)

        // Calculate the overall average trust for each cause (on ALL solutions)
        WITH o, c, avg(coalesce(r.trust_score, 0.0)) AS overall_avg_trust
        ORDER BY overall_avg_trust DESC
        LIMIT $cause_limit

        // Now get the top solutions for each selected cause
        MATCH (c)-[r:RESOLVED_BY]->(s:SOLUTION)
        WITH o, c, s, r.trust_score AS trust_score, overall_avg_trust
        ORDER BY c.id, trust_score DESC

        // Group by cause and take top N solutions per cause
        WITH c, overall_avg_trust, collect({solution: s, trust: trust_score})[0..$solution_limit] AS top_solutions

        UNWIND top_solutions AS solution_data
        WITH c, solution_data.solution AS s, solution_data.trust AS trust_score, overall_avg_trust

        OPTIONAL MATCH (s)-[:APPLIES_TO]->(d:DOOR_TYPE)
        RETURN
            c.id AS cause_id, c.name AS cause_name, c.description AS cause_description,
            s.id AS solution_id, s.name AS solution_name, s.description AS solution_description,
            trust_score,
            overall_avg_trust,
            d.id AS door_type_id, d.name AS door_type_name, d.description AS door_type_description
        ORDER BY overall_avg_trust DESC, trust_score DESC
        """
        params = {
            "observation_id": observation_node.id,
            "cause_limit": self.constants.MAX_CAUSE_PER_OBSERVATION,
            "solution_limit": self.constants.MAX_SOLUTIONS_PER_CAUSE,
        }

        results = await self.knowledge_repo.execute_read_query(query, params)
        logger.info(f"Query returned {len(results)} results")

        # Debug: Log the first result to see what fields are returned
        if results:
            logger.info(f"First result keys: {list(results[0].keys())}")
            logger.info(f"First result values: {dict(results[0])}")

        return results

    def _process_query_results(self, results, observation_node: KnowledgeNode):
        """Process query results into grouped data structure."""
        from collections import defaultdict

        grouped = defaultdict(
            lambda: {
                "nodes": [observation_node],
                "solutions": [],
                "trust_scores": [],
                "overall_avg_trust": TrustValues.DEFAULT_TRUST_SCORE,
            }
        )

        # Log each result as we process it
        for i, r in enumerate(results):
            cause_id = r["cause_id"]
            trust_score = r.get("trust_score") or TrustValues.DEFAULT_TRUST_SCORE
            overall_avg_trust = (
                r.get("overall_avg_trust") or TrustValues.DEFAULT_TRUST_SCORE
            )

            logger.info(
                f"Processing result {i+1}: Cause '{r['cause_name']}' (ID: {cause_id}) -> Solution '{r['solution_name']}' (ID: {r['solution_id']}) with trust_score: {trust_score}, overall_avg_trust: {overall_avg_trust:.4f}"
            )

            # Only add CAUSE node once
            if len(grouped[cause_id]["nodes"]) == 1:
                self._add_cause_node_to_group(grouped, cause_id, r)

            # Create and add solution node
            solution_node, door_node = self._create_solution_and_door_nodes(r)

            grouped[cause_id]["nodes"].append(solution_node)
            if door_node:
                grouped[cause_id]["nodes"].append(door_node)

            grouped[cause_id]["solutions"].append((solution_node, door_node))
            grouped[cause_id]["trust_scores"].append(trust_score)

        return grouped

    def _add_cause_node_to_group(self, grouped, cause_id: str, result_row):
        """Add a new cause node to the grouped data."""
        logger.info(
            f"Adding new cause node: {result_row['cause_name']} (ID: {cause_id})"
        )

        grouped[cause_id]["nodes"].append(
            KnowledgeNode(
                id=cause_id,
                type=KnowledgeType.CAUSE,
                name=result_row["cause_name"],
                description=result_row["cause_description"],
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat(),
                created_by="system",
            )
        )
        # Store the overall average trust calculated on ALL solutions
        grouped[cause_id]["overall_avg_trust"] = result_row.get(
            "overall_avg_trust", TrustValues.DEFAULT_TRUST_SCORE
        )

    def _create_solution_and_door_nodes(self, result_row):
        """Create solution and optional door type nodes from query result."""
        # Create SOLUTION node
        solution_node = KnowledgeNode(
            id=result_row["solution_id"],
            type=KnowledgeType.SOLUTION,
            name=result_row["solution_name"],
            description=result_row["solution_description"],
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat(),
            created_by="system",
        )

        # Optional DOOR_TYPE node
        door_node = None
        if result_row["door_type_id"]:
            logger.info(
                f"Adding door type: {result_row['door_type_name']} (ID: {result_row['door_type_id']}) for solution {result_row['solution_name']}"
            )
            door_node = KnowledgeNode(
                id=result_row["door_type_id"],
                type=KnowledgeType.DOOR_TYPE,
                name=result_row["door_type_name"] or "",
                description=result_row["door_type_description"] or "",
                properties={},
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat(),
                created_by="system",
            )

        return solution_node, door_node

    async def _log_trust_verification(self, observation_node: KnowledgeNode, grouped):
        """Execute verification query and log detailed trust score information."""
        # Get all trust scores for each cause for logging verification
        all_trust_scores_query = """
        MATCH (o:OBSERVATION {id: $observation_id})-[:OBSERVED_WITH]->(c:CAUSE)
        MATCH (c)-[r:RESOLVED_BY]->(s:SOLUTION)
        WITH c, collect({solution_name: s.name, trust: coalesce(r.trust_score, 0.0)}) AS all_solutions
        WHERE c.id IN $cause_ids
        RETURN c.id AS cause_id, c.name AS cause_name, all_solutions
        """

        cause_ids = list(grouped.keys())
        all_trust_lookup = {}

        if cause_ids:
            all_trust_results = await self.knowledge_repo.execute_read_query(
                all_trust_scores_query,
                {
                    "observation_id": observation_node.id,
                    "cause_ids": cause_ids,
                },
            )

            # Create a lookup for all trust scores by cause
            for result in all_trust_results:
                cause_id = result["cause_id"]
                all_solutions = result["all_solutions"]
                all_trust_lookup[cause_id] = all_solutions

        # Log grouped data and calculate averages
        self._log_detailed_group_information(grouped, all_trust_lookup)

    def _log_detailed_group_information(self, grouped, all_trust_lookup):
        """Log detailed information about grouped data for debugging."""
        logger.info(f"Grouped data for {len(grouped)} causes:")

        for cause_id, data in grouped.items():
            cause_name = data["nodes"][1].name if len(data["nodes"]) > 1 else "Unknown"
            trust_scores = data["trust_scores"]
            limited_avg_score = (
                sum(trust_scores) / len(trust_scores)
                if trust_scores
                else TrustValues.DEFAULT_TRUST_SCORE
            )
            overall_avg_trust = data["overall_avg_trust"]

            logger.info(f"  Cause: '{cause_name}' (ID: {cause_id})")

            # Show ALL trust scores for this cause
            if cause_id in all_trust_lookup:
                self._log_trust_score_details(
                    cause_id, all_trust_lookup, overall_avg_trust
                )

            logger.info(
                f"    Trust scores (top {len(trust_scores)} returned): {trust_scores}"
            )
            logger.info(f"    Average of returned solutions: {limited_avg_score:.4f}")
            logger.info(f"    Number of returned solutions: {len(data['solutions'])}")

            self._log_returned_solutions(data["solutions"])

    def _log_trust_score_details(self, cause_id, all_trust_lookup, overall_avg_trust):
        """Log detailed trust score information for verification."""
        all_solutions = all_trust_lookup[cause_id]
        all_trusts = [sol["trust"] for sol in all_solutions]
        manual_avg = (
            sum(all_trusts) / len(all_trusts)
            if all_trusts
            else TrustValues.DEFAULT_TRUST_SCORE
        )
        logger.info(f"    ALL trust scores ({len(all_trusts)} solutions): {all_trusts}")
        logger.info(f"    Manual average verification: {manual_avg:.4f}")
        logger.info(f"    Query overall average: {overall_avg_trust:.4f}")

        # Show solution names with their trust scores
        for sol in all_solutions:
            logger.info(f"      - '{sol['solution_name']}': {sol['trust']}")

    def _log_returned_solutions(self, solutions):
        """Log information about returned solutions."""
        logger.info(f"    Returned solutions:")
        for j, (solution_node, door_node) in enumerate(solutions):
            door_info = f" -> Door: {door_node.name}" if door_node else ""
            logger.info(f"      {j+1}. '{solution_node.name}'{door_info}")

    def _create_knowledge_paths(self, grouped):
        """Create KnowledgePath objects from grouped data and sort them."""
        paths = []

        for data in grouped.values():
            nodes = data["nodes"]
            trust_scores = data["trust_scores"]
            overall_avg_trust = data["overall_avg_trust"]

            relationships = self._create_relationships_for_nodes(nodes, trust_scores)

            # Use the overall average trust score calculated on ALL solutions
            paths.append(KnowledgePath(nodes, relationships, overall_avg_trust))

        # Sort paths by trust score in descending order (highest first)
        paths.sort(key=lambda path: path.trust_score, reverse=True)
        logger.info(f"Sorted paths by trust score in descending order")

        # Log final ranking
        self._log_final_ranking(paths)

        return paths

    def _create_relationships_for_nodes(self, nodes, trust_scores):
        """Create relationships between nodes in the correct order."""
        relationships = []

        for i in range(len(nodes) - 1):
            rel_type = (
                RelationshipType.OBSERVED_WITH
                if i == 0
                else (
                    RelationshipType.RESOLVED_BY
                    if i == 1
                    else RelationshipType.APPLIES_TO
                )
            )
            trust_score_for_rel = (
                trust_scores[i - 1] if rel_type == RelationshipType.RESOLVED_BY else 1.0
            )
            relationships.append(
                KnowledgeRelationship(
                    source_id=nodes[i].id,
                    target_id=nodes[i + 1].id,
                    type=rel_type,
                    trust_score=trust_score_for_rel,
                )
            )

        return relationships

    def _log_final_ranking(self, paths):
        """Log the final ranking of knowledge paths."""
        logger.info(f"Final ranking of {len(paths)} knowledge paths:")

        for i, path in enumerate(paths):
            cause_name = path.nodes[1].name if len(path.nodes) > 1 else "Unknown"

            logger.info(
                f"  Rank {i+1}: Cause '{cause_name}' with overall average trust score (ALL solutions): {path.trust_score:.4f}"
            )

            logger.info(
                f"    Path contains {len(path.nodes)} nodes and {len(path.relationships)} relationships"
            )

    @timer()
    async def _convert_observations_to_paths(
        self,
        observations: List[KnowledgeNode],
    ) -> List[KnowledgePath]:
        """
        Convert observations to paths concurrently, with limits per observation.
        """
        logger.info(f"Converting {len(observations)} observations to paths)")
        logger.debug(f"Observations to convert: {observations}")
        path_tasks = [
            self._get_ranked_causes_with_solutions(obs) for obs in observations
        ]
        path_results = await asyncio.gather(*path_tasks, return_exceptions=True)

        # Process results and collect valid paths
        all_paths = self._collect_valid_paths(path_results, observations)

        # Sort and return top paths
        all_paths.sort(key=lambda p: p.trust_score, reverse=True)
        return all_paths[: self.constants.MAX_PATHS]

    def _collect_valid_paths(
        self, path_results: List, observations: List[KnowledgeNode]
    ) -> List[KnowledgePath]:
        """Process path results and collect all valid paths with source info."""
        all_paths = []

        for i, result in enumerate(path_results):
            if isinstance(result, Exception):
                logger.error(
                    f"Error generating paths for observation {i}: {str(result)}"
                )
                continue

            if not result:
                continue

            # Extract source info from observation
            obs = observations[i]
            source_info = (
                obs.properties.get("search_source", "Unknown")
                if obs.properties
                else "Unknown"
            )

            # Add source info to all paths and collect them
            for path in result:
                if not hasattr(path, "properties"):
                    path.properties = {}
                path.properties["search_source"] = source_info
                all_paths.append(path)

        return all_paths

    def _log_source_distribution(self, paths: List[KnowledgePath]) -> None:
        """Log the distribution of sources in final results."""
        source_counts = {}
        for path in paths:
            source = (
                path.properties.get("search_source", "Unknown")
                if hasattr(path, "properties") and path.properties
                else "Unknown"
            )
            source = (
                path.properties.get("search_source", "Unknown")
                if hasattr(path, "properties") and path.properties
                else "Unknown"
            )
            source_counts[source] = source_counts.get(source, 0) + 1
        logger.info(f"Final source distribution - paths: {source_counts}")
        logger.info(f"Final path distribution - paths: {source_counts}")
