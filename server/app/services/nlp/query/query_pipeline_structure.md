# Query Processing Pipeline Analysis

## 1. Overview

The query processing pipeline is a sophisticated modular system designed to understand natural language queries from revolving door mechanics, find relevant knowledge in a graph database, and generate informative answers. The architecture follows a clean separation of concerns with specialized components handling different aspects of the query processing workflow.

The system leverages language models (like GPT) to extract meaningful entities from queries and then uses a multi-strategy search approach to find relevant knowledge paths in the **OCS (Observation, Cause, Solution)** framework, which has replaced the older SPCS framework in the core processing logic.

## 2. Architecture Components

```mermaid
graph TD
    A[Mechanic's Query] --> B[QueryProcessingService]
    B --> C[LLMClient]
    C --> D[Entity Extraction]
    D --> E[Search Service]
    E --> F[Knowledge Paths]
    F --> G[Path Processor]
    G --> H[Ranked Paths]
    H --> I[LLMClient]
    I --> J[Path Verification]
    J --> K[Answer Generation]
    B --> L[Door Service]
    L --> M[Door Type Information]
    E --> N[Embedding Service]
    N --> O[Semantic Search]
```

### 2.1 Key Components and Their Roles

1. **QueryProcessingService** (`query_service.py`)
   - Orchestrates the overall query processing workflow
   - Coordinates interactions between specialized services
   - Manages the flow from query text to answer generation

2. **LLMClient** (`llm_client.py`)
   - Handles interactions with language models
   - Extracts entities and intent from queries
   - Verifies knowledge paths for validity
   - Generates natural language answers

3. **SearchService** (`search_service.py`)
   - Implements multiple search strategies
   - Uses tiered fallback mechanisms for robustness
   - Combines semantic similarity with attribute matching
   - Optimizes search based on query specificity

4. **PathProcessor** (`path_processor.py`)
   - Extracts and processes components from knowledge paths
   - Finds paths for specific nodes in the knowledge graph
   - Ranks knowledge paths using a multi-factor algorithm
   - Calculates text similarity between queries and knowledge

5. **DoorService** (`door_service.py`)
   - Manages door-related information
   - Retrieves door models and types
   - Uses caching to improve performance

6. **Utilities** (`utils.py`)
   - Provides logging functions for consistent formatting
   - Implements performance monitoring with timer decorators
   - Offers helper functions for entity extraction

## 3. Query Processing Flow

### 3.1 Entity Extraction

The workflow begins with entity extraction from the query text:

```python
# Extract entities and query intent
extracted_entities = await self.llm_client.extract_entities_and_intent(
    query_text, context_info
)

# Only try to extract door model if not already provided
if not door_type_id and "door_model" in extracted_entities:
    door_model = extracted_entities["door_model"]
    door_type = await self.door_service.get_door_type_by_model(door_model)
    if door_type:
        door_type_id = door_type.id
```

The entity extraction process uses carefully crafted prompts to identify:
- Query intent (observation_analysis, cause_identification, solution_finding, general_info)
- Observation details (visual, auditory, positional)
- Error codes and related parts
- Door model information
- Environmental factors

### 3.2 Knowledge Path Search

Once entities are extracted, the system searches for relevant knowledge paths:

```python
# Find relevant knowledge paths based on extracted entities and intent
paths = await self.search_service.find_knowledge_paths(
    extracted_entities, door_type_id, self.llm_client
)
```

The search process employs a sophisticated tiered approach:

1. **Tier 1**: Exact attribute match + semantic similarity
2. **Tier 2**: Combinations of attributes
3. **Tier 3**: Individual high-value attributes
4. **Tier 4**: Pure semantic search
5. **Tier 5**: Fallback search mechanisms

This tiered approach ensures the system can handle a wide variety of queries with different levels of detail.

### 3.3 Path Verification

Retrieved knowledge paths are verified for validity using LLM-based checks:

```python
# Verify paths with structured output
verification_result = self.gpt_processor.with_pydantic(
    system_prompt=system_prompt,
    user_prompt=user_prompt,
    response_model=PathVerificationModel,
    temperature=0.1,
)

# Access attributes directly from the Pydantic model
invalid_path_ids = verification_result.invalid_path_ids

# Filter out invalid paths
valid_paths = [
    path
    for i, path in enumerate(paths)
    if i not in invalid_path_ids
]
```

The verification process ensures that:
- Cause nodes actually explain why problems occur
- Invalid causes (like maintenance history) are filtered out
- Paths maintain logical coherence (observation -> cause -> solution)

### 3.4 Path Ranking

Valid knowledge paths are ranked using a multi-factor algorithm:

```python
# 2. Calculate mechanic experience (30%)
experience_years = 0.7  # Default value
if creator_id:
    try:
        mechanic = await self.mechanic_repo.get_by_id(creator_id)
        if mechanic:
            experience_years = mechanic.experience_years
    except Exception as e:
        log_warning(f"Error getting mechanic experience: {str(e)}")

mechanic_factor = experience_years * 0.3

# 3. Calculate solution success rate (40%)
# 4. Calculate recency factor (20%)
# 5. Calculate observation attribute match (10%)

# 6. Combine factors
total_score = (
    mechanic_factor
    + success_factor
    + recency_factor
    + observation_factor
)
```

Key ranking factors include:
- Mechanic experience (30%)
- Solution success rate (40%)
- Information recency (20%)
- Observation attribute matches (10%)

### 3.5 Answer Generation

Finally, the system generates a natural language answer based on the highest-ranked knowledge paths:

```python
# Generate answer using only the best path
answer = await self.llm_client.generate_answer(
    query_text, knowledge_paths, extracted_entities, context_info
)
```

The answer generation process focuses on providing clear, practical information following the OCS structure:
1. Describe the observation (problem)
2. Explain the cause
3. Provide the solution steps
4. Include trust information

## 4. Search Strategies

The search service implements several sophisticated strategies to find relevant knowledge paths.

### 4.1 Optimized Observation Search

```python
@async_timer()
async def optimized_observation_search(
    self,
    query_text: str,
    extracted_entities: Dict[str, Any],
    door_type_id: Optional[str] = None,
    limit: int = 10,
) -> List[KnowledgePath]:
    """
    Optimized search strategy for Observation nodes with tiered fallbacks.
    Searches within specified door model first, then extends to all models if needed.
    """
```

This approach:
- Searches within the specified door model first
- Falls back to cross-model search if necessary
- Merges results with preference for door-specific matches
- Preserves high-quality matches regardless of door model

### 4.2 Combined Observation Search

```python
@async_timer()
async def combined_observation_search(
    self,
    observation_text: str,
    observation_attributes: Dict[str, Any],
    door_type_id: Optional[str] = None,
    limit: int = 5,
) -> List[KnowledgePath]:
    """
    Perform a combined search using both embedding similarity and attribute matching.
    """
```

This method combines:
- Semantic embedding similarity for conceptual matching
- Specific attribute matching for technical details
- Weighted scoring to balance both approaches

### 4.3 Semantic Similarity Search

```python
@async_timer()
async def search_by_semantic_similarity(
    self,
    node_type: str,
    observation_description: str,
    door_type_id: Optional[str] = None,
    limit: int = 5,
) -> List[KnowledgePath]:
    """
    Search knowledge nodes by semantic similarity using Neo4j vector search.
    """
```

Key features:
- Uses vector embeddings to represent query semantics
- Leverages Neo4j vector search capabilities
- Applies trust scoring based on similarity
- Handles different node types (Observation, Cause, Solution)

### 4.4 Tiered Search with Fallbacks

```python
@async_timer()
async def _tiered_search(
    self,
    observation_text: str,
    observation_attributes: Dict[str, Any],
    door_type_id: Optional[str] = None,
    limit: int = 10,
) -> List[KnowledgePath]:
    """
    Perform tiered search with progressive fallbacks.
    """
```

The tiered approach ensures:
- Maximum precision with exact matches when possible
- Graceful degradation to broader searches when necessary
- Adaptation to queries with varying levels of detail
- Comprehensive coverage of the knowledge base

## 5. OCS Knowledge Framework

The query pipeline works with knowledge organized in the OCS (Observation-Cause-Solution) framework:

### 5.1 Observation Nodes

Observations represent what mechanics perceive about door issues:

```python
# Extract observation
observation = None
for node in path.nodes:
    if node.type == KnowledgeType.OBSERVATION:
        observation = node
        break

# Include observation attributes if available
obs_attributes = []
if getattr(observation, "visual_observation", None):
    obs_attributes.append(f"Visual: {observation.visual_observation}")
if getattr(observation, "auditory_observation", None):
    obs_attributes.append(f"Sound: {observation.auditory_observation}")
```

### 5.2 Cause Nodes

Causes explain the underlying problems:

```python
# Extract cause
cause = None
for node in path.nodes:
    if node.type == KnowledgeType.CAUSE:
        cause = node
        break

if cause:
    answer_parts.append(f"Cause: {cause.name}")
    answer_parts.append(f"{cause.description}\n")
```

### 5.3 Solution Nodes

Solutions provide practical fix instructions:

```python
# Extract solution
solution = None
for node in path.nodes:
    if node.type == KnowledgeType.SOLUTION:
        solution = node
        break

if solution:
    answer_parts.append(f"Solution: {solution.name}")
    answer_parts.append(f"{solution.description}")

    # Add parts information
    if solution.properties.get("parts_involved"):
        parts_str = ", ".join(solution.properties.get("parts_involved", []))
        answer_parts.append(f"Parts involved: {parts_str}")
```

## 6. Path Processing and Ranking

The path processor handles knowledge path analysis and ranking.

### 6.1 Path Component Extraction

```python
def extract_path_components(self, path_data):
    """
    Extract nodes and relationships from a Neo4j path.
    Enhanced to handle different formats of path data with better error handling.
    """
```

This method:
- Handles multiple path data formats
- Extracts nodes and relationships with proper typing
- Provides robust error handling
- Supports different Neo4j data structures

### 6.2 Path Finding

```python
async def find_paths_for_nodes(
    self,
    nodes: List[KnowledgeNode],
    node_type: str,
    door_type_id: Optional[str] = None,
) -> List[KnowledgePath]:
    """
    Find knowledge paths for a list of nodes.
    Updated to work with OCS structure.
    """
```

This approach:
- Finds paths starting from different node types
- Adapts search strategy based on node type
- Filters paths by door type when specified
- Handles the OCS structure correctly

## 7. Answer Generation

The LLM client generates natural language answers from knowledge paths.

### 7.1 Single-Path Focus

```python
# Generate answer using only the best path
try:
    # Initialize system and user prompts focused on a single path
    system_prompt = """
    Je bent een technische ondersteuningsassistent voor draaideuren en roldeurmechanici.

    Geef een duidelijk, praktisch antwoord dat de mechanicus kan gebruiken om het probleem direct op te lossen.
    Gebruik alleen de informatie die beschikbaar is in het kennispad. Volg exact het observatie-oorzaak-oplossing pad.
    """
```

This approach:
- Focuses on the single best path for clarity
- Follows the OCS structure precisely
- Provides practical, actionable information
- Includes trust information

### 7.2 Template-Based Fallbacks

```python
# Fallback to template-based answer with just the best path
answer = []

# Add trust info
trust_info = f"(Betrouwbaarheid: {best_path.trust_score:.0%})"

# Create structured response
if observation:
    answer.append(f"**Probleem:**")
    answer.append(f"{observation.name}")
```

The system includes:
- Graceful fallbacks if LLM generation fails
- Consistent structure preservation
- Clear section labeling
- Trust transparency

## 8. Performance Optimization

The code includes several performance optimization techniques:

### 8.1 Caching

```python
async def get_door_type_by_model(self, model: str) -> Optional[Any]:
    """
    Get door type by model name from the database with caching.
    """
    if not model:
        return None

    try:
        # Check cache first
        cached_door_type = self.door_cache.get(model)
        if cached_door_type:
            log_info(f"Cache hit for door model: {model}")
            return cached_door_type
```

This provides:
- Reduced database load for common queries
- Faster response times
- Negative result caching to avoid repeated lookups

### 8.2 Performance Monitoring

```python
@async_timer()
async def combined_observation_search(
    self,
    observation_text: str,
    observation_attributes: Dict[str, Any],
    door_type_id: Optional[str] = None,
    limit: int = 5,
) -> List[KnowledgePath]:
```

The system includes:
- Detailed performance tracking with timer decorators
- Function-level timing logs
- Error tracking
- Result count statistics

### 8.3 Parallel Processing

```python
# Run door-specific and all-model searches concurrently
door_specific_task = self._tiered_search(
    observation_text,
    observation_attributes,
    door_type_id,
    limit,
)

# Only run all-model search if door_type_id is specified
if door_type_id:
    all_model_task = self._tiered_search(
        observation_text,
        observation_attributes,
        None,  # No door type filter
        limit,
    )

    # Wait for both tasks to complete
    door_specific_paths, all_model_paths = await asyncio.gather(
        door_specific_task, all_model_task
    )
```

This approach:
- Runs independent searches in parallel
- Reduces overall query processing time
- Optimizes resource utilization
- Maintains responsiveness

## 9. Path Verification

The path verification process ensures knowledge quality:

```python
async def verify_knowledge_paths(
    self,
    paths: List[KnowledgePath],
) -> List[KnowledgePath]:
    """
    Verify that each node in the knowledge paths correctly represents its declared type.
    Uses Pydantic models for structured data validation.
    """
```

Key features:
- Structured verification using Pydantic models
- Validity checks for cause explanations
- Filtering of invalid/unhelpful paths
- Pattern-based validation for common issues

## 10. Conclusion

The query processing pipeline represents a sophisticated system for understanding mechanic queries and providing relevant knowledge. Key strengths include:

- **Modular Architecture**: Clean separation of concerns with specialized components
- **Multi-Strategy Search**: Tiered approach with graceful fallbacks
- **OCS Framework**: Clear organization of knowledge into Observation, Cause, and Solution
- **Performance Optimization**: Caching, parallel processing, and monitoring
- **Knowledge Verification**: Ensuring validity and relevance of responses
- **Door-Specific Processing**: Prioritizing door-specific knowledge while accessing broader knowledge when needed

This architecture enables junior mechanics to access the collective knowledge of experienced professionals, effectively bridging the knowledge gap in revolving door maintenance. The pipeline's fallback mechanisms and verification steps ensure robustness even when dealing with ambiguous queries or incomplete knowledge.