import openai
import json
from typing import Any, Dict, Type, Optional, Union, List
from pydantic import BaseModel

from app.core.exceptions import QueryProcessingError

from app.core.logging import log_extraction

logger = log_extraction()


class GptProcessor:
    def __init__(self, settings, model="gpt-4.1"):
        self.settings = settings
        self.model = model
        self.openai_client = self._initialize_openai_client()

    def _initialize_openai_client(self):
        if not self.settings.OPENAI_API_KEY:
            raise QueryProcessingError("OpenAI API key is not set")
        client = openai.OpenAI(api_key=self.settings.OPENAI_API_KEY)
        return client

    def __call__(
        self,
        system_prompt: str,
        user_prompt: str,
        json_output: bool = True,
        temperature: float = 0.1,
    ) -> Union[Dict[str, Any], str]:
        """
        Process a query using OpenAI's GPT model.

        Args:
            system_prompt (str): The system prompt to set the context.
            user_prompt (str): The user prompt for the query.
            json_output (bool): Whether to return the output as JSON.
            temperature (float): Sampling temperature for degree of randomness.
        Returns:
            Union[Dict[str, Any], str]: The response from the GPT model.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
                response_format=(
                    {"type": "json_object"} if json_output else None
                ),
            )
            content = response.choices[0].message.content

            if json_output:
                return json.loads(content)

            return content

        except Exception as e:
            logger.error(f"GPT call outer error: {str(e)}")
            raise QueryProcessingError(f"GPT call failed: {str(e)}")

    def with_json_schema(
        self,
        system_prompt: str,
        user_prompt: str,
        json_schema: Dict[str, Any],
        temperature: float = 0.1,
    ) -> Dict[str, Any]:
        """
        Process a query using OpenAI's GPT model with a structured output.

        Args:
            system_prompt (str): The system prompt to set the context.
            user_prompt (str): The user prompt for the query.
            json_schema (Dict[str, Any]): The JSON schema for the response.
            temperature (float): Sampling temperature for degree of randomness.
        Returns:
            Dict[str, Any]: The structured response from the GPT model.
        """

        try:
            response = self.openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=temperature,
                response_format={
                    "type": "json_schema",
                    "json_schema": json_schema,
                },
            )
            content = response.choices[0].message.content
            return json.loads(content)

        except Exception as e:
            logger.error(f"GPT structured output error: {str(e)}")
            raise QueryProcessingError(
                f"GPT structured output failed: {str(e)}"
            )

    def with_pydantic(
        self,
        system_prompt: str,
        user_prompt: str,
        response_model: Type[BaseModel],
        temperature: float = 0.1,
    ) -> BaseModel:
        """
        Process a query using OpenAI's GPT model and parse the response with a Pydantic model.

        The beta.chat.completions.parse method is specifically designed for direct Pydantic
        model parsing. Unlike chat.completions.create which returns raw text that must be
        manually parsed and validated, this method:
        1. Automatically validates the response against the Pydantic model
        2. Handles type conversions automatically
        3. Returns a ready-to-use model instance
        4. Provides better error messages for malformed responses

        This is more efficient and reduces the chance of validation errors when working
        with structured data.

        Args:
            system_prompt (str): The system prompt to set the context.
            user_prompt (str): The user prompt for the query.
            response_model (Type[BaseModel]): The Pydantic model to parse the response with.
            temperature (float): Sampling temperature for degree of randomness.
        Returns:
            BaseModel: The parsed response from the GPT model.
        """

        try:
            completion = self.openai_client.beta.chat.completions.parse(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                response_format=response_model,
                temperature=temperature,
            )
            return completion.choices[0].message.parsed

        except Exception as e:
            logger.error(f"GPT Pydantic parsing error: {str(e)}")
            raise QueryProcessingError(
                f"GPT Pydantic parsing failed: {str(e)}"
            )

    def with_pydantic_list(
        self,
        system_prompt: str,
        user_prompt: str,
        response_model: Type[BaseModel],
        temperature: float = 0.1,
    ) -> List[BaseModel]:
        """
        Process a query using OpenAI's GPT model and parse the response as a list of Pydantic models.

        Args:
            system_prompt (str): The system prompt to set the context.
            user_prompt (str): The user prompt for the query.
            response_model (Type[BaseModel]): The Pydantic model to parse the response items with.
            temperature (float): Sampling temperature for degree of randomness.
        Returns:
            List[BaseModel]: A list of parsed responses from the GPT model.
        """

        try:
            # Create a container model for the list
            class ListContainer(BaseModel):
                items: List[response_model]

            completion = self.openai_client.beta.chat.completions.parse(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                response_format=ListContainer,
                temperature=temperature,
            )
            return completion.choices[0].message.parsed.items

        except Exception as e:
            logger.error(f"GPT Pydantic list parsing error: {str(e)}")
            raise QueryProcessingError(
                f"GPT Pydantic list parsing failed: {str(e)}"
            )
