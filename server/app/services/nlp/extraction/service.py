import logging
import asyncio
import pandas as pd
import os
import json
from typing import List, Dict, Any, Tuple, Optional
from fastapi import Depends

from app.core.config import get_settings
from app.core.exceptions import KnowledgeExtractionError
from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgeRelationship,
    KnowledgeType,
)
from app.models.schemas.knowledge import (
    KnowledgeExtractionResponse,
    FirstKnowledgeExtractionResponse,
    ExtractedNode,
    KnowledgeExtractionSummary,
)
from app.services.nlp.extraction.path_service import (
    PathService,
)  # TODO: where is this used?
from app.services.database.repositories.mechanic_repo import MechanicRepository
from app.services.database.repositories.door_repo import DoorRepository
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)
from app.services.cache.door_cache import DoorTypeCache, get_door_cache
from app.services.nlp.extraction.processors import ExtractionProcessor
from app.services.nlp.extraction.graph_builder import KnowledgeGraphBuilder
from app.services.nlp.extraction.utils import (
    save_data_analysis_CSV,
    validate_extracted_data,
)
from app.services.nlp.extraction.constants import (
    DEFAULT_EXPERIENCE,
    ErrorMessage,
)
from app.services.nlp.extraction.models import KnowledgeExtraction

import random

from app.core.logging import log_extraction
from app.core.session_logging import get_current_session_id, log_extraction_session

logger = log_extraction()


class KnowledgeExtractionService:
    """
    Service for extracting knowledge from text using LLMs.
    """

    def __init__(
        self,
        settings=Depends(get_settings),
        mechanic_repo: MechanicRepository = Depends(),
        door_repo: DoorRepository = Depends(),
        knowledge_repo: KnowledgeRepository = Depends(),
        door_cache: DoorTypeCache = Depends(get_door_cache),
    ):
        self.settings = settings
        self.mechanic_repo = mechanic_repo
        self.door_repo = door_repo
        self.knowledge_repo = knowledge_repo
        self.door_cache = door_cache
        self.extraction_processor = ExtractionProcessor(settings)
        self.graph_builder = KnowledgeGraphBuilder(
            knowledge_repo=knowledge_repo,
            door_repo=door_repo,
            embedding_service=knowledge_repo.embedding_service,
        )

    async def _get_door_type_by_model(self, model: str):
        """Get door type by model using global cache"""
        return self.door_cache.get_by_model(model)

    async def _get_experience_years(
        self,
        mechanic_id: Optional[str],
    ) -> float:
        """
        Get the experience years of a mechanic.

        Args:
            mechanic_id: The mechanic ID.
            mechanic_repo: Repository for mechanic data.
            default_experience: Default value if mechanic not found.

        Returns:
            float: The experience years of the mechanic, or a default value if not found.
        """

        if not mechanic_id:
            return DEFAULT_EXPERIENCE

        try:
            mechanic = await self.mechanic_repo.get_by_id(mechanic_id)
            if (
                not mechanic
                or not hasattr(mechanic, "experience_years")
                or not isinstance(mechanic.experience_years, (int, float))
                or mechanic.experience_years < 0
            ):
                logger.warning(
                    f"Mechanic {mechanic_id} not found or invalid experience years, using default: {DEFAULT_EXPERIENCE}"
                )
                return DEFAULT_EXPERIENCE

            return mechanic.experience_years
        except Exception as e:
            logger.warning(
                f"Could not get mechanic experience: {str(e)}, using default: {DEFAULT_EXPERIENCE}"
            )
            return DEFAULT_EXPERIENCE

    async def extract_knowledge(
        self,
        text: str,
        mechanic_id: Optional[str] = None,
        door_type=None,
        service_order_number: Optional[str] = None,
    ) -> Tuple[KnowledgeExtraction, str]:  # Updated return type
        """Extract knowledge with improved error handling. Updated for OCS structure."""

        try:
            # Use asyncio gather for parallel processing
            extracted_data, data_analysis_model, experience_years = (
                await asyncio.gather(
                    self.extraction_processor.extract_knowledge(text),
                    self.extraction_processor.extract_knowledge_for_data_analysis(text),
                    self._get_experience_years(mechanic_id),
                )
            )

            logger.debug(f"Extracted data: {extracted_data}")
            logger.debug(f"Data analysis model: {data_analysis_model}")
            logger.debug(f"Experience years: {experience_years}")

            if not extracted_data:
                logger.error(ErrorMessage.NO_EXTRACTION_DATA)
                raise KnowledgeExtractionError(ErrorMessage.NO_EXTRACTION_DATA)

            if not data_analysis_model:
                logger.error(ErrorMessage.NO_EXTRACTION_DATA)
                raise KnowledgeExtractionError(ErrorMessage.NO_EXTRACTION_DATA)

            # Generate admin summary for frontend
            admin_summary = await self.extraction_processor.generate_admin_summary(
                text,
                extracted_data,
                service_order_number,
                door_type.model,
            )
            if not admin_summary:
                logger.error(ErrorMessage.NO_ADMIN_SUMMARY)
                raise KnowledgeExtractionError(ErrorMessage.NO_ADMIN_SUMMARY)

            # Save the mechanic name and client data to csv using the model directly

            save_data_analysis_CSV(
                mechanic_id=mechanic_id,
                door_type=str(door_type.model) if door_type else "Unknown",
                service_order_number=service_order_number,
                extracted_client_data=data_analysis_model,
            )

            # Log extraction session using the session logger

            log_extraction_session(
                session_id=get_current_session_id(),
                mechanic_id=mechanic_id,
                service_order_number=service_order_number,
                transcription_text=text,
                door_type=str(door_type.model) if door_type else "Unknown",
                llm_output=extracted_data,
                llm_output_data_analysis=data_analysis_model.model_dump(),
                admin_summary=admin_summary,
                error=None,
            )

            # Validate the extracted data with better error handling
            try:
                extracted_data_dict = extracted_data.model_dump()
                validate_extracted_data(extracted_data_dict)
            except Exception as e:
                logger.error(f"Extraction validation error: {str(e)}")
                raise KnowledgeExtractionError(
                    ErrorMessage.INVALID_KNOWLEDGE_STRUCTURE.format(str(e))
                )

            logger.debug(f"Extracted data type: {type(extracted_data)}")
            logger.debug(f"Admin summary type: {type(admin_summary)}")
            return extracted_data, admin_summary  # Return Pydantic model and summary

        except KnowledgeExtractionError:
            # Re-raise knowledge extraction errors
            raise
        except Exception as e:
            logger.error(f"Knowledge extraction error: {str(e)}")
            log_extraction_session(
                session_id=get_current_session_id(),
                error=str(e),
            )
            raise KnowledgeExtractionError(
                ErrorMessage.FAILED_EXTRACT_KNOWLEDGE.format(str(e))
            )

    # Updated process_extraction method:
    async def process_extraction(
        self,
        text: str,
        door_model: Optional[str] = None,
        mechanic_id: Optional[str] = None,
        service_order_number: Optional[str] = None,
    ) -> KnowledgeExtractionResponse:
        """
        Process knowledge extraction from text and return a structured response.

        Args:
            text: The text to extract knowledge from
            door_model: Optional door model
            mechanic_id: Optional mechanic ID

        Returns:
            KnowledgeExtractionResponse: Structured response containing nodes, relationships and paths
        """
        try:
            # Get door_type from door_model if provided
            door_type = None
            if door_model:
                door_type = await self.door_repo.get_door_type_by_model(
                    door_model.replace("Boon Edam ", "")
                )
                if not door_type:
                    logger.info(f"Creating new door type for model {door_model}")
                    door_type = await self.door_repo.create_door_type(
                        model=door_model,
                        manufacturer="Boon Edam",
                        description=f"Door model {door_model}",
                    )

            # Extract knowledge - now returns (extracted_data, admin_summary)
            extracted_data, admin_summary = await self.extract_knowledge(
                text, mechanic_id, door_type, service_order_number
            )

            logger.debug(f"extracted data: {extracted_data.model_dump_json()}")

            extracted_nodes = []

            for path_index, knowledge_path in enumerate(extracted_data.knowledge_paths):
                for node_type in ["observation", "cause", "solution"]:
                    extracted_nodes.append(
                        ExtractedNode(
                            type=node_type,
                            data=getattr(knowledge_path, node_type).model_dump(),
                            path_id=path_index,
                        )
                    )

            return FirstKnowledgeExtractionResponse(
                nodes=extracted_nodes,
                raw_extracted_data=extracted_data.model_dump_json(),  # Use Pydantic's JSON serialization
                admin=admin_summary,
            )

        except Exception as e:
            logger.error(f"Error in process_extraction: {str(e)}")
            raise

    async def start_build_knowledge_graph(
        self,
        extracted_data: Dict[str, Any],
        mechanic_id: Optional[str],
        mechanic_experience: float,
        door_type: Optional[Any],
        service_order_number: Optional[str],
    ) -> bool:
        """
        Build the knowledge graph from extracted data and store it in the database.

        Args:
            extracted_data: The extracted knowledge data
            mechanic_id: ID of the mechanic who performed the service
            mechanic_experience: Experience years of the mechanic
            door_type: Door type object
        """
        if not extracted_data:
            logger.error("No extracted data provided for knowledge graph building")
            raise KnowledgeExtractionError(
                "Cannot build knowledge graph: no extracted data provided"
            )

        try:
            # Log the input data structure
            logger.info(
                f"Building knowledge graph from data with keys: {extracted_data.keys()}"
            )
            if "knowledge_paths" in extracted_data:
                logger.info(
                    f"Found {len(extracted_data['knowledge_paths'])} knowledge paths"
                )

            # Get the current session ID to add to nodes
            session_id = get_current_session_id()
            logger.info(f"Using session ID for nodes: {session_id}")

            # Pass service_order_number and session_id to build_knowledge_graph
            nodes, relationships, node_ids = (
                await self.graph_builder.build_knowledge_graph(
                    extracted_data,
                    mechanic_id,
                    mechanic_experience,
                    door_type,
                )
            )

            # Log detailed information about created nodes
            logger.info(
                f"Built knowledge graph with {len(nodes)} nodes and {len(relationships)} relationships"
            )
            logger.info(f"Node types created: {[node.type.value for node in nodes]}")
            logger.info(f"Created node IDs: {node_ids}")

            # Log the created node IDs to the session
            logger.info(
                f"Logging {len(node_ids)} node IDs to session {session_id}: {node_ids}"
            )

            log_extraction_session(
                session_id=session_id,
                node_pointers=node_ids,  #  pass the node IDs
                service_order_number=service_order_number,
            )

            return True
        except Exception as e:
            logger.error(f"Error building knowledge graph: {str(e)}")
            raise KnowledgeExtractionError(
                ErrorMessage.FAILED_BUILD_GRAPH.format(str(e))
            )
