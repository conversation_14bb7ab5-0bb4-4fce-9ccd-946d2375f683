"""LLM processing and extraction for knowledge data."""

import os
import json
import openai
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
import pandas as pd
from datetime import datetime

from pathlib import Path
import csv

from app.core.exceptions import KnowledgeExtractionError
from app.services.nlp.extraction.constants import (
    MODEL_NAME,
    TEMPERATURE,
    ExtractionField,
    ErrorMessage,
    TrustValues,
)
from app.services.nlp.extraction.extraction_prompts.prompts import (
    create_extraction_prompt_for_quality_analysis,
    create_system_prompt_for_quality_analysis,
    get_extraction_system_prompt,
    create_extraction_prompt,
    get_admin_summary_prompts,
)

from app.services.nlp.extraction.models import QualityAnalysisReport, KnowledgeExtraction
from app.services.nlp.gpt import GptProcessor
from app.core.logging import log_extraction

logger = log_extraction()


class ExtractionProcessor:
    """Processes text descriptions to extract knowledge using OpenAI API."""

    def __init__(self, settings):
        self.settings = settings
        self.model_name = MODEL_NAME
        self.temperature = TEMPERATURE
        self.openai_client = None

    def _init_openai(self):
        """Initialize OpenAI client."""
        if not self.openai_client:
            if not self.settings.OPENAI_API_KEY:
                raise KnowledgeExtractionError(ErrorMessage.OPENAI_API_KEY_NOT_SET)

            self.openai_client = openai.OpenAI(api_key=self.settings.OPENAI_API_KEY)

    async def extract_knowledge(self, text: str) -> Dict[str, Any]:
        """
        Use the LLM to extract knowledge from text.

        Args:
            text: The text to extract knowledge from.

        Returns:
            Dict[str, Any]: The extracted knowledge.

        Raises:
            KnowledgeExtractionError: If extraction fails.
        """
        try:
            # Call the OpenAI API
            self._init_openai()
            processor = GptProcessor(self.settings, self.model_name)
            response = processor.with_pydantic(
                system_prompt=get_extraction_system_prompt(),
                user_prompt=create_extraction_prompt(text),
                response_model=KnowledgeExtraction,
                temperature=self.temperature,
            )

            return response

        except Exception as e:
            logger.error(f"LLM extraction error for client model: {str(e)}")
            raise KnowledgeExtractionError(
                ErrorMessage.LLM_EXTRACTION_ERROR.format(str(e))
            )

    async def extract_knowledge_for_data_analysis(self, text: str, ) -> QualityAnalysisReport:
        """
        Use the LLM to extract knowledge from text for data analysis purposes within boon
        Returns a Pydantic model instance directly.

        Args:
            text: The text to extract knowledge from.

        Returns:
            QualityAnalysisReport: The extracted knowledge as a Pydantic model.

        Raises:
            KnowledgeExtractionError: If extraction fails.
        """
        try:
            # Call the OpenAI API
            self._init_openai()
            processor = GptProcessor(self.settings, self.model_name)
            response = processor.with_pydantic(
                system_prompt=create_system_prompt_for_quality_analysis(),
                user_prompt=create_extraction_prompt_for_quality_analysis(text),
                response_model=QualityAnalysisReport,
                temperature=self.temperature,
            )

            return response

        except Exception as e:
            logger.error(f"LLM extraction error for client model: {str(e)}")
            raise KnowledgeExtractionError(
                ErrorMessage.LLM_EXTRACTION_ERROR.format(str(e))
            )

    async def generate_admin_summary(
        self,
        text: str,
        extracted_data: KnowledgeExtraction,
        service_order_number: str,
        door_model: str,
    ) -> str:
        """
        Generate an admin summary for the service job based on the transcript and extracted knowledge.
        For the frontend and the client.
        """
        try:
            self._init_openai()

            # Define path to CSV using pathlib
            csv_path = (
                Path(__file__).parent.parent.parent.parent.parent
                / "admin_examples"
                / "admin_summaries.csv"
            )

            csv_path.parent.mkdir(parents=True, exist_ok=True)

            # Ensure CSV file exists with headers
            if not csv_path.exists():
                with open(csv_path, "w", newline="") as f:
                    writer = csv.writer(f, delimiter=";")
                    writer.writerow(["Category", "Observation", "client_explanation"])
                    logger.warning(
                        f"Client explanations CSV did not exist and was created at {csv_path}"
                    )

            # Load CSV rows as list of dictionaries
            else:
                csv_explanations = []
                with open(csv_path, newline="", encoding="utf-8") as f:
                    reader = csv.DictReader(f, delimiter=";")
                    for i, row in enumerate(reader):
                        explanation = f"- {row['Category']}: {row['Observation']} - {row['client_explanation']}"
                        csv_explanations.append(explanation)

            explanation_text = "\n".join(csv_explanations)

            # Prepare prompts
            extracted_data_dict = extracted_data.model_dump()

            system_prompt, user_prompt = get_admin_summary_prompts(
                text=text,
                service_order_number=service_order_number,
                door_model=door_model,
                date=datetime.now().strftime("%d-%m-%Y"),
                extracted_data=extracted_data_dict,
                csv_explanations=explanation_text,
            )

            # Call the GPT model with JSON schema
            processor = GptProcessor(self.settings, self.model_name)
            response = processor(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                json_output=False,
                temperature=0.3,
            )

            logger.debug(f"Admin summary: {response}")
            return response

        except Exception as e:
            logger.error(f"Error generating admin summary: {str(e)}")
            return ""
