"""Pydantic models for knowledge extraction."""

import logging
from pydantic import BaseModel, Field
from typing import List
from enum import Enum


from app.core.logging import log_extraction

logger = log_extraction()


class QualityAnalysisCause(BaseModel):
    """Model for client-visible cause information."""

    description: str = Field(..., description="Explanation of why this the functional problem is happening")
    parts_involved: List[str] = Field(
        default_factory=list, description="Components that are causing the problem"
    )


class QualityAnalysisReport(BaseModel):
    """Maintenance report using SPCS framework for quality analysis."""

    symptom: str = Field(
        ..., description="What is the door not doing correctly according to the client?"
    )
    problem: str = Field(..., description="Details about the functional problem observed by the mechanic")
    error_codes: List[str] = Field(default_factory=list, description="Error code(s) if present")
    cause: QualityAnalysisCause = Field(..., description="What is the underlying issue?")
    solution: str = Field(
        ..., description="Step-by-step instructions for it was fixed"
    )


### FOR PATHS / DATABASE


class Observation(BaseModel):
    """Observation details in the OCS framework"""

    name: str = Field(..., description="Brief descriptive name of the observation")
    description: str = Field(..., description="Detailed description of the observation")
    visual_observation: str = Field(default="", description="What can be seen")
    auditory_observation: str = Field(default="", description="What can be heard")
    positional_observation: str = Field(
        default="", description="Where on the door and behavior"
    )
    error_codes: List[str] = Field(default="", description="Error code(s) if present")
    related_parts: List[str] = Field(
        default_factory=list, description="Related parts involved"
    )


class Cause(BaseModel):
    """Cause details in the OCS framework"""

    name: str = Field(..., description="Brief descriptive name of the cause")
    description: str = Field(..., description="Detailed description of the cause")


class Solution(BaseModel):
    """Solution details in the OCS framework"""

    name: str = Field(..., description="Brief descriptive name of the solution")
    description: str = Field(
        ..., description="Detailed description of the solution steps"
    )
    parts_involved: List[str] = Field(
        default_factory=list, description="Parts involved in the solution"
    )


class KnowledgePath(BaseModel):
    """Complete knowledge path following OCS framework"""

    observation: Observation
    cause: Cause
    solution: Solution


class KnowledgeExtraction(BaseModel):
    """Main knowledge extraction response following OCS framework"""

    knowledge_paths: List[KnowledgePath] = Field(
        default_factory=list,
        description="List of observation-cause-solution knowledge paths",
    )
