import csv
from pathlib import Path
from datetime import datetime
from typing import Any, Dict, Optional, List


from app.core.exceptions import KnowledgeExtractionError
from app.services.nlp.extraction.constants import (
    Extraction<PERSON>ield,
    OCSComponent,
    ErrorMessage,
)

from app.core.logging import log_extraction

logger = log_extraction()


def save_data_analysis_CSV(
    mechanic_id: str,
    door_type: str,
    service_order_number: str,
    extracted_client_data: Any,
    csv_file: str = "output/SPCS_for_quality_analysis.csv",
) -> None:
    """
    Save SPCS data to CSV with proper column structure.

    Args:
        mechanic_id: The ID of the mechanic
        extracted_client_data: The Pydantic model response for the client
        csv_file: The file to save the data to
    """
    timestamp = datetime.utcnow().isoformat()

    file_exists = Path(csv_file).exists()

    # Add parts info to solution if available
    parts_involved = ""
    if extracted_client_data.cause.parts_involved:
        parts_involved = f"{', '.join(extracted_client_data.cause.parts_involved)}"

    error_codes = ""
    if extracted_client_data.error_codes:
        error_codes = f"{', '.join(extracted_client_data.error_codes)}"

    # Create data row
    data_row = [
        timestamp,
        mechanic_id,
        service_order_number,
        door_type,
        extracted_client_data.symptom,
        extracted_client_data.problem,
        error_codes,
        extracted_client_data.cause.description,
        parts_involved,
        extracted_client_data.solution,
    ]

    if not file_exists:
        Path(csv_file).parent.mkdir(parents=True, exist_ok=True)
        Path(csv_file).touch()

    # Write to CSV
    with open(csv_file, "a", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)

        # Write headers if file doesn't exist
        if not file_exists:
            headers = [
                "timestamp",
                "mechanic_id",
                "service_order_number",
                "door_type",
                "symptom",
                "problem",
                "error_codes",
                "cause",
                "parts_involved",
                "solution",
            ]
            writer.writerow(headers)

        writer.writerow(data_row)

    logger.info(f"Saved quality analysis data to {csv_file}")


def validate_extracted_data(data: Dict[str, Any]) -> None:
    """
    Validate the extracted data meets minimum requirements.
    Updated for OCS structure.

    Args:
        data: The extracted data.

    Raises:
        KnowledgeExtractionError: If validation fails.
    """
    # Check if at least one knowledge path exists
    if (
        ExtractionField.KNOWLEDGE_PATHS not in data
        or not data[ExtractionField.KNOWLEDGE_PATHS]
    ):
        raise KnowledgeExtractionError(ErrorMessage.MIN_ONE_PATH)

    # Check that each knowledge path has at least one component of OCS
    for i, path in enumerate(data[ExtractionField.KNOWLEDGE_PATHS]):
        # Define the OCS components
        ocs_components = OCSComponent.ALL
        if not any(k in path for k in ocs_components):
            raise KnowledgeExtractionError(
                ErrorMessage.PATH_MIN_ONE_COMPONENT.format(i + 1)
            )
