from app.services.nlp.extraction.models import KnowledgeExtraction
import json
from app.services.nlp.extraction.constants import (
    POSSIBLE_CODES,
)


def get_extraction_system_prompt() -> str:
    """
    Get the system prompt for the LLM for extracting knowledge from text.

    Returns:
        str: The system prompt.
    """
    return """
        You are a specialized knowledge extraction system for revolving door mechanics.
        Your task is to extract structured knowledge from natural language descriptions
        of revolving door maintenance and repairs.

        You must follow the OCS framework: Observation, Cause, Solution.

        You should identify all separate observations, causes, and solutions
        in the text, and establish the correct relationships between them.

        You will also extract any relevant tacit knowledge that doesn't fit the OCS framework.

        You will receive text from a mechanic describing their work on a revolving door.
        You should return a JSON object with the extracted knowledge.

        Be precise, literal, and accurate in your extraction.
        """


def create_extraction_prompt(text: str) -> str:
    """
    Create the extraction prompt for the LLM using Pydantic schema.
    """
    # Get the JSON schema from the Pydantic model
    schema = KnowledgeExtraction.model_json_schema()

    prompt = f"""
    I need you to extract knowledge from the following description of revolving door maintenance:

    ```
    {text}
    ```

    Extract the information according to the OCS framework (Observation, Cause, Solution)
    and any tacit knowledge. Identify ALL observations, causes, and solutions
    mentioned in the text. Then connect them to show the proper relationships
    between them.

    Return a JSON object that matches this exact schema:
    {json.dumps(schema, indent=4)}

    Normalization rules (strictly apply them):
    
    - For `error_codes`:
        1. Error codes are usually represented as a combination of letters, possibly in multiple flashes (but can also be a single code).
        2. If an error code is present, extract only the code(s) in a list format. Example: "error codes are first ADE and then CF" → "error_codes": ["ADE", "CF"]
        3. Here are the possible error codes: {", ".join(POSSIBLE_CODES)}. Only include those that are explicitly mentioned in the text. If another code is mentioned that is not in this list, do not include it.
        4. If no error codes are present or stated, use exactly an empty list: []
    - For `visual_observation` and `auditory_observation` fields:
    - If nothing is seen or heard, use exactly: ""
    - Example: "no visual clues", "geen zichtbare problemen", "geen geluid", "no sound" → ""
    - Avoid subjective or vague modifiers like "nogal", "een beetje", "licht", "wat", "tamelijk", "redelijk", "vrij", "behoorlijk".
    - Only report the objective condition, for example:
        - "nogal versleten" → "versleten"
        - "een beetje vuil" → "vuil"
        - "tamelijk beschadigd" → "beschadigd"
    - For `positional_observation`:
        - Use the format "location: behavior", for example: "motor housing: stopped rotating"
        - Apply the same rule to the behavior part of the string (after the ":").
        - Example: "motor housing: nogal stotterend" → "motor housing: stotterend"

    - For `related_parts`:
        - Always output an array of lowercase part names with no duplication
        - Example: ["sensor", "motor"]
    
    Normalization examples:

        Input: "No error codes on screen"
        Output: "error_codes": ""

        Input: "error ABD en CF"
        Output: "error_codes": "["ABD", "CF"]"

        Input: "Geen geluid"
        Output: "auditory_observation": ""

        Input: "Geen zichtbare problemen"
        Output: "visual_observation": ""

        Input: "Haarborstels aan de onderkant nogal versleten"
        Output: "visual_observation": "Haarborstels aan de onderkant versleten"

        Input: "Een beetje vuil op de sensor"
        Output: "visual_observation": "vuil op de sensor"

        Input: "motor buiten stopt met draaien"
        Output: "positional_observation": "motor buiten: stopt met draaien"

        Input: "motor buiten: nogal stotterend"
        Output: "positional_observation": "motor buiten: stotterend"

        Only include fields that you can identify from the text. For tacit knowledge,
        only include items that don't fit into the OCS framework.

        CRITICAL PATH CREATION RULES:
        - When multiple observation types (visual, auditory, positional) relate to the SAME cause and solution, combine them into a SINGLE KnowledgePath with one Observation object that fills in all relevant fields
        - Example: If "door is dirty" (visual), "door rattling" (auditory), and "door not turning" (positional) are ALL caused by "worn bearings" and fixed by "bearing replacement", create ONE KnowledgePath with:
          * observation.visual_observation = "dirty"
          * observation.auditory_observation = "rattling" 
          * observation.positional_observation = "not turning"
        - Only create separate KnowledgePath objects when there are genuinely different cause-solution chains
        - If the same solution addresses multiple unrelated causes, then create separate knowledge paths
        - Prioritize logical grouping over excessive segmentation

    IMPORTANT: Maintain the original language (Dutch/English) from the input text. E.g. if the transcript is in Dutch, the captured data should be in Dutch. If the transcript is in English, the captured data should be in English.
    """
    return prompt


def get_admin_summary_prompts(
    text: str,
    service_order_number: str,
    door_model: str,
    date: str,
    extracted_data: dict, 
    csv_explanations: str
) -> tuple[str, str]:
    """
    Generate the system and user prompts for the admin summary.

    Args:
        text: The original transcript text
        extracted_data: The extracted knowledge data
        csv_explanations: Formatted string of CSV explanations for components

    Returns:
        tuple: A tuple containing (system_prompt, user_prompt)
    """
    system_prompt = """
Je bent een professionele servicetechnicus voor draaideuren die rapporten schrijft.
Maak duidelijke, beknopte samenvattingen van uitgevoerd onderhoud, met focus op
commerciële waarde en veiligheidsimplicaties. Schrijf altijd in het Nederlands.
"""

    user_prompt = f"""
Maak op basis van het volgende servicetranscript een professionele samenvatting voor administratieve doeleinden.
De samenvatting moet duidelijk, beknopt en geschikt zijn voor klantdocumentatie.

Origineel transcript:
{text}

Geëxtraheerde kennis:
- Date: {date}
- Serviceordernummer: {service_order_number}
- Deurmodel: {door_model}
- Aantal geïdentificeerde problemen: {len(extracted_data.get('knowledge_paths', []))}

Geef een samenvatting die de volgende secties bevat:

1. **Wat is er gedaan?** (bijv. uitgevoerd onderhoud)
- Vermeld de specifieke onderhoudsacties die zijn uitgevoerd
- Inclusief eventuele reparaties of aanpassingen

2. **Wat moet er nog gedaan worden?** (bijv. component X moet vervangen worden)
- Vermeld eventueel nog uit te voeren werkzaamheden of toekomstig onderhoud
- Specificeer welke componenten aandacht nodig hebben

3. **Waarom?** (Commerciële vertaling)
- Leg de zakelijke/veiligheidsredenen voor de aanbevelingen uit
- Voorbeeld: "Component X vertoont Y en moet om veiligheidsredenen vervangen worden"
- Focus op betrouwbaarheid, veiligheid en operationele impact

4. **Algemene beoordeling van de deur (NEN score)**
- Geef een conditiebeoordeling van 0-6 op basis van de volgende schaal:

Conditie 0: Installatie niet aangetroffen/niet beschikbaar voor onderhoud
Conditie 1: Uitstekend - Installatie is nieuw (jonger dan 1 jaar), in zeer goede staat, vertoont geen slijtage en alles functioneert naar behoren
Conditie 2: Goed - Installatie is ouder dan 1 jaar, heeft mogelijk lichte gebruikssporen, is in goede staat en functioneert naar behoren
Conditie 3: Redelijk - Installatie functioneert maar vertoont slijtage. Volgt eventueel een offerte om de bedrijfszekerheid te kunnen herstellen/verbeteren
Conditie 4: Matig - Installatie functioneert, maar onderdelen dienen in verband met slijtage of schade vervangen te worden. Een offerte volgt om de bedrijfszekerheid te kunnen herstellen. Melding aan beheerder
Conditie 5: Slecht - Installatie functioneert niet (veilig). Installatie in slechte staat maar herstelbaar. Een offerte volgt om de bedrijfszekerheid te kunnen herstellen. Melding aan beheerder
Conditie 6: Zeer slecht - Installatie functioneert niet (veilig). Installatie in onherstelbaar slechte staat. Advies volgt vanuit Boon Edam. Melding aan beheerder

Formatteer de samenvatting als een professioneel rapport met duidelijke secties.
Houd het beknopt maar volledig, onder de 350 woorden.
Vermeld het NEN conditienummer (0-6) met een korte rechtvaardiging gebaseerd op de bevindingen.

BELANGRIJK: VERPLICHT te gebruiken informatie voor de samenvatting:
Scan het transcript ZORGVULDIG op de volgende termen en problemen.
Let op: termen of formuleringen in het transcript komen niet altijd letterlijk overeen met de observaties in de lijst hieronder. Gebruik daarom contextuele interpretatie en professioneel inzicht om te bepalen of een bepaalde uitleg van toepassing is.
Je mag aannemen dat wanneer een probleem of component in andere woorden genoemd wordt (bijv. “motor” of "motorunit" i.p.v. “motor unit), je de uitleg uit de juiste categorie toch moet gebruiken — mits de betekenis overeenkomt.
Het is verplicht om de volledige uitleg uit het bestand op te nemen zodra er inhoudelijke overlap is met wat de monteur rapporteert — ongeacht of de exacte woorden overeenkomen.
Voor ELKE categorie of observatie uit onderstaande lijst die in het transcript wordt genoemd,
MOET je de bijbehorende uitleg VOLLEDIG verwerken in je rapport, met name in de "Waarom?" sectie.
Deze uitleg is NIET optioneel maar VERPLICHT om op te nemen wanneer de betreffende componenten of problemen genoemd worden:

{csv_explanations}

Voorbeeld: Als in het transcript "De verlichting is verouderd" OF "Verlichting" wordt genoemd,
MOET je de bijbehorende uitleg gebruiken en letterlijk verwijzen naar het doel van de aanbevolen vervanging.

Deze uitleg is essentieel om aan de klant duidelijk te maken waarom bepaalde werkzaamheden nodig zijn.
Het niet opnemen van deze informatie maakt het rapport onvolledig en onbruikbaar.

BELANGRIJK: Schrijf het rapport in het Nederlands.
IT SHOULD NOT BE MARKDOWN, JUST PLAIN TEXT.
"""

    return system_prompt, user_prompt


def create_system_prompt_for_quality_analysis() -> str:
    return """
    You are a specialized knowledge extraction system for revolving door mechanics.
    Your task is to extract structured knowledge from natural language descriptions
    of revolving door maintenance and repairs.

    You must follow the SPCS framework: Symptom, Problem, Cause, Solution.

    You will receive text from a mechanic describing their work on a revolving door.

    Be precise, literal, and accurate in your extraction. DO NOT MAKE UP INFORMATION THAT WAS NOT PROVIDED.
    USE DUTCH LANGUAGE.
    """


def create_extraction_prompt_for_quality_analysis(text: str) -> str:
    """
    Create the extraction prompt for the LLM for client purposes (SPCS framework) to be saved in a CSV file.

    Args:
        text: The text to extract knowledge from.

    Returns:
        str: The extraction prompt.
    """

    prompt = f"""
    I need you to extract maintenance information from the following transcript for the quality management report:

    ```
    {text}
    ```

    Extract the information in a client-friendly format following the SPCS framework:
    - Symptom: What the door is not doing correctly (what the client might have noticed)
    - Problem: The observed functional problem/failure with the door (from the perspective of the mechanic)
    - Error Codes: Any error codes mentioned (only the error codes, no surrounding text)
    - Cause: Why this problem is occurring
    - Part Involved: Which parts are causing the problem
    - Solution: How it was/will be fixed/solved

    Guidelines for client-friendly extraction:
    - Use clear, language wherever possible, technical terms are fine if necessary
    
    Example output:
    # TODO

    Important:
    - Extract only ONE primary symptom-problem-cause-solution chain

    The output must be valid JSON that can be parsed into the QualityAnalysisReport model.
    
    
    IMPORTANTLY: DO NOT MAKE UP INFORMATION THAT WAS NOT PROVIDED. IF INFORMATION IS ABSENT, SAY "" (empty string).
    USE DUTCH LANUAGE.
    """

    return prompt
