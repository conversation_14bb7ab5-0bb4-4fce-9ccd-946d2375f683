"""Builds knowledge graphs from extracted data."""

import logging
import uuid
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional

from app.core.exceptions import KnowledgeExtractionError, EntityNotFoundError
from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgeRelationship,
    KnowledgeType,
    RelationshipType,
)
from app.services.nlp.extraction.constants import (
    NodeProperty,
    TacitKnowledgeType,
    ExtractionField,
)

from app.services.analytics.scoring import KnowledgeScoringService
import random

from app.core.logging import log_extraction

logger = log_extraction()


class KnowledgeGraphBuilder:
    """Builds knowledge graphs from extracted OCS data."""

    def __init__(self, knowledge_repo, door_repo, embedding_service):
        self.knowledge_repo = knowledge_repo
        self.door_repo = door_repo
        self.embedding_service = embedding_service
        self.scoring_service = KnowledgeScoringService(knowledge_repo=knowledge_repo)

    # Modify the _process_node method to be type-aware in similarity matching:
    async def _process_node(
        self,
        node_type: KnowledgeType,
        node_name: str,
        node_description: str,
        node_properties: Dict[str, Any],
        path_node_ids: Dict[str, str],
        nodes: List[KnowledgeNode],
        node_id_map: Dict[str, str],
        mechanic_id: Optional[str],
        now: datetime,
        component_key: str = None,
    ) -> str:
        """
        Process a single node with consistent type-aware similarity detection.

        Args:
            node_type: The type of knowledge node
            node_name: Name of the node
            node_description: Description of the node
            node_properties: Properties of the node
            path_node_ids: Dict to store node IDs for path creation
            nodes: List of nodes to append to
            node_id_map: Map of original IDs to stored IDs
            mechanic_id: ID of the mechanic creating the node
            now: Current timestamp
            component_key: Key to store the node ID in path_node_ids (if applicable)

        Returns:
            str: The ID of the created or found similar node
        """
        logger.info(
            f"🎯 _process_node called with type: {node_type.value}, name: '{node_name}'"
        )
        logger.info(
            f"🎯 _process_node called with type: {node_type.value}, name: '{node_name}'"
        )
        # Skip processing if name and description are both empty
        if not node_name.strip() and not node_description.strip():
            logger.warning(
                f"Skipping empty node (name='{node_name}', description='{node_description}')"
            )
            logger.warning(
                f"Skipping empty node (name='{node_name}', description='{node_description}')"
            )
            return None

        node_id = str(uuid.uuid4())
        node = KnowledgeNode(
            id=node_id,
            type=node_type,
            name=node_name,
            description=node_description,
            properties=node_properties or {},
            created_at=now,
            updated_at=now,
            created_by=mechanic_id,
        )

        # Log the node being processed with EXACT type
        node_type_name = node_type.value
        logger.info(f"Processing {node_type_name} node: '{node_name}' (ID: {node_id})")

        # Check for similar existing nodes with TYPE-AWARE matching
        try:
            # Only look for similar nodes if we have meaningful content
            if node_name.strip() and node_description.strip():
                similar_nodes = await self.knowledge_repo.find_similar_nodes(
                    node_type, node.name, node.description, node.properties
                )

                if similar_nodes:
                    # IMPORTANT Only use similar node if it has the SAME TYPE
                    # @TODO To be discussed: Should we do that?
                    for existing_node in similar_nodes:
                        # Check if the existing node has the correct type AND meaningful content
                        if (
                            existing_node.type == node_type
                            and existing_node.name.strip()
                            and existing_node.description.strip()
                        ):

                            logger.info(
                                f"Found similar {node_type_name} node with matching type: {existing_node.name}"
                            )
                        if (
                            existing_node.type == node_type
                            and existing_node.name.strip()
                            and existing_node.description.strip()
                        ):

                            logger.info(
                                f"Found similar {node_type_name} node with matching type: {existing_node.name}"
                            )

                            if existing_node.id not in node_id_map:
                                nodes.append(existing_node)
                                node_id_map[existing_node.id] = existing_node.id

                            if component_key:
                                path_node_ids[component_key] = existing_node.id

                            return existing_node.id
                        else:
                            logger.info(
                                f"Found similar node but wrong type: existing={existing_node.type.value}, needed={node_type_name} - skipping"
                            )

                            logger.info(
                                f"Found similar node but wrong type: existing={existing_node.type.value}, needed={node_type_name} - skipping"
                            )

            # No similar node with correct type found, create new one
            logger.info(f"Creating new {node_type_name} node: {node.name}")
            nodes.append(node)
            node_id_map[node_id] = node_id

            if component_key:
                path_node_ids[component_key] = node_id

            return node_id

        except Exception as e:
            logger.error(
                f"Error during similarity check for {node_type_name}: {str(e)}"
            )
            logger.error(
                f"Error during similarity check for {node_type_name}: {str(e)}"
            )
            # If similarity check fails, create a new node
            logger.info(f"Creating new {node_type_name} node due to error: {node.name}")
            nodes.append(node)
            node_id_map[node_id] = node_id

            if component_key:
                path_node_ids[component_key] = node_id

            return node_id

    async def _create_relationship(
        self,
        source_id: str,
        target_id: str,
        relationship_type: RelationshipType,
        mechanic_id: Optional[str],
        now: datetime,
        relationships: List[KnowledgeRelationship],
        trust: Optional[float] = None,
    ) -> None:
        """
        Create a relationship between two nodes and add it to the relationships list.

        Args:
            source_id: ID of the source node
            target_id: ID of the target node
            relationship_type: Type of relationship
            trust: Trust score for the relationship
            mechanic_id: ID of the mechanic creating the relationship
            now: Current timestamp
            relationships: List to append the relationship to
        """
        relationship_kwargs = {
            "source_id": source_id,
            "target_id": target_id,
            "type": relationship_type,
            "created_at": now,
            "updated_at": now,
            "created_by": mechanic_id,
        }

        if trust is not None:
            relationship_kwargs["trust_score"] = trust

        logger.info(
            f"Creating relationship: {relationship_type.value} from {source_id} to {target_id} with trust {trust}"
        )

        relationship = KnowledgeRelationship(**relationship_kwargs)
        relationships.append(relationship)

    async def build_knowledge_graph(
        self,
        data: Dict[str, Any],
        mechanic_id: Optional[str],
        experience_years: float,
        door_type,  # Use the passed door_type object
    ) -> Tuple[List[KnowledgeNode], List[KnowledgeRelationship], List[str]]:
        """
        Build knowledge nodes and relationships from extracted data.
        Updated for OCS structure with proper door type handling.
        
        Returns:
            Tuple containing:
            - List of knowledge nodes
            - List of relationships
            - List of node IDs for tracking
        """
        try:
            now = datetime.utcnow()
            nodes = []
            relationships = []
            node_id_map = {}  # Store IDs of created nodes for creating relationships
            created_node_ids = []  # Track all created node IDs

            # Handle door_type properly - ensure we have a proper object with ID
            if door_type is None:
                logger.error("Door type is None")
                raise KnowledgeExtractionError(
                    "Door type is required but not found. Please provide a valid door model."
                )

            # CRITICAL CHANGE: Ensure we have a valid door type object
            door_type_id = None

            # Case 1: door_type is an object with an id attribute
            if hasattr(door_type, "id") and door_type.id:
                door_type_id = door_type.id
                logger.info(f"Using door_type_id from object: {door_type_id}")

            # Case 2: door_type is a dictionary with an id key
            elif isinstance(door_type, dict) and "id" in door_type:
                door_type_id = door_type["id"]
                logger.info(f"Using door_type_id from dictionary: {door_type_id}")

            # Case 3: door_type is a string, but we need to look it up
            elif isinstance(door_type, str):
                # THIS IS THE KEY CHANGE: We don't use the string as an ID!
                # Instead, we need to lookup the door type node by model
                try:
                    # Use door cache if available
                    from app.services.cache.door_cache import get_door_cache

                    door_cache = get_door_cache()
                    door_obj = door_cache.get_by_model(door_type)

                    if door_obj and hasattr(door_obj, "id"):
                        door_type_id = door_obj.id
                        logger.info(
                            f"Found door type ID {door_type_id} for model {door_type} in cache"
                        )
                except Exception as e:
                    logger.error(f"Error looking up door type: {str(e)}")

                # If we couldn't find it in the cache, raise an error
                if not door_type_id:
                    raise KnowledgeExtractionError(
                        f"Could not find door type with model '{door_type}'. "
                        "Please ensure the door model exists in the database before extraction."
                    )

            # If we still don't have a door type ID, raise an error
            if not door_type_id:
                raise KnowledgeExtractionError(
                    "Could not determine door type ID for knowledge extraction."
                )

            logger.info(f"Successfully resolved door_type_id to: {door_type_id}")

            if ExtractionField.KNOWLEDGE_PATHS in data:
                logger.info(
                    f"Found {len(data[ExtractionField.KNOWLEDGE_PATHS])} knowledge paths"
                )
            else:
                logger.warning(f"No {ExtractionField.KNOWLEDGE_PATHS} found in data")

            # Process multiple knowledge paths
            for path_index, path in enumerate(
                data.get(ExtractionField.KNOWLEDGE_PATHS, [])
            ):
                logger.info(f"entering OCS loop - Processing path {path_index+1}")
                logger.info(f"Processing knowledge path {path_index+1}")
                logger.debug(f"Path contains keys: {path.keys()}")

                path_node_ids = {}  # Store node IDs for this path

                # Process Observation component
                if "observation" in path:
                    if path["observation"] is None:
                        logger.warning(
                            f"Observation is None in path {path_index+1}, skipping"
                        )
                    else:
                        logger.info(f"Processing observation in path {path_index+1}")
                        observation_data = path["observation"]

                        # Create nested attributes structure
                        attributes = {}

                        obs_name = observation_data.get(NodeProperty.NAME, "")
                        obs_desc = observation_data.get(NodeProperty.DESCRIPTION, "")

                        # Extract observation attributes and group them
                        visual_observation = observation_data.get("visual_observation")
                        auditory_observation = observation_data.get(
                            "auditory_observation"
                        )
                        positional_observation = observation_data.get(
                            "positional_observation"
                        )
                        error_codes = observation_data.get("error_codes")
                        related_parts = observation_data.get("related_parts", [])

                        # Store all observation attributes under 'attributes' key
                        if visual_observation:
                            attributes["visual_observation"] = visual_observation
                        if auditory_observation:
                            attributes["auditory_observation"] = auditory_observation
                        if positional_observation:
                            attributes["positional_observation"] = (
                                positional_observation
                            )
                        if error_codes:
                            attributes["error_codes"] = error_codes
                        if related_parts:
                            attributes["related_parts"] = related_parts

                        # Create observation properties with nested attributes
                        observation_properties = {}
                        if attributes:  # Only add if we have attributes
                            observation_properties["attributes"] = attributes

                        # Process the observation node
                        observation_id = await self._process_node(
                            node_type=KnowledgeType.OBSERVATION,
                            node_name=observation_data.get(NodeProperty.NAME, ""),
                            node_description=observation_data.get(
                                NodeProperty.DESCRIPTION, ""
                            ),
                            node_properties=observation_properties,
                            path_node_ids=path_node_ids,
                            nodes=nodes,
                            node_id_map=node_id_map,
                            mechanic_id=mechanic_id,
                            now=now,
                            component_key="observation",
                        )

                        created_node = next(
                            (n for n in nodes if n.id == observation_id), None
                        )

                        logger.info(
                            f"Created observation node with ID {observation_id}"
                        )
                        path_node_ids["observation"] = observation_id
                        logger.info(
                            f"Created observation node with ID {observation_id}"
                        )
                        path_node_ids["observation"] = observation_id
                        logger.info(f"Added observation node with ID {observation_id}")

                # Process Cause component
                if "cause" in path:
                    if path["cause"] is None:
                        logger.warning(
                            f"Cause is None in path {path_index+1}, skipping"
                        )
                    else:
                        logger.info(f"Processing cause in path {path_index+1}")
                        cause_data = path["cause"]
                        cause_id = await self._process_node(
                            node_type=KnowledgeType.CAUSE,
                            node_name=cause_data.get(NodeProperty.NAME, ""),
                            node_description=cause_data.get(
                                NodeProperty.DESCRIPTION, ""
                            ),
                            node_properties={},  # No special properties for cause nodes
                            path_node_ids=path_node_ids,
                            nodes=nodes,
                            node_id_map=node_id_map,
                            mechanic_id=mechanic_id,
                            now=now,
                            component_key="cause",
                        )
                        path_node_ids["cause"] = cause_id
                        logger.info(f"Added cause node with ID {cause_id}")

                # Process Solution component
                if "solution" in path:
                    if path["solution"] is None:
                        logger.warning(
                            f"Solution is None in path {path_index+1}, skipping"
                        )
                    else:
                        logger.info(f"Processing solution in path {path_index+1}")
                        solution_data = path["solution"]
                        solution_properties = {}

                        # Handle special properties for solution nodes
                        parts_involved = solution_data.get(
                            NodeProperty.PARTS_INVOLVED, []
                        )
                        part_numbers = solution_data.get(NodeProperty.PART_NUMBERS, [])

                        if parts_involved:
                            solution_properties[NodeProperty.PARTS_INVOLVED] = (
                                parts_involved
                            )
                        if part_numbers:
                            solution_properties[NodeProperty.PART_NUMBERS] = (
                                part_numbers
                            )

                        solution_id = await self._process_node(
                            node_type=KnowledgeType.SOLUTION,
                            node_name=solution_data.get(NodeProperty.NAME, ""),
                            node_description=solution_data.get(
                                NodeProperty.DESCRIPTION, ""
                            ),
                            node_properties=solution_properties,
                            path_node_ids=path_node_ids,
                            nodes=nodes,
                            node_id_map=node_id_map,
                            mechanic_id=mechanic_id,
                            now=now,
                            component_key="solution",
                        )
                        path_node_ids["solution"] = solution_id
                        logger.info(f"Added solution node with ID {solution_id}")

                # Create relationships within this path
                logger.info(f"Creating relationships for path {path_index+1}")

                # Create OCS relationships
                if "observation" in path_node_ids and "cause" in path_node_ids:
                    logger.info(
                        f"Creating OBSERVED_WITH relationship: observation -> cause"
                    )
                    await self._create_relationship(
                        source_id=path_node_ids["observation"],
                        target_id=path_node_ids["cause"],
                        relationship_type=RelationshipType.OBSERVED_WITH,
                        mechanic_id=mechanic_id,
                        now=now,
                        relationships=relationships,
                    )

                if "cause" in path_node_ids and "solution" in path_node_ids:
                    logger.info(f"Creating RESOLVED_BY relationship: cause -> solution")
                    trust = self.scoring_service.calculate_init_trust(experience_years)
                    logger.info(f"Calculated trust score: {trust}")
                    await self._create_relationship(
                        source_id=path_node_ids["cause"],
                        target_id=path_node_ids["solution"],
                        relationship_type=RelationshipType.RESOLVED_BY,
                        trust=trust,
                        mechanic_id=mechanic_id,
                        now=now,
                        relationships=relationships,
                    )

                # Connect each solution to the door type
                if "solution" in path_node_ids:
                    logger.info(
                        f"Creating APPLIES_TO relationship: solution -> door_type"
                    )
                    await self._create_relationship(
                        source_id=path_node_ids["solution"],
                        target_id=door_type_id,
                        relationship_type=RelationshipType.APPLIES_TO,
                        mechanic_id=mechanic_id,
                        now=now,
                        relationships=relationships,
                    )

            # Process environment if available
            if ExtractionField.ENVIRONMENT in data:
                if data[ExtractionField.ENVIRONMENT] is None:
                    logger.warning("Environment data is None, skipping")
                else:
                    logger.info("Processing environment data")
                    environment_data = data[ExtractionField.ENVIRONMENT]
                    environment_id = await self._process_node(
                        node_type=KnowledgeType.GENERAL,
                        node_name=environment_data.get(ExtractionField.CONDITION, ""),
                        node_description=environment_data.get(
                            NodeProperty.DESCRIPTION, ""
                        ),
                        node_properties={},
                        path_node_ids={},
                        nodes=nodes,
                        node_id_map=node_id_map,
                        mechanic_id=mechanic_id,
                        now=now,
                    )
                    logger.info(f"Added environment node with ID {environment_id}")

                    # Connect environment to causes
                    logger.info("Creating relationships for environment")
                    for path in data.get(ExtractionField.KNOWLEDGE_PATHS, []):
                        if "cause" in path and path["cause"] is not None:
                            cause_node_id = None
                            for node in nodes:
                                if (
                                    node.type == KnowledgeType.CAUSE
                                    and node.name
                                    == path["cause"].get(NodeProperty.NAME, "")
                                ):
                                    cause_node_id = node.id
                                    break

                            if cause_node_id:
                                logger.info(
                                    f"Creating AFFECTS relationship: environment -> cause"
                                )
                                await self._create_relationship(
                                    source_id=environment_id,
                                    target_id=cause_node_id,
                                    relationship_type=RelationshipType.AFFECTS,
                                    mechanic_id=mechanic_id,
                                    now=now,
                                    relationships=relationships,
                                )

                    # Connect environment to door type
                    logger.info(
                        f"Creating APPLIES_TO relationship: environment -> door_type"
                    )
                    await self._create_relationship(
                        source_id=environment_id,
                        target_id=door_type_id,
                        relationship_type=RelationshipType.APPLIES_TO,
                        mechanic_id=mechanic_id,
                        now=now,
                        relationships=relationships,
                    )

            # Process tacit knowledge nodes
            tacit_knowledge = data.get(ExtractionField.TACIT_KNOWLEDGE, [])
            if tacit_knowledge:
                logger.info(f"Processing {len(tacit_knowledge)} tacit knowledge items")
            else:
                logger.info("No tacit knowledge to process")

            for i, item in enumerate(tacit_knowledge):
                if item is None:
                    logger.warning(f"Tacit knowledge item {i} is None, skipping")
                    continue

                logger.info(f"Processing tacit knowledge item {i+1}")

                # Determine tacit knowledge type
                tacit_type = KnowledgeType.GENERAL
                try:
                    type_str = item.get(
                        ExtractionField.TYPE, TacitKnowledgeType.GENERAL
                    )
                    tacit_type = KnowledgeType(type_str)
                except ValueError:
                    logger.warning(
                        f"Invalid tacit knowledge type: {type_str}, using GENERAL"
                    )
                    tacit_type = KnowledgeType.GENERAL

                # Process tacit knowledge node
                tacit_id = await self._process_node(
                    node_type=tacit_type,
                    node_name=item.get(NodeProperty.NAME, ""),
                    node_description=item.get(NodeProperty.DESCRIPTION, ""),
                    node_properties={},
                    path_node_ids={},
                    nodes=nodes,
                    node_id_map=node_id_map,
                    mechanic_id=mechanic_id,
                    now=now,
                )
                logger.info(f"Added tacit knowledge node with ID {tacit_id}")

                # Connect tacit knowledge to relevant OCS components
                logger.info("Finding relevant component for tacit knowledge")
                connected = False
                for path in data.get(ExtractionField.KNOWLEDGE_PATHS, []):
                    most_relevant_component = None
                    for component in ["observation", "cause", "solution"]:
                        if component in path and path[component] is not None:
                            # Find the node ID for this component
                            component_node_id = None
                            for node in nodes:
                                component_type = (
                                    KnowledgeType.OBSERVATION
                                    if component == "observation"
                                    else (
                                        KnowledgeType.CAUSE
                                        if component == "cause"
                                        else KnowledgeType.SOLUTION
                                    )
                                )
                                if node.type == component_type and node.name == path[
                                    component
                                ].get(NodeProperty.NAME, ""):
                                    component_node_id = node.id
                                    break

                            if component_node_id:
                                most_relevant_component = component_node_id
                                break

                    if most_relevant_component:
                        logger.info(
                            f"Creating RELATES_TO relationship: tacit -> {component}"
                        )
                        await self._create_relationship(
                            source_id=tacit_id,
                            target_id=most_relevant_component,
                            relationship_type=RelationshipType.RELATES_TO,
                            mechanic_id=mechanic_id,
                            now=now,
                            relationships=relationships,
                        )
                        connected = True
                        break  # Only connect to one component per path

                # Always connect tacit knowledge to door type
                logger.info(f"Creating APPLIES_TO relationship: tacit -> door_type")
                await self._create_relationship(
                    source_id=tacit_id,
                    target_id=door_type_id,
                    relationship_type=RelationshipType.APPLIES_TO,
                    mechanic_id=mechanic_id,
                    now=now,
                    relationships=relationships,
                )

            logger.info(
                f"Built knowledge graph with {len(nodes)} nodes and {len(relationships)} relationships"
            )

            logger.info("Saving nodes and relationships to Neo4j database...")

            # Save all nodes to the database
            for node in nodes:
                try:
                    saved_node = await self.knowledge_repo.create_knowledge_node(node)
                    logger.info(
                        f"Saved node to database: {saved_node.id} ({saved_node.type.value}) - {saved_node.name}"
                    )
                    created_node_ids.append(saved_node.id)
                except Exception as e:
                    logger.error(f"Failed to save node {node.id}: {str(e)}")

            # Save all relationships to the database
            for relationship in relationships:
                try:
                    saved_rel = await self.knowledge_repo.create_knowledge_relationship(
                        relationship
                    )
                    logger.info(
                        f"Saved relationship to database: {saved_rel.source_id} -> {saved_rel.target_id} ({saved_rel.type.value})"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to save relationship {relationship.source_id} -> {relationship.target_id}: {str(e)}"
                    )

            logger.info("Successfully saved all nodes and relationships to Neo4j!")

            logger.info(f"Created {len(created_node_ids)} node IDs: {created_node_ids}")
            return nodes, relationships, created_node_ids

        except Exception as e:
            logger.error(f"Error in build_knowledge_graph: {str(e)}")
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
            raise
