from itertools import combinations


class OCSComponent:
    """Constants for OCS framework components."""

    OBSERVATION = "observation"
    CAUSE = "cause"
    SOLUTION = "solution"
    ALL = ["observation", "cause", "solution"]


class TacitKnowledgeType:
    """Constants for knowledge types in extraction data."""

    TECHNIQUE = "TECHNIQUE"
    SHORTCUT = "SHORTCUT"
    OBSERVATION = "OBSERVATION"
    GENERAL = "GENERAL"


class NodeProperty:
    """Constants for node properties."""

    NAME = "name"
    DESCRIPTION = "description"
    PARTS_INVOLVED = "parts_involved"
    PART_NUMBERS = "part_numbers"
    VISUAL_OBSERVATION = "visual_observation"
    AUDITORY_OBSERVATION = "auditory_observation"
    POSITIONAL_OBSERVATION = "positional_observation"
    ERROR_CODES = "error_codes"
    RELATED_PARTS = "related_parts"


class ExtractionField:
    """Constants for extraction data fields."""

    DOOR_MODEL = "door_model"
    ENVIRONMENT = "environment"
    KNOWLEDGE_PATHS = "knowledge_paths"
    TACIT_KNOWLEDGE = "tacit_knowledge"
    TRUST = "trust"
    CONDITION = "condition"
    TYPE = "type"
    PATH_COMPLETENESS = "path_completeness"
    RELATIONSHIP_ACCURACY = "relationship_accuracy"


class TrustValues:
    """Default trust values."""

    PATH_COMPLETENESS_DEFAULT = 0.8
    RELATIONSHIP_ACCURACY_DEFAULT = 0.7
    DEFAULT_TRUST_SCORE = 0.5


class ErrorMessage:
    """Error messages for knowledge extraction."""

    OPENAI_API_KEY_NOT_SET = "OpenAI API key is not set"
    DOOR_MODEL_REQUIRED = (
        "Door model/type is required but was not mentioned in the text or provided."
    )
    DOOR_MODEL_NOT_FOUND = (
        "Door model '{}' not found in the database. Valid models are: {}"
    )
    NO_EXTRACTION_DATA = "LLM extraction returned no data"
    NO_ADMIN_SUMMARY = "Failed to generate admin summary"
    INVALID_KNOWLEDGE_STRUCTURE = "Invalid knowledge structure: {}"
    FAILED_BUILD_GRAPH = "Failed to build knowledge graph: {}"
    FAILED_EXTRACT_KNOWLEDGE = "Failed to extract knowledge: {}"
    MIN_ONE_PATH = "At least one knowledge path must be extracted."
    PATH_MIN_ONE_COMPONENT = (
        "Knowledge path {} must contain at least one component of the OCS framework."
    )
    PARSE_RESPONSE_ERROR = "Failed to parse extraction results"
    LLM_EXTRACTION_ERROR = "Failed to perform extraction: {}"


class SearchConstants:
    """Constants for search service."""

    # Constants from search_service.py
    MAX_PATHS = 5
    REQUIRED_PATHS_PER_TIER = 5
    MAX_OBSERVATIONS_PER_QUERY = 5
    MAX_CAUSE_PER_OBSERVATION = 1
    MAX_SOLUTIONS_PER_CAUSE = 1
    ENABLE_LLM_VERIFICATION = False

    DEFAULT_SEARCH_LIMIT = 10
    SEMANTIC_SEARCH_KNN_MULTIPLIER = 3  # multiplier for KNN search to increase chances of finding relevant observations

    SIMILARITY_THRESHOLD = 0.5  # default similarity threshold for observation embeddings (overall observation embedding)
    DEFAULT_SIMILARITY_FOR_EXACT_MATCH = (
        0.8  # Default similarity score for exact matches (e.g. error code)
    )

    # similarity thresholds for attribute embeddings
    SIMILARITY_THRESHOLD_VISUAL = 0.5
    SIMILARITY_THRESHOLD_AUDITORY = 0.5
    SIMILARITY_THRESHOLD_POSITIONAL = 0.5

    ATTR_OVERALL = "observation"  # overall observation embedding
    ATTR_VISUAL = "visual_observation"
    ATTR_AUDITORY = "auditory_observation"
    ATTR_POSITIONAL = "positional_observation"
    ATTR_ERROR_CODES = "error_codes"
    ATTR_RELATED_PARTS = "related_parts"
    OBSERVATION_ATTRIBUTES = [
        ATTR_OVERALL,
        ATTR_VISUAL,
        ATTR_AUDITORY,
        ATTR_POSITIONAL,
        ATTR_ERROR_CODES,
        ATTR_RELATED_PARTS,
    ]
    ATTR_IMPORTANCE_ORDER = [
        ATTR_ERROR_CODES,
        ATTR_RELATED_PARTS,
        ATTR_VISUAL,
        ATTR_AUDITORY,
        ATTR_POSITIONAL,
    ]  # Priority order for attributes in permutation search


# Constants for default values
DEFAULT_EXPERIENCE = 0
MAX_EXPERIENCE = 10
DEFAULT_TRUST = 0.7
MODEL_NAME = "gpt-4.1"  # TODO: move this to settings
TEMPERATURE = 0.2
FEEDBACK_WEIGHT = 0.5  # Weight for how much the total feedback score affects an update of the trust score
TRUST_WARMUP_PERIOD = 10
POSSIBLE_CODES = ["".join(c) for r in range(1, 7) for c in combinations("ABCDEF", r)]
