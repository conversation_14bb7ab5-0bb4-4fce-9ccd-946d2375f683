"""Service to organize knowledge nodes and relationships into meaningful paths."""

import logging
from typing import List, Dict, Any, Optional
from uuid import uuid4

from app.models.schemas.knowledge import KnowledgePathResponse
from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgeRelationship,
    KnowledgeType,
    RelationshipType,
    KnowledgePath,
)
from app.core.logging import log_extraction

logger = log_extraction()


class PathService:
    """Service for organizing knowledge nodes and relationships into paths."""

    @staticmethod
    def generate_knowledge_paths(
        nodes: List[KnowledgeNode], relationships: List[KnowledgeRelationship]
    ) -> List[KnowledgePathResponse]:
        """
        Organize nodes and relationships into meaningful knowledge paths.
        This function tries to create paths based on the OCS (Observation-Cause-Solution) model.

        Args:
            nodes: List of KnowledgeNode objects
            relationships: List of KnowledgeRelationship objects

        Returns:
            List of KnowledgePathResponse objects representing paths through the knowledge graph
        """
        # Group nodes by type
        nodes_by_type = {
            KnowledgeType.OBSERVATION: [],
            KnowledgeType.CAUSE: [],
            KnowledgeType.SOLUTION: [],
        }

        # Create a lookup map for nodes by ID
        node_map = {}
        for node in nodes:
            node_map[node.id] = node
            if node.type in nodes_by_type:
                nodes_by_type[node.type].append(node)

        # Create a relationship map (source_id -> target_id -> relationship)
        relationship_map = {}
        for rel in relationships:
            if rel.source_id not in relationship_map:
                relationship_map[rel.source_id] = {}
            relationship_map[rel.source_id][rel.target_id] = rel

        # Build paths starting from observations
        paths = []
        start_nodes = []

        # Prefer to start with observations if available
        if nodes_by_type[KnowledgeType.OBSERVATION]:
            start_nodes = nodes_by_type[KnowledgeType.OBSERVATION]
        # Otherwise try causes
        elif nodes_by_type[KnowledgeType.CAUSE]:
            start_nodes = nodes_by_type[KnowledgeType.CAUSE]
        # If neither exist, use solutions
        elif nodes_by_type[KnowledgeType.SOLUTION]:
            start_nodes = nodes_by_type[KnowledgeType.SOLUTION]

        processed_nodes = set()

        # Process each potential start node
        for start_node in start_nodes:
            if start_node.id in processed_nodes:
                continue

            path = []
            path_relationships = []
            current_node = start_node
            processed_nodes.add(current_node.id)
            path.append(current_node)

            # Try to build a path following relationships
            for _ in range(3):  # Maximum path length of 4 nodes
                if (
                    current_node.id in relationship_map
                    and relationship_map[current_node.id]
                ):
                    # Find all potential next steps
                    next_steps = relationship_map[current_node.id]

                    # Choose the next step with highest trust score
                    best_next = None
                    best_rel = None
                    best_score = -1

                    for target_id, rel in next_steps.items():
                        if (
                            target_id in node_map
                            and target_id not in processed_nodes
                            and rel.trust_score > best_score
                        ):
                            best_next = node_map[target_id]
                            best_rel = rel
                            best_score = rel.trust_score

                    if best_next and best_rel:
                        path.append(best_next)
                        path_relationships.append(best_rel)
                        processed_nodes.add(best_next.id)
                        current_node = best_next
                    else:
                        break
                else:
                    break

            # Create path response if we have at least 2 nodes
            if len(path) >= 2:
                # Calculate average trust score for the path
                avg_trust = (
                    sum(rel.trust_score for rel in path_relationships)
                    / len(path_relationships)
                    if path_relationships
                    else 0.8
                )

                # Create domain path object for ID generation
                domain_path = KnowledgePath(
                    nodes=path,
                    relationships=path_relationships,
                    trust_score=avg_trust,
                )

                # Generate deterministic path ID
                path_id = domain_path.generate_path_id()

                paths.append(
                    KnowledgePathResponse(
                        id=path_id,
                        nodes=path,
                        relationships=path_relationships,
                        trust_score=avg_trust,
                    )
                )

        # If we couldn't build any meaningful paths, create single-node paths
        if not paths:
            # Group single-node paths by type
            for type_key, type_nodes in nodes_by_type.items():
                if type_nodes:
                    # Create a domain KnowledgePath object for ID generation
                    domain_path = KnowledgePath(
                        nodes=type_nodes,
                        relationships=[],
                        trust_score=0.7,
                    )
                    path_id = domain_path.generate_path_id()

                    paths.append(
                        KnowledgePathResponse(
                            id=path_id,
                            nodes=type_nodes,
                            relationships=[],
                            trust_score=0.7,  # Default trust
                        )
                    )

        # Add a catch-all path for any miscellaneous nodes if no paths were found
        if not paths and nodes:
            # Create a domain KnowledgePath object for ID generation
            domain_path = KnowledgePath(
                nodes=nodes[:5], relationships=[], trust_score=0.6
            )
            path_id = domain_path.generate_path_id()

            paths.append(
                KnowledgePathResponse(
                    id=path_id,
                    nodes=nodes[:5],  # Limit to 5 nodes
                    relationships=[],
                    trust_score=0.6,  # Lower trust for miscellaneous path
                )
            )

        return paths
