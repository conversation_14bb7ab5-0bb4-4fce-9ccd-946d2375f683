from collections import OrderedDict
import logging
import time
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
import numpy as np
from functools import lru_cache

logger = logging.getLogger(__name__)


class EmbeddingCache:
    """
    Cache for node embeddings to reduce API calls and improve performance.
    Implements a singleton pattern similar to DoorTypeCache.
    """

    _instance = None

    @classmethod
    def get_instance(cls):
        """Get or create the singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        """Initialize the embedding cache."""
        if EmbeddingCache._instance is not None:
            raise RuntimeError("Use get_instance() instead of constructor")

        # Use OrderedDict for O(1) operations and proper LRU tracking
        self.node_embeddings = (
            OrderedDict()
        )  # {node_id: (embedding, timestamp)}
        self.max_cache_size = 10000  # Maximum number of embeddings to cache
        self.ttl = 3600 * 24 * 7  # Cache TTL in seconds (7 days)
        self.is_preloaded = False
        logger.info("Embedding cache initialized")

    async def preload(self, query_executor, limit: int = 1000):
        """
        Preload the most frequently accessed nodes' embeddings into cache.

        Args:
            query_executor: Any object that can execute a query with the execute_read_query method
            limit: Maximum number of nodes to preload
        """
        try:
            logger.info(f"Preloading embeddings for up to {limit} nodes")

            # Query to get nodes with embeddings
            query = """
            MATCH (n)
            WHERE (n:SYMPTOM OR n:PROBLEM OR n:CAUSE OR n:SOLUTION)
            AND n.embedding IS NOT NULL
            RETURN n.id as id, n.embedding as embedding
            LIMIT $limit
            """

            results = await query_executor.execute_read_query(
                query, {"limit": limit}
            )

            count = 0
            for result in results:
                node_id = result["id"]
                embedding_data = result["embedding"]

                if embedding_data:
                    try:
                        # Convert from database format to numpy array
                        embedding = np.array(embedding_data)
                        self.set(node_id, embedding)
                        count += 1
                    except Exception as e:
                        logger.error(
                            f"Error loading embedding for node {node_id}: {str(e)}"
                        )

            self.is_preloaded = True
            logger.info(f"Successfully preloaded {count} embeddings")

        except Exception as e:
            logger.error(f"Error preloading embeddings: {str(e)}")

    def get(self, node_id: str) -> Optional[np.ndarray]:
        """
        Get embedding for a node if it exists in cache and is not expired.
        Updates the item's position in the LRU cache.
        """
        if node_id in self.node_embeddings:
            embedding, timestamp = self.node_embeddings[node_id]

            # Check if the embedding is expired
            if time.time() - timestamp <= self.ttl:
                # Move the item to the end (most recently used position)
                self.node_embeddings.move_to_end(node_id)
                return embedding

            # Remove expired embedding
            del self.node_embeddings[node_id]

        return None

    def set(self, node_id: str, embedding: np.ndarray):
        """
        Set embedding for a node in the cache.
        """
        # If cache is full, remove least recently used item
        if len(self.node_embeddings) >= self.max_cache_size:
            # Remove the first item (least recently used)
            self.node_embeddings.popitem(last=False)

        # Add or update the item (automatically goes to the end - most recently used)
        self.node_embeddings[node_id] = (embedding, time.time())

    def clear(self):
        """Clear the entire cache."""
        self.node_embeddings.clear()
        logger.info("Embedding cache cleared")

    def get_batch(self, node_ids: List[str]) -> Dict[str, np.ndarray]:
        """
        Get embeddings for multiple nodes if they exist in cache.

        Args:
            node_ids: List of node IDs

        Returns:
            Dict[str, np.ndarray]: Dictionary of node_id to embedding
        """
        result = {}
        for node_id in node_ids:
            embedding = self.get(node_id)
            if embedding is not None:
                result[node_id] = embedding

        return result

    def set_batch(self, embeddings: Dict[str, np.ndarray]):
        """
        Set embeddings for multiple nodes.

        Args:
            embeddings: Dictionary of node_id to embedding
        """
        for node_id, embedding in embeddings.items():
            self.set(node_id, embedding)


@lru_cache()
def get_embedding_cache():
    """
    Get the embedding cache singleton instance.
    Can be used with FastAPI dependency injection.
    """
    return EmbeddingCache.get_instance()
