import logging
from typing import Dict, Optional
from fastapi import Depends

from app.core.config import get_settings
from app.services.database.repositories.door_repo import DoorRepository

logger = logging.getLogger(__name__)


class DoorTypeCache:
    """Global cache for door type information"""

    _instance = None  # Singleton instance

    def __init__(self, settings=Depends(get_settings)):
        self.settings = settings
        self.cache = {}
        self.loaded = False

    @classmethod
    def get_instance(cls, settings=Depends(get_settings)):
        """Get singleton instance with dependency injection"""
        if cls._instance is None:
            cls._instance = cls(settings)
        return cls._instance

    async def preload(self, door_repo: DoorRepository):
        """Preload all door types from database"""
        if self.loaded:
            return

        try:
            query = "MATCH (d:DOOR_TYPE) RETURN d"
            results = await door_repo.db.execute_query(query, {})

            for result in results:
                door_type = door_repo._dict_to_door_type(result["d"])

                # Cache by ID
                self.cache[door_type.id] = door_type

                # Cache by model (case-insensitive)
                if door_type.model:
                    self.cache[door_type.model.lower()] = door_type

                # Cache by manufacturer + model
                if door_type.manufacturer and door_type.model:
                    mfg_model = (
                        f"{door_type.manufacturer} {door_type.model}".lower()
                    )
                    self.cache[mfg_model] = door_type

            self.loaded = True
            logger.info(
                f"Preloaded {len(results)} door types into global cache"
            )
        except Exception as e:
            logger.error(f"Error preloading door types: {str(e)}")

    def get_by_id(self, door_type_id: str):
        """Get door type by ID"""
        return self.cache.get(door_type_id)

    def get_by_model(self, model: str):
        """Get door type by model with flexible matching"""
        if not model or not isinstance(model, str):
            return None

        model_lower = model.lower()

        # Try exact match first
        if model_lower in self.cache:
            return self.cache[model_lower]

        # Try manufacturer + model variations
        # Common prefix to remove for better matching
        prefixes = ["boon edam ", "boonedam ", "boon "]
        for prefix in prefixes:
            if model_lower.startswith(prefix):
                clean_model = model_lower[len(prefix) :]
                if clean_model in self.cache:
                    return self.cache[clean_model]

        # Try partial matches
        for key, door_type in self.cache.items():
            # Skip ID-based keys (typically UUIDs)
            if len(key) == 36 and "-" in key:
                continue

            if model_lower in key or key in model_lower:
                return door_type

        return None


# Create a dependency that can be used in other services
def get_door_cache(settings=Depends(get_settings)):
    return DoorTypeCache.get_instance(settings)
