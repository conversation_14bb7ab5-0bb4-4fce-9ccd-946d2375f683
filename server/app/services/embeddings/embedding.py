import logging
import numpy as np
from typing import List
from fastapi import Depends
from openai import OpenAI
from app.core.config import get_settings
from app.services.cache.embedding_cache import get_embedding_cache

# Added import for parallel processing
import concurrent.futures

logger = logging.getLogger(__name__)


class EmbeddingService:
    def __init__(
        self,
        settings=get_settings(),  # Depends(get_settings),
        embedding_cache=get_embedding_cache(),  # =Depends(get_embedding_cache),
    ):
        self.settings = settings
        self.client = None
        self.model_name = "text-embedding-3-large"  # Keeping original model
        self.vector_size = 3072  # Keeping original vector size
        self.initialized = False
        self.initialization_error = None
        self.batch_size = 100  # Max batch size for API requests
        self.embedding_cache = embedding_cache

        # Try to initialize immediately
        try:
            self._init_client()
        except Exception as e:
            self.initialization_error = str(e)
            logger.error(f"Failed to initialize OpenAI client at startup: {str(e)}")

    def _init_client(self):
        """Initialize the OpenAI client with better error handling."""
        if self.client is None and not self.initialization_error:
            try:
                logger.info(f"Initializing OpenAI client for {self.model_name}")
                self.client = OpenAI(api_key=self.settings.OPENAI_API_KEY)
                self.initialized = True
                logger.info("OpenAI client initialized successfully")
            except Exception as e:
                self.initialization_error = str(e)
                logger.error(f"Error initializing OpenAI client: {str(e)}")
                raise RuntimeError(f"Failed to initialize OpenAI client: {str(e)}")

    def generate_embedding(self, text: str) -> np.ndarray:
        """
        Generate an embedding vector for the given text using OpenAI.

        Args:
            text: The text to embed.

        Returns:
            np.ndarray: The embedding vector.
        """
        self._init_client()

        try:
            # Generate embedding via OpenAI API
            response = self.client.embeddings.create(model=self.model_name, input=text)
            embedding = response.data[0].embedding

            return np.array(embedding)
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            # Return a zero vector as fallback
            return np.zeros(self.vector_size)

    def generate_embeddings_batch(self, texts: List[str]) -> List[np.ndarray]:
        """
        Generate embeddings for a list of texts in batches.

        Args:
            texts: List of texts to embed.

        Returns:
            List[np.ndarray]: List of embedding vectors.
        """
        self._init_client()

        if not texts:
            return []

        embeddings = []

        try:
            # Process in batches to stay within API limits
            for i in range(0, len(texts), self.batch_size):
                batch = texts[i : i + self.batch_size]

                # Generate embeddings for the batch
                response = self.client.embeddings.create(
                    model=self.model_name, input=batch
                )

                # Extract embeddings from response
                batch_embeddings = [np.array(item.embedding) for item in response.data]
                embeddings.extend(batch_embeddings)

            return embeddings
        except Exception as e:
            logger.error(f"Error generating batch embeddings: {str(e)}")
            # Return zero vectors as fallback
            return [np.zeros(self.vector_size) for _ in range(len(texts))]

    def calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity with fallback to text-based matching if model fails."""
        # Always try to initialize if not already done
        if not self.initialized and not self.initialization_error:
            try:
                self._init_client()
            except:
                pass

        # If client initialization failed, use text-based fallback
        if self.initialization_error or not self.client:
            return self._text_based_similarity(text1, text2)

        try:
            # Get embeddings for both texts in a single API call
            embeddings = self.generate_embeddings_batch([text1, text2])

            if len(embeddings) != 2:
                return self._text_based_similarity(text1, text2)

            embedding1 = embeddings[0]
            embedding2 = embeddings[1]

            # Calculate cosine similarity
            norm1 = np.linalg.norm(embedding1)
            norm2 = np.linalg.norm(embedding2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            similarity = np.dot(embedding1, embedding2) / (norm1 * norm2)
            return float(similarity)
        except Exception as e:
            logger.error(f"Error calculating similarity: {str(e)}")
            # If embedding calculation fails, fall back to text-based similarity
            return self._text_based_similarity(text1, text2)

    def _text_based_similarity(self, text1: str, text2: str) -> float:
        """Text-based similarity fallback."""
        text1_lower = text1.lower()
        text2_lower = text2.lower()

        # Same fallback logic as before
        if text1_lower == text2_lower:
            return 1.0
        elif text1_lower in text2_lower or text2_lower in text1_lower:
            return 0.8
        else:
            words1 = set(text1_lower.split())
            words2 = set(text2_lower.split())
            common_words = words1.intersection(words2)

            if common_words:
                return 0.4 + (0.4 * len(common_words) / max(len(words1), len(words2)))

            return 0.0

    def batch_calculate_similarities(
        self, query_text: str, candidate_texts: List[str]
    ) -> List[float]:
        try:
            # Generate query embedding
            query_embedding = self.generate_embedding(query_text)

            # Generate all candidate embeddings in a single batch API call
            candidate_embeddings = self.generate_embeddings_batch(candidate_texts)

            # Calculate similarities in parallel (new approach)
            return self._parallel_similarity_calculation(
                query_embedding, candidate_embeddings
            )
        except Exception as e:
            logger.error(f"Error in batch similarity calculation: {str(e)}")
            return [0.0] * len(candidate_texts)

    def batch_calculate_similarities_with_cache(
        self,
        query_text: str,
        candidate_texts: List[str],
        candidate_ids: List[str],
        db_connection=None,  # Keeping the optional database connection parameter
    ) -> List[float]:
        """Calculate similarities without awaiting - returns directly."""
        try:
            # Validate inputs
            if not candidate_ids or len(candidate_ids) != len(candidate_texts):
                logger.warning(
                    "Missing or mismatched node IDs for cache - falling back"
                )
                return self.batch_calculate_similarities(query_text, candidate_texts)

            # Generate query embedding
            query_embedding = self.generate_embedding(query_text)

            # Get cached embeddings from memory cache
            cached_embeddings = self.embedding_cache.get_batch(candidate_ids)

            # Track statistics for logging
            cache_hits = 0
            db_hits = 0
            api_hits = 0

            # Pre-allocate results list with placeholders
            similarities = [None] * len(candidate_texts)

            # Track which nodes need embedding generation
            missing_indices = []
            missing_texts = []
            missing_ids = []

            # Track cached embeddings for parallel processing
            cached_embeddings_list = []
            cached_indices = []

            # First pass: use cached embeddings where available
            for i, (text, node_id) in enumerate(zip(candidate_texts, candidate_ids)):
                # Check memory cache first
                if node_id in cached_embeddings:
                    # Track cached embedding for parallel processing
                    cached_embeddings_list.append(cached_embeddings[node_id])
                    cached_indices.append(i)
                    cache_hits += 1
                    logger.debug(f"Using cached embedding for node {node_id}")
                else:
                    # Mark for later processing
                    missing_indices.append(i)
                    missing_texts.append(text)
                    missing_ids.append(node_id)

            # Process cached embeddings in parallel if any exist
            if cached_embeddings_list:
                cached_similarities = self._parallel_similarity_calculation(
                    query_embedding, cached_embeddings_list
                )
                for idx, sim in enumerate(cached_similarities):
                    similarities[cached_indices[idx]] = sim

            # If we have nodes missing from cache, try to get from database
            db_retrieved_embeddings = {}
            if (
                missing_ids
                and db_connection
                and hasattr(db_connection, "get_embeddings_sync")
            ):
                try:
                    # Use a synchronous method to get embeddings from DB
                    db_retrieved_embeddings = db_connection.get_embeddings_sync(
                        missing_ids
                    )

                    # Process results from database
                    db_embedding_list = []
                    db_indices = []
                    db_node_ids = []

                    for idx, node_id in enumerate(missing_ids):
                        if (
                            node_id in db_retrieved_embeddings
                            and db_retrieved_embeddings[node_id] is not None
                        ):
                            # Get the embedding from DB
                            embedding = np.array(db_retrieved_embeddings[node_id])
                            # Add to cache for future use
                            self.embedding_cache.set(node_id, embedding)
                            # Track for parallel processing
                            db_embedding_list.append(embedding)
                            db_indices.append(missing_indices[idx])
                            db_node_ids.append(node_id)
                            db_hits += 1

                    # Process DB embeddings in parallel
                    if db_embedding_list:
                        db_similarities = self._parallel_similarity_calculation(
                            query_embedding, db_embedding_list
                        )
                        for idx, sim in enumerate(db_similarities):
                            similarities[db_indices[idx]] = sim

                    # Update missing lists based on what we found in DB
                    new_missing_indices = []
                    new_missing_texts = []
                    new_missing_ids = []

                    for idx, node_id in enumerate(missing_ids):
                        if (
                            node_id not in db_node_ids
                            and node_id not in db_retrieved_embeddings
                        ):
                            # Still missing, keep for API generation
                            new_missing_indices.append(missing_indices[idx])
                            new_missing_texts.append(missing_texts[idx])
                            new_missing_ids.append(node_id)

                    # Update our missing lists
                    missing_indices = new_missing_indices
                    missing_texts = new_missing_texts
                    missing_ids = new_missing_ids

                except Exception as e:
                    logger.error(f"Error retrieving embeddings from database: {str(e)}")
                    # Continue with what we have

            # If we still have missing embeddings, calculate them using OpenAI API
            if missing_texts:
                logger.info(
                    f"Generating {len(missing_texts)} missing embeddings via API"
                )
                missing_embeddings = self.generate_embeddings_batch(missing_texts)
                api_hits = len(missing_texts)

                # Calculate similarities for missing embeddings in parallel
                missing_similarities = self._parallel_similarity_calculation(
                    query_embedding, missing_embeddings
                )

                # Fill in missing similarities
                for j, similarity in enumerate(missing_similarities):
                    i = missing_indices[j]
                    node_id = missing_ids[j]
                    embedding = missing_embeddings[j]

                    similarities[i] = similarity

                    # Cache the embedding for future use
                    self.embedding_cache.set(node_id, embedding)

            # Ensure no None values remain in the similarities list
            for i, sim in enumerate(similarities):
                if sim is None:
                    logger.warning(
                        f"Missing similarity value at index {i}, using default 0.0"
                    )
                    similarities[i] = 0.0

            # Log statistics
            logger.info(
                f"Similarity calculation: {cache_hits} from cache, {db_hits} from DB, {api_hits} from API"
            )
            return similarities

        except Exception as e:
            logger.error(f"Error in cached similarity calculation: {str(e)}")
            return self.batch_calculate_similarities(query_text, candidate_texts)

    def _parallel_similarity_calculation(
        self,
        query_embedding: np.ndarray,
        candidate_embeddings: List[np.ndarray],
    ) -> List[float]:
        """Calculate similarities between query embedding and multiple candidate embeddings in parallel."""
        if not candidate_embeddings:
            return []

        try:
            # Define the similarity calculation function
            def calculate_single_similarity(candidate_emb):
                norm1 = np.linalg.norm(query_embedding)
                norm2 = np.linalg.norm(candidate_emb)

                if norm1 == 0 or norm2 == 0:
                    return 0.0

                similarity = np.dot(query_embedding, candidate_emb) / (norm1 * norm2)
                return float(similarity)

            # Use ThreadPoolExecutor for parallel processing
            with concurrent.futures.ThreadPoolExecutor() as executor:
                # Submit all tasks
                futures = [
                    executor.submit(calculate_single_similarity, emb)
                    for emb in candidate_embeddings
                ]

                # Gather results in order
                similarities = [future.result() for future in futures]

            return similarities

        except Exception as e:
            logger.error(f"Error in parallel similarity calculation: {str(e)}")
            # Fall back to sequential calculation if parallel processing fails
            similarities = []
            for embedding in candidate_embeddings:
                # Calculate cosine similarity
                norm1 = np.linalg.norm(query_embedding)
                norm2 = np.linalg.norm(embedding)

                if norm1 == 0 or norm2 == 0:
                    similarities.append(0.0)
                else:
                    similarity = np.dot(query_embedding, embedding) / (norm1 * norm2)
                    similarities.append(float(similarity))

            return similarities
