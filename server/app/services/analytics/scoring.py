import logging
import math
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import Depends

from app.models.domain.knowledge import KnowledgePath, RelationshipType
from app.services.database.repositories.knowledge_repo import (
    KnowledgeRepository,
)
from app.services.nlp.extraction.constants import (
    MAX_EXPERIENCE,
    FEEDBACK_WEIGHT,
    TRUST_WARMUP_PERIOD,
)

logger = logging.getLogger(__name__)


class KnowledgeScoringService:
    """
    Service for scoring and updating trust scores for knowledge paths.
    Now works with feedback_entries instead of success_count.
    """

    def __init__(self, knowledge_repo: KnowledgeRepository = Depends()):
        self.knowledge_repo = knowledge_repo

    def _calculate_feedback_score(
        self, feedback_entries: List[Dict], base_score: float
    ) -> float:
        """
        Calculate a provisional feedback‐only trust score.

        Moves *from* the given base_score toward the positive ratio
        of the feedback entries, weighted by confidence.

        Args:
            feedback_entries: List of feedback entries with type and timestamp.
            base_score:       The prior trust score to start from.

        Returns:
            float: Provisional trust score (0.0–1.0).
        """
        # If there’s no feedback yet, stay at the base_score
        if not feedback_entries:
            return base_score

        positive_count = len(
            [f for f in feedback_entries if f.get("type") == "positive"]
        )
        total_count = len(feedback_entries)
        if total_count == 0:
            return base_score

        positive_ratio = positive_count / total_count
        # ramp up to full confidence at 10 entries
        confidence_factor = min(1.0, total_count / TRUST_WARMUP_PERIOD)

        # Move from base_score toward the positive_ratio
        final_score = base_score + confidence_factor * (
            positive_ratio - base_score
        )
        return max(0.0, min(1.0, final_score))

    async def record_successful_solution(
        self, knowledge_path: KnowledgePath, mechanic_id: str
    ) -> Dict[str, Any]:
        """
        Record a successful solution by adding positive feedback to RESOLVED_BY relationships.

        Args:
            knowledge_path: The knowledge path that was successful.
            mechanic_id: ID of the mechanic who reported success.

        Returns:
            Dict[str, Any]: Summary of updates made.
        """
        try:
            updates = {
                "relationships_updated": 0,
                "feedback_entries_added": 0,
                "trust_scores_updated": [],
            }

            # Find RESOLVED_BY relationships
            resolved_by_relationships = [
                rel
                for rel in knowledge_path.relationships
                if rel.type == RelationshipType.RESOLVED_BY
            ]

            # Add positive feedback to each RESOLVED_BY relationship
            for relationship in resolved_by_relationships:
                # Get current relationship data
                current_rel = (
                    await self.knowledge_repo.get_knowledge_relationship(
                        relationship.source_id,
                        relationship.target_id,
                        "RESOLVED_BY",
                    )
                )

                if not current_rel:
                    continue

                # Get current feedback entries
                feedback_entries = current_rel.get("feedback_entries", [])
                old_trust = current_rel.get("trust_score")

                # Add positive feedback entry
                new_feedback = {
                    "type": "positive",
                    "timestamp": datetime.utcnow().isoformat(),
                    "mechanic_id": mechanic_id,
                }
                feedback_entries.append(new_feedback)

                # Calculate new trust score
                new_trust_score = self.update_trust(
                    old_trust, feedback_entries
                )

                # Update relationship
                await self.knowledge_repo.update_knowledge_relationship(
                    relationship.source_id,
                    relationship.target_id,
                    "RESOLVED_BY",
                    {
                        "feedback_entries": feedback_entries,
                        "trust_score": new_trust_score,
                    },
                )

                updates["relationships_updated"] += 1
                updates["feedback_entries_added"] += 1
                updates["trust_scores_updated"].append(
                    {
                        "source_id": relationship.source_id,
                        "target_id": relationship.target_id,
                        "type": "RESOLVED_BY",
                        "old_score": current_rel.get("trust_score", 0.5),
                        "new_score": new_trust_score,
                        "feedback_count": len(feedback_entries),
                    }
                )

                logger.info(
                    f"Added positive feedback to {relationship.source_id} -> {relationship.target_id}"
                )

            return updates

        except Exception as e:
            logger.error(f"Error recording successful solution: {str(e)}")
            raise

    async def record_failed_solution(
        self, knowledge_path: KnowledgePath, mechanic_id: str
    ) -> Dict[str, Any]:
        """
        Record a failed solution by adding negative feedback to RESOLVED_BY relationships.

        Args:
            knowledge_path: The knowledge path that failed.
            mechanic_id: ID of the mechanic who reported failure.

        Returns:
            Dict[str, Any]: Summary of updates made.
        """
        try:
            updates = {
                "relationships_updated": 0,
                "feedback_entries_added": 0,
                "trust_scores_updated": [],
            }

            # Find RESOLVED_BY relationships
            resolved_by_relationships = [
                rel
                for rel in knowledge_path.relationships
                if rel.type == RelationshipType.RESOLVED_BY
            ]

            # Add negative feedback to each RESOLVED_BY relationship
            for relationship in resolved_by_relationships:
                # Get current relationship data
                current_rel = (
                    await self.knowledge_repo.get_knowledge_relationship(
                        relationship.source_id,
                        relationship.target_id,
                        "RESOLVED_BY",
                    )
                )

                if not current_rel:
                    continue

                # Get current feedback entries
                feedback_entries = current_rel.get("feedback_entries", [])
                old_trust = current_rel.get("trust_score")

                # Add negative feedback entry
                new_feedback = {
                    "type": "negative",
                    "timestamp": datetime.utcnow().isoformat(),
                    "mechanic_id": mechanic_id,
                }
                feedback_entries.append(new_feedback)

                # Calculate new trust score
                new_trust_score = self.update_trust(
                    old_trust, feedback_entries
                )

                # Update relationship
                await self.knowledge_repo.update_knowledge_relationship(
                    relationship.source_id,
                    relationship.target_id,
                    "RESOLVED_BY",
                    {
                        "feedback_entries": feedback_entries,
                        "trust_score": new_trust_score,
                    },
                )

                updates["relationships_updated"] += 1
                updates["feedback_entries_added"] += 1
                updates["trust_scores_updated"].append(
                    {
                        "source_id": relationship.source_id,
                        "target_id": relationship.target_id,
                        "type": "RESOLVED_BY",
                        "old_score": current_rel.get("trust_score", 0.5),
                        "new_score": new_trust_score,
                        "feedback_count": len(feedback_entries),
                    }
                )

                logger.info(
                    f"Added negative feedback to {relationship.source_id} -> {relationship.target_id}"
                )

            return updates

        except Exception as e:
            logger.error(f"Error recording failed solution: {str(e)}")
            raise

    def calculate_init_trust(
        self,
        experience_years: float,
    ) -> float:
        """
        Calculate the initital trust score based on experience.
        """
        normalized_experience = min(
            1.0, max(0.0, experience_years / MAX_EXPERIENCE)
        )

        logger.info(
            f"Calculating trust: experience_years={experience_years}, "
            f"normalized_experience={normalized_experience}, "
        )

        return normalized_experience

    def update_trust(
        self,
        trust_score: float,
        feedback_entries: List[Dict] = None,
    ) -> float:
        """
        Blend the provisional feedback score with the existing trust_score.

        Args:
            trust_score:      The old trust score (0.0–1.0).
            feedback_entries: List of feedback entries just added.

        Returns:
            float: The new blended trust score.
        """
        if trust_score is None or not (0.0 <= trust_score <= 1.0):
            raise ValueError("Trust score must be between 0.0 and 1.0")

        entries = feedback_entries or []

        # Get the provisional feedback‐only score, anchored at the old trust
        provisional = self._calculate_feedback_score(entries, trust_score)

        # Blend toward the provisional using the global weight
        new_score = (
            FEEDBACK_WEIGHT * provisional + (1 - FEEDBACK_WEIGHT) * trust_score
        )

        logger.info(
            f"Old trust score: {trust_score:.3f}, "
            f"provisional feedback score: {provisional:.3f}, "
            f"New trust score: {new_score:.3f}"
        )
        return new_score
