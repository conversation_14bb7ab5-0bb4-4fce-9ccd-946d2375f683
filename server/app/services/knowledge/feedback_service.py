# app/services/knowledge/feedback_service.py
"""
Feedback Service for handling relationship feedback operations.
This service manages the simplified feedback system that stores feedback directly in relationships.
"""
import json
from typing import Any, Dict, List, Optional
from datetime import datetime
from fastapi import Depends
import logging

from app.services.database.repositories.knowledge.relationship_repository import (
    RelationshipRepository,
)
from app.core.exceptions import DatabaseError

logger = logging.getLogger(__name__)


class FeedbackService:
    """Service for handling relationship feedback operations."""

    def __init__(
        self,
        relationship_repo: RelationshipRepository = Depends(),
    ):
        self.relationship_repo = relationship_repo

    async def add_feedback(
        self,
        source_id: str,
        target_id: str,
        relationship_type: str,
        feedback_data: Dict[str, Any],
        user_id: Optional[str] = None,
    ) -> bool:
        """
        Add feedback to a relationship.

        Args:
            source_id: Source node ID
            target_id: Target node ID
            relationship_type: Type of relationship
            feedback_data: Feedback information (e.g., {"rating": 5, "comment": "Helpful"})
            user_id: Optional user ID who provided feedback

        Returns:
            bool: True if feedback was added successfully
        """
        try:
            # Get current relationship
            current_rel = await self.relationship_repo.get_relationship(
                source_id, target_id, relationship_type
            )

            if not current_rel:
                logger.warning(
                    f"Relationship not found: {source_id} -> {target_id} ({relationship_type})"
                )
                return False

            # Get current feedback entries
            current_feedback = current_rel.get("feedback_entries", [])

            # Create new feedback entry
            new_feedback = {
                "timestamp": datetime.utcnow().isoformat(),
                "user_id": user_id,
                "data": feedback_data,
            }

            # Add to feedback list
            current_feedback.append(new_feedback)

            # Update relationship with new feedback
            updates = {
                "feedback_entries": current_feedback,
                "updated_at": datetime.utcnow().isoformat(),
            }

            # Optionally recalculate trust score based on feedback
            if self._should_update_trust_score(feedback_data):
                new_trust_score = self._calculate_trust_score_from_feedback(
                    current_rel.get("trust_score", 0.0), current_feedback
                )
                updates["trust_score"] = new_trust_score

            success = await self.relationship_repo.update_relationship(
                source_id, target_id, relationship_type, updates
            )

            if success:
                logger.info(
                    f"Added feedback to relationship {source_id} -> {target_id}"
                )

            return success

        except Exception as e:
            logger.error(f"Error adding feedback: {str(e)}")
            raise DatabaseError(f"Failed to add feedback: {str(e)}")

    async def get_feedback_history(
        self,
        source_id: str,
        target_id: str,
        relationship_type: str,
        limit: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Get feedback history for a relationship.

        Args:
            source_id: Source node ID
            target_id: Target node ID
            relationship_type: Type of relationship
            limit: Optional limit on number of feedback entries to return

        Returns:
            List of feedback entries
        """
        try:
            relationship = await self.relationship_repo.get_relationship(
                source_id, target_id, relationship_type
            )

            if not relationship:
                return []

            feedback_entries = relationship.get("feedback_entries", [])

            # Sort by timestamp (newest first)
            feedback_entries.sort(
                key=lambda x: x.get("timestamp", ""), reverse=True
            )

            # Apply limit if specified
            if limit is not None:
                feedback_entries = feedback_entries[:limit]

            return feedback_entries

        except Exception as e:
            logger.error(f"Error getting feedback history: {str(e)}")
            raise DatabaseError(f"Failed to get feedback history: {str(e)}")

    async def remove_feedback(
        self,
        source_id: str,
        target_id: str,
        relationship_type: str,
        feedback_timestamp: str,
        user_id: Optional[str] = None,
    ) -> bool:
        """
        Remove a specific feedback entry from a relationship.

        Args:
            source_id: Source node ID
            target_id: Target node ID
            relationship_type: Type of relationship
            feedback_timestamp: Timestamp of feedback to remove
            user_id: Optional user ID (for authorization check)

        Returns:
            bool: True if feedback was removed successfully
        """
        try:
            current_rel = await self.relationship_repo.get_relationship(
                source_id, target_id, relationship_type
            )

            if not current_rel:
                return False

            current_feedback = current_rel.get("feedback_entries", [])

            # Filter out the feedback entry to remove
            updated_feedback = []
            removed = False

            for entry in current_feedback:
                # Match by timestamp and optionally by user_id
                if entry.get("timestamp") == feedback_timestamp:
                    if user_id is None or entry.get("user_id") == user_id:
                        removed = True
                        continue  # Skip this entry (remove it)

                updated_feedback.append(entry)

            if not removed:
                logger.warning(
                    f"Feedback entry not found for removal: {feedback_timestamp}"
                )
                return False

            # Update relationship
            updates = {
                "feedback_entries": updated_feedback,
                "updated_at": datetime.utcnow().isoformat(),
            }

            # Recalculate trust score
            new_trust_score = self._calculate_trust_score_from_feedback(
                current_rel.get("trust_score", 0.0), updated_feedback
            )
            updates["trust_score"] = new_trust_score

            success = await self.relationship_repo.update_relationship(
                source_id, target_id, relationship_type, updates
            )

            if success:
                logger.info(
                    f"Removed feedback from relationship {source_id} -> {target_id}"
                )

            return success

        except Exception as e:
            logger.error(f"Error removing feedback: {str(e)}")
            raise DatabaseError(f"Failed to remove feedback: {str(e)}")

    async def get_feedback_summary(
        self,
        source_id: str,
        target_id: str,
        relationship_type: str,
    ) -> Dict[str, Any]:
        """
        Get a summary of feedback for a relationship.

        Args:
            source_id: Source node ID
            target_id: Target node ID
            relationship_type: Type of relationship

        Returns:
            Dict containing feedback summary statistics
        """
        try:
            feedback_entries = await self.get_feedback_history(
                source_id, target_id, relationship_type
            )

            if not feedback_entries:
                return {
                    "total_feedback": 0,
                    "average_rating": None,
                    "rating_distribution": {},
                    "latest_feedback": None,
                }

            # Calculate statistics
            ratings = []
            rating_distribution = {}

            for entry in feedback_entries:
                data = entry.get("data", {})
                if "rating" in data:
                    rating = data["rating"]
                    ratings.append(rating)
                    rating_distribution[rating] = (
                        rating_distribution.get(rating, 0) + 1
                    )

            average_rating = sum(ratings) / len(ratings) if ratings else None

            return {
                "total_feedback": len(feedback_entries),
                "average_rating": average_rating,
                "rating_distribution": rating_distribution,
                "latest_feedback": (
                    feedback_entries[0] if feedback_entries else None
                ),
                "feedback_count_by_rating": rating_distribution,
            }

        except Exception as e:
            logger.error(f"Error getting feedback summary: {str(e)}")
            raise DatabaseError(f"Failed to get feedback summary: {str(e)}")

    def _should_update_trust_score(
        self, feedback_data: Dict[str, Any]
    ) -> bool:
        """Determine if trust score should be updated based on feedback data."""
        # Update trust score if feedback contains a rating
        return "rating" in feedback_data

    def _calculate_trust_score_from_feedback(
        self,
        current_trust_score: float,
        feedback_entries: List[Dict[str, Any]],
    ) -> float:
        """
        Calculate new trust score based on feedback entries.

        Args:
            current_trust_score: Current trust score
            feedback_entries: List of feedback entries

        Returns:
            float: New calculated trust score
        """
        if not feedback_entries:
            return current_trust_score

        # Extract ratings from feedback
        ratings = []
        for entry in feedback_entries:
            data = entry.get("data", {})
            if "rating" in data:
                ratings.append(data["rating"])

        if not ratings:
            return current_trust_score

        # Calculate average rating (assuming ratings are 1-5)
        average_rating = sum(ratings) / len(ratings)

        # Convert rating to trust score (0.0 to 1.0)
        # Rating of 5 = 0.95 trust, Rating of 1 = 0.1 trust
        feedback_trust_score = 0.1 + (average_rating - 1) * (0.85 / 4)

        # Blend with current trust score (give more weight to feedback if we have more data)
        feedback_weight = min(
            0.7, len(ratings) * 0.1
        )  # Max 70% weight to feedback
        current_weight = 1 - feedback_weight

        new_trust_score = (current_trust_score * current_weight) + (
            feedback_trust_score * feedback_weight
        )

        # Ensure bounds
        return max(0.0, min(0.95, new_trust_score))
