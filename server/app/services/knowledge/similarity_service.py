# app/services/knowledge/similarity_service.py
from typing import List, Optional, <PERSON><PERSON>
from fastapi import Depends
import logging

from app.models.domain.knowledge import KnowledgeNode, KnowledgeType
from app.services.embeddings.embedding import EmbeddingService
from app.services.database.repositories.knowledge.node_repository import (
    NodeRepository,
)
from app.utils.knowledge_utils import (
    prepare_text_for_similarity,
    get_similarity_threshold_for_type,
)

logger = logging.getLogger(__name__)


class SimilarityService:
    """Service for handling similarity calculations and node matching."""

    def __init__(
        self,
        embedding_service: EmbeddingService = Depends(),
        node_repository: NodeRepository = Depends(),
    ):
        self.embedding_service = embedding_service
        self.node_repository = node_repository

    async def find_similar_nodes_by_embedding(
        self,
        query_text: str,
        node_type: Optional[KnowledgeType] = None,
        limit: int = 10,
        threshold: float = 0.6,
    ) -> List[Tuple[KnowledgeNode, float]]:
        """Find similar nodes using stored embeddings with cache support."""
        try:
            # Get nodes with embeddings
            type_str = node_type.value if node_type else None
            nodes_with_embeddings = (
                await self.node_repository.get_nodes_with_embeddings(type_str)
            )

            if not nodes_with_embeddings:
                return []

            # Prepare for batch similarity calculation
            node_texts = []
            node_ids = []
            nodes = []

            for node in nodes_with_embeddings:
                node_text = prepare_text_for_similarity(
                    node.type, node.name, node.description, node.properties
                )
                node_texts.append(node_text)
                node_ids.append(node.id)
                nodes.append(node)

            # Calculate similarities
            similarities = await self.embedding_service.batch_calculate_similarities_with_cache(
                query_text, node_texts, node_ids, self.node_repository.db
            )

            # Combine nodes with similarity scores
            node_similarities = list(zip(nodes, similarities))

            # Filter by threshold and sort
            filtered_nodes = [
                (node, sim)
                for node, sim in node_similarities
                if sim >= threshold
            ]
            filtered_nodes.sort(key=lambda x: x[1], reverse=True)

            return filtered_nodes[:limit]

        except Exception as e:
            logger.error(f"Error finding similar nodes by embedding: {str(e)}")
            return []

    async def find_similar_nodes(
        self,
        node_type: KnowledgeType,
        name: str,
        description: str = "",
        properties: dict = None,
        similarity_threshold: float = 0.8,
    ) -> List[KnowledgeNode]:
        """Find nodes similar to the provided parameters using semantic similarity."""
        try:
            # Adjust threshold based on node type
            adjusted_threshold = get_similarity_threshold_for_type(
                node_type, similarity_threshold
            )

            logger.info(
                f"Finding similar {node_type.value} nodes with threshold {adjusted_threshold}"
            )

            # First try exact match on name (case-insensitive)
            exact_match = await self._find_exact_name_match(node_type, name)
            if exact_match:
                return exact_match

            # Use similarity search
            query_text = prepare_text_for_similarity(
                node_type, name, description, properties
            )

            similar_nodes_with_scores = (
                await self.find_similar_nodes_by_embedding(
                    query_text=query_text,
                    node_type=node_type,
                    limit=5,
                    threshold=adjusted_threshold,
                )
            )

            return [node for node, _ in similar_nodes_with_scores]

        except Exception as e:
            logger.error(f"Error finding similar nodes: {str(e)}")
            return []

    async def _find_exact_name_match(
        self, node_type: KnowledgeType, name: str
    ) -> List[KnowledgeNode]:
        """Find exact name matches for a given node type."""
        query = f"""
        MATCH (n:{node_type.value})
        WHERE toLower(n.name) = toLower($name)
        RETURN n, labels(n) as labels
        """

        try:
            results = await self.node_repository.execute_read_query(
                query, {"name": name}
            )
            if results:
                logger.info(f"Found exact name match for {name}")
                nodes = []
                for result in results:
                    node_data = result["n"]
                    node_data["labels"] = result["labels"]
                    node = (
                        self.node_repository.converter.dict_to_knowledge_node(
                            node_data
                        )
                    )
                    nodes.append(node)
                return nodes
            return []
        except Exception as e:
            logger.error(f"Error finding exact name match: {str(e)}")
            return []
