# services/speech/transcription.py

import base64
import logging
import os
import shutil
import subprocess
from typing import Optional
import tempfile
import json

import openai
from fastapi import Depends

from app.core.config import get_settings
from app.core.exceptions import TranscriptionError

logger = logging.getLogger(__name__)


class TranscriptionService:
    """
    Service for speech-to-text transcription.
    """

    def __init__(self, settings=Depends(get_settings)):
        self.settings = settings
        self.openai_client = None
        logger.info("TranscriptionService initialized")

        # Try to load API key as early as possible
        try:
            # Check if API key is in environment variables directly
            api_key = os.environ.get("OPENAI_API_KEY")
            if api_key:
                logger.info("Found OPENAI_API_KEY in environment variables")
                self.api_key = api_key
            elif hasattr(settings, "OPENAI_API_KEY") and settings.OPENAI_API_KEY:
                logger.info("Found OPENAI_API_KEY in settings")
                self.api_key = settings.OPENAI_API_KEY
            else:
                logger.warning("OPENAI_API_KEY not found in environment or settings")
                self.api_key = None
        except Exception as e:
            logger.error(f"Error initializing API key: {str(e)}")
            self.api_key = None

    def _init_openai(self):
        """Initialize OpenAI client."""
        if not self.openai_client:
            # First try to use the API key initialized in __init__
            if self.api_key:
                logger.info("Using pre-loaded API key")
                api_key = self.api_key
            # Then try to get it from settings
            elif (
                hasattr(self.settings, "OPENAI_API_KEY")
                and self.settings.OPENAI_API_KEY
            ):
                logger.info("Using API key from settings")
                api_key = self.settings.OPENAI_API_KEY
            # Finally try to get it directly from environment
            else:
                logger.info("Trying to get API key from environment")
                api_key = os.environ.get("OPENAI_API_KEY")

            if not api_key:
                logger.error("OpenAI API key not set")
                raise TranscriptionError("OpenAI API key is not set")

            try:
                logger.info(f"Initializing OpenAI client with API key {api_key[:4]}...")
                self.openai_client = openai.OpenAI(api_key=api_key)
            except Exception as e:
                logger.error(f"Error initializing OpenAI client: {str(e)}")
                raise TranscriptionError(
                    f"Failed to initialize OpenAI client: {str(e)}"
                )

    # services/speech/transcription.py
    async def transcribe_audio(self, audio_data: str, file_format: str = "mp3") -> str:
        """
        Transcribe audio data using OpenAI's Transcription API.

        Args:
            audio_data: Base64 encoded audio data.
            file_format: Audio file format (default: "mp3")

        Returns:
            str: The transcribed text.

        Raises:
            TranscriptionError: If transcription fails.
        """
        try:
            # Initialize OpenAI client
            self._init_openai()

            # Validate file format and create temporary file
            file_format = self._validate_and_correct_format(file_format)
            temp_file_path = self._create_temp_audio_file(audio_data, file_format)

            # Execute transcription strategy with multiple fallbacks
            transcription = self._execute_transcription_strategy(
                temp_file_path, file_format
            )
            logger.info(f"Transcription completed successfully: {transcription}...")
            return transcription

        except Exception as e:
            logger.error(f"Transcription error: {str(e)}")
            raise TranscriptionError(f"Failed to transcribe audio: {str(e)}")

    def _execute_transcription_strategy(
        self, temp_file_path: str, file_format: str
    ) -> str:
        """
        Execute the multi-stage transcription strategy with proper cleanup.

        Args:
            temp_file_path: Path to the temporary audio file
            file_format: Original file format

        Returns:
            str: Transcribed text

        Raises:
            TranscriptionError: If all transcription attempts fail
        """
        wav_path = temp_file_path + ".wav"

        try:
            # Strategy 1: Try WAV conversion first (most reliable)
            result = self._try_wav_transcription(temp_file_path, wav_path)
            if result:
                return result

            # Strategy 2: Try original file format
            result = self._try_original_transcription(temp_file_path, file_format)
            if result:
                return result

            # Strategy 3: Try MP3 fallback (last resort)
            return self._try_mp3_fallback(temp_file_path, file_format)

        finally:
            # Clean up temporary files
            self._safe_cleanup_file(temp_file_path)
            self._safe_cleanup_file(wav_path)

    def _try_wav_transcription(self, temp_file_path: str, wav_path: str) -> str:
        """
        Attempt WAV conversion and transcription.

        Returns:
            str: Transcribed text if successful, None if failed
        """
        if not shutil.which("ffmpeg"):
            return None

        try:
            logger.info("Converting audio to WAV for more reliable processing")
            self._convert_to_wav(temp_file_path, wav_path)

            # Try transcription with converted WAV file
            transcribed_text = self._transcribe_file_with_openai(wav_path)
            if transcribed_text:
                logger.info(
                    f"Successfully transcribed audio: {transcribed_text[:50]}..."
                )
                return transcribed_text

        except Exception as e:
            logger.warning(f"Failed to convert to WAV or transcribe WAV: {str(e)}")
            # Return None to continue with original file

        return None

    def _try_original_transcription(self, temp_file_path: str, file_format: str) -> str:
        """
        Attempt transcription with original file format.

        Returns:
            str: Transcribed text if successful, None if failed
        """
        try:
            logger.info(
                f"Sending original audio to OpenAI Transcription API (format: {file_format})"
            )
            transcribed_text = self._transcribe_file_with_openai(temp_file_path)

            if transcribed_text:
                logger.info(
                    f"Successfully transcribed audio: {transcribed_text[:50]}..."
                )
                return transcribed_text

        except Exception as api_error:
            self._log_detailed_transcription_error(api_error)
            # Store the error for potential re-raising later
            self._last_api_error = api_error

        return None

    def _try_mp3_fallback(self, temp_file_path: str, file_format: str) -> str:
        """
        Attempt MP3 conversion and transcription as last resort.

        Returns:
            str: Transcribed text

        Raises:
            TranscriptionError: If all attempts fail
        """
        # Only try MP3 if we haven't tried it yet and ffmpeg is available
        if file_format == "mp3" or not shutil.which("ffmpeg"):
            raise TranscriptionError(
                f"Failed to transcribe audio after all conversion attempts: {str(getattr(self, '_last_api_error', 'Unknown error'))}"
            )

        logger.info("Attempting to convert audio to mp3 format as last resort")
        mp3_path = temp_file_path + ".mp3"

        try:
            self._convert_to_mp3(temp_file_path, mp3_path)

            logger.info("Sending MP3 to Transcription API")
            transcribed_text = self._transcribe_file_with_openai(mp3_path)
            return transcribed_text

        except Exception as mp3_error:
            logger.error(f"Error with MP3 fallback: {str(mp3_error)}")
            raise TranscriptionError(
                f"Failed to transcribe audio after all conversion attempts: {str(getattr(self, '_last_api_error', mp3_error))}"
            )
        finally:
            self._safe_cleanup_file(mp3_path)

    def _validate_and_correct_format(self, file_format: str) -> str:
        """Validate audio format and return corrected format if needed."""
        supported_formats = [
            "flac",
            "m4a",
            "mp3",
            "mp4",
            "mpeg",
            "mpga",
            "oga",
            "ogg",
            "wav",
            "webm",
        ]

        if file_format not in supported_formats:
            logger.warning(f"Unsupported format: {file_format}. Trying mp3 instead.")
            return "mp3"

        return file_format

    def _create_temp_audio_file(self, audio_data: str, file_format: str) -> str:
        """Create temporary file from base64 audio data."""
        # Decode base64 audio data
        decoded_audio = base64.b64decode(audio_data)

        # Create a temporary file to store the audio
        with tempfile.NamedTemporaryFile(
            delete=False, suffix=f".{file_format}"
        ) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(decoded_audio)

        return temp_file_path

    def _convert_to_wav(self, input_path: str, output_path: str):
        """Convert audio file to WAV format using ffmpeg."""
        subprocess.run(
            [
                "ffmpeg",
                "-i",
                input_path,
                "-acodec",
                "pcm_s16le",
                "-ar",
                "16000",
                output_path,
            ],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

    def _convert_to_mp3(self, input_path: str, output_path: str):
        """Convert audio file to MP3 format using ffmpeg."""
        subprocess.run(
            ["ffmpeg", "-i", input_path, "-acodec", "libmp3lame", output_path],
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

    def _transcribe_file_with_openai(self, file_path: str) -> str:
        """
        Transcribe a single audio file using OpenAI API.

        Returns:
            str: Transcribed text

        Raises:
            Exception: If transcription fails (to be caught by caller)
        """
        with open(file_path, "rb") as audio_file:
            response = self.openai_client.audio.transcriptions.create(
                file=audio_file,
                model=self.settings.INPUT_AUDIO_TRANSCRIPTION_MODEL,
                language=self.settings.TRANSCRIPTION_LANGUAGE,
                response_format="text",
            )

            # The response should be the transcribed text directly
            transcribed_text = response

            if not transcribed_text or transcribed_text.strip() == "":
                logger.warning("OpenAI Transcription API returned empty transcription")
                raise TranscriptionError("Empty transcription result")

            return transcribed_text

    def _log_detailed_transcription_error(self, api_error: Exception):
        """Log detailed information about transcription API errors."""
        logger.error(f"Transcription API error: {str(api_error)}")
        error_str = str(api_error)

        # Try to extract and log the detailed error message
        try:
            # Handle different error object structures
            if hasattr(api_error, "json"):
                error_details = api_error.json()
                logger.error(f"API error details: {error_details}")
            elif "Invalid file format" in error_str:
                logger.error("Invalid file format detected")

            # For nested error objects
            if "error" in error_str and "message" in error_str:
                try:
                    # Try to parse the error message as JSON if possible
                    error_obj = json.loads(error_str)
                    if isinstance(error_obj, dict) and "error" in error_obj:
                        logger.error(f"Parsed error message: {error_obj['error']}")
                except:
                    # If parsing fails, just continue
                    pass
        except Exception as parse_err:
            logger.error(f"Error parsing API error: {str(parse_err)}")

    def _safe_cleanup_file(self, file_path: str):
        """Safely remove a file if it exists."""
        if file_path and os.path.exists(file_path):
            try:
                os.unlink(file_path)
            except Exception as cleanup_error:
                logger.warning(
                    f"Failed to cleanup file {file_path}: {str(cleanup_error)}"
                )
