from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import uuid

from neo4j import AsyncGraphDatabase, AsyncDriver, AsyncSession
from neo4j.exceptions import ServiceUnavailable, AuthError

from app.core.config import get_settings
from app.core.exceptions import Neo4jConnectionError, DatabaseError

from app.core.logging import log_database

logger = log_database()


class Neo4jService:
    """
    Service for Neo4j database operations.
    """

    def __init__(self):
        """Initialize the Neo4j database connection."""
        self.settings = get_settings()
        self.driver = None

    async def connect(self) -> None:
        """
        Connect to the Neo4j database.

        Raises:
            Neo4jConnectionError: If connection fails.
        """
        if self.driver is not None:
            return

        try:
            self.driver = AsyncGraphDatabase.driver(
                "bolt://neo4j:7687",  # Use bolt protocol with standard Bolt port
                auth=("neo4j", "password"),
            )
            # Test connection
            await self.driver.verify_connectivity()
            logger.info("Connected to Neo4j database")
        except (ServiceUnavailable, AuthError) as e:
            logger.error(f"Failed to connect to Neo4j: {str(e)}")
            raise Neo4jConnectionError(f"Failed to connect to Neo4j: {str(e)}")

    async def close(self) -> None:
        """Close the Neo4j database connection."""
        if self.driver is not None:
            await self.driver.close()
            self.driver = None
            logger.info("Disconnected from Neo4j database")

    async def get_session(self) -> AsyncSession:
        """
        Get a Neo4j session.

        Returns:
            AsyncSession: The Neo4j session.

        Raises:
            Neo4jConnectionError: If connection fails.
        """
        if self.driver is None:
            await self.connect()

        return self.driver.session()

    async def execute_query(
        self, query: str, params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute a Neo4j Cypher query.
        """
        try:
            async with await self.get_session() as session:
                result = await session.run(query, params or {})
                # Use this for Neo4j Python driver 5.x
                records = []
                async for record in result:
                    records.append(record.data())
                return records
        except Exception as e:
            logger.error(f"Database query error: {str(e)}")
            logger.debug(f"Query: {query}")
            logger.debug(f"Params: {params}")
            raise DatabaseError(f"Error executing query: {str(e)}")

    async def create_node(
        self, label: str, properties: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a node in the Neo4j database with explicit transaction handling.

        Args:
            label: The node label.
            properties: Node properties.

        Returns:
            Dict[str, Any]: The created node.

        Raises:
            DatabaseError: If node creation fails.
        """
        # Generate ID if not provided
        if "id" not in properties:
            properties["id"] = str(uuid.uuid4())

        # Add timestamps
        now = datetime.utcnow().isoformat()
        properties.setdefault("created_at", now)
        properties.setdefault("updated_at", now)

        query = f"""
        CREATE (n:{label} $properties)
        RETURN n
        """

        async with self.driver.session() as session:
            try:
                # Use explicit transaction
                result = await session.run(query, {"properties": properties})
                records = []
                async for record in result:
                    records.append(record.data())

                if not records:
                    raise DatabaseError(f"Failed to create {label} node")

                # Successfully created
                logger.info(f"Created {label} node with ID {properties['id']}")
                return records[0]["n"]
            except Exception as e:
                logger.error(f"Database error creating node: {str(e)}")
                raise DatabaseError(f"Failed to create {label} node: {str(e)}")

    async def get_node_by_id(
        self, label: str, node_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a node by ID.

        Args:
            label: The node label.
            node_id: The node ID.

        Returns:
            Optional[Dict[str, Any]]: The node or None if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        query = f"""
        MATCH (n:{label} {{id: $id}})
        RETURN n
        """

        results = await self.execute_query(query, {"id": node_id})
        if not results:
            return None

        return results[0]["n"]

    async def update_node(
        self, label: str, node_id: str, properties: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Update a node by ID.

        Args:
            label: The node label.
            node_id: The node ID.
            properties: Properties to update.

        Returns:
            Optional[Dict[str, Any]]: The updated node or None if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        # Add updated timestamp
        properties["updated_at"] = datetime.utcnow().isoformat()

        # Build dynamic SET clause for properties
        set_clauses = [f"n.{key} = ${key}" for key in properties.keys()]
        set_clause = ", ".join(set_clauses)

        query = f"""
        MATCH (n:{label} {{id: $id}})
        SET {set_clause}
        RETURN n
        """

        # Prepare parameters
        params = {"id": node_id, **properties}

        results = await self.execute_query(query, params)
        if not results:
            return None

        return results[0]["n"]

    async def delete_node(self, label: str, node_id: str) -> bool:
        """
        Delete a node by ID.

        Args:
            label: The node label.
            node_id: The node ID.

        Returns:
            bool: True if deleted, False if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        query = f"""
        MATCH (n:{label} {{id: $id}})
        DETACH DELETE n
        RETURN count(n) as count
        """

        results = await self.execute_query(query, {"id": node_id})

        return results[0]["count"] > 0

    async def create_relationship(
        self,
        source_label: str,
        source_id: str,
        target_label: str,
        target_id: str,
        relationship_type: str,
        properties: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Create a relationship between two nodes with explicit transaction handling.
        """
        props = properties or {}

        # Add timestamps
        now = datetime.utcnow().isoformat()
        props.setdefault("created_at", now)
        props.setdefault("updated_at", now)

        # Version control properties
        props.setdefault("valid_from", now)
        props.setdefault("is_current", True)

        # First verify both nodes exist with a single query to avoid Cartesian product
        verification_query = f"""
        MATCH (a:{source_label} {{id: $source_id}}), (b:{target_label} {{id: $target_id}})
        RETURN a.id as source_exists, b.id as target_exists
        """

        try:
            async with self.driver.session() as session:
                # Check if both nodes exist
                result = await session.run(
                    verification_query,
                    {"source_id": source_id, "target_id": target_id},
                )

                exists_records = []
                async for record in result:
                    exists_records.append(record.data())

                if not exists_records:
                    raise DatabaseError(
                        f"Source node {source_label} with ID {source_id} or target node {target_label} with ID {target_id} not found"
                    )

                # Create the relationship with a single query
                query = f"""
                MATCH (a:{source_label} {{id: $source_id}})
                MATCH (b:{target_label} {{id: $target_id}})
                CREATE (a)-[r:{relationship_type} $properties]->(b)
                RETURN r, a, b
                """

                result = await session.run(
                    query,
                    {
                        "source_id": source_id,
                        "target_id": target_id,
                        "properties": props,
                    },
                )

                records = []
                async for record in result:
                    records.append(record.data())

                if not records:
                    raise DatabaseError(
                        f"Failed to create relationship: {source_id} -> {target_id}"
                    )

                logger.info(
                    f"Created relationship: {source_label}({source_id}) -[{relationship_type}]-> {target_label}({target_id})"
                )
                return records[0]

        except Exception as e:
            logger.error(f"Error creating relationship: {str(e)}")
            logger.error(
                f"Source: {source_label}({source_id}), Target: {target_label}({target_id}), Type: {relationship_type}"
            )
            raise DatabaseError(f"Error creating relationship: {str(e)}")

    async def get_relationships(
        self,
        source_label: Optional[str] = None,
        source_id: Optional[str] = None,
        target_label: Optional[str] = None,
        target_id: Optional[str] = None,
        relationship_type: Optional[str] = None,
        is_current: bool = True,
    ) -> List[Dict[str, Any]]:
        """
        Get relationships matching the criteria, returning r as a dict of its properties.

        Args:
            source_label: Optional source node label.
            source_id: Optional source node ID.
            target_label: Optional target node label.
            target_id: Optional target node ID.
            relationship_type: Optional relationship type.
            is_current: Whether to return only current relationships.

        Returns:
            List[Dict[str, Any]]: The matching relationships, each record as:
              {
                "a": <node a as dict>,
                "r": <relationship properties as dict>,
                "b": <node b as dict>
              }
        """
        # Build dynamic source clause: always filter by ID if provided, label optional
        if source_id:
            label_part = f":{source_label}" if source_label else ""
            source_clause = f"(a{label_part} {{id: $source_id}})"
        else:
            source_clause = "(a)"

        # Build dynamic target clause: always filter by ID if provided, label optional
        if target_id:
            label_part = f":{target_label}" if target_label else ""
            target_clause = f"(b{label_part} {{id: $target_id}})"
        else:
            target_clause = "(b)"

        # Relationship type filter
        rel_clause = f"[r:{relationship_type}]" if relationship_type else "[r]"

        # Filter to only current relationships if requested
        where_clause = "WHERE r.is_current = true" if is_current else ""

        # Assemble Cypher: project properties(r) as r
        query = f"""
        MATCH {source_clause}-{rel_clause}->{target_clause}
        {where_clause}
        RETURN a, properties(r) AS r, b
        """

        # Prepare parameters
        params: Dict[str, Any] = {}
        if source_id:
            params["source_id"] = source_id
        if target_id:
            params["target_id"] = target_id

        # Execute and return
        return await self.execute_query(query, params)

    async def update_relationship(
        self,
        source_id: str,
        target_id: str,
        relationship_type: str,
        properties: Dict[str, Any],
    ) -> Optional[Dict[str, Any]]:
        """
        Update a relationship by source and target IDs.

        Args:
            source_id: The source node ID.
            target_id: The target node ID.
            relationship_type: The relationship type.
            properties: Properties to update.

        Returns:
            Optional[Dict[str, Any]]: The updated relationship or None if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        # Add updated timestamp
        properties["updated_at"] = datetime.utcnow().isoformat()

        # Build dynamic SET clause for properties
        set_clauses = [f"r.{key} = ${key}" for key in properties.keys()]
        set_clause = ", ".join(set_clauses)

        query = f"""
        MATCH (a {{id: $source_id}})-[r:{relationship_type}]->(b {{id: $target_id}})
        WHERE r.is_current = true
        SET {set_clause}
        RETURN r, a, b
        """

        # Prepare parameters
        params = {"source_id": source_id, "target_id": target_id, **properties}

        results = await self.execute_query(query, params)
        if not results:
            return None

        return results[0]["r"]

    async def execute_cypher(
        self, cypher: str, params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Execute a custom Cypher query.

        Args:
            cypher: The Cypher query.
            params: Parameters for the query.

        Returns:
            List[Dict[str, Any]]: The query results.

        Raises:
            DatabaseError: If query execution fails.
        """
        return await self.execute_query(cypher, params)
