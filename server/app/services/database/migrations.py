"""
Neo4j Vector Search Migration Utilities

This module provides utilities for setting up and managing vector search capabilities in Neo4j.
It handles the creation of vector indices for semantic search and the migration of existing
node embeddings to a compatible vector format.

These utilities are typically used during application initialization or database upgrades
to ensure that the graph database is properly configured for vector-based similarity searches.

Key components:
- Vector index creation for different knowledge node types (SYMPTOM, PROBLEM, CAUSE, SOLUTION)
- Migration of existing embeddings to the proper format for vector search
"""

import logging
from datetime import datetime
import numpy as np

logger = logging.getLogger(__name__)


async def create_vector_indices(session):
    """
    Create vector indices for knowledge nodes in Neo4j to enable vector-based similarity search.
    """
    logger.info("Creating vector indices...")

    # Create separate vector indices for each node type and observation embedding type
    vector_indices = [
        # Keep the original indices for backward compatibility
        """
        CREATE VECTOR INDEX observation_embedding IF NOT EXISTS
        FOR (n:OBSERVATION)
        ON (n.embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        """
        CREATE VECTOR INDEX cause_embedding IF NOT EXISTS
        FOR (n:CAUSE)
        ON (n.embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        """
        CREATE VECTOR INDEX solution_embedding IF NOT EXISTS
        FOR (n:SOLUTION)
        ON (n.embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        # Add new indices for specific observation types
        """
        CREATE VECTOR INDEX observation_visual_embedding IF NOT EXISTS
        FOR (n:OBSERVATION)
        ON (n.visual_embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        """
        CREATE VECTOR INDEX observation_auditory_embedding IF NOT EXISTS
        FOR (n:OBSERVATION)
        ON (n.auditory_embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
        """
        CREATE VECTOR INDEX observation_positional_embedding IF NOT EXISTS
        FOR (n:OBSERVATION)
        ON (n.positional_embedding)
        OPTIONS {
          indexConfig: {
            `vector.dimensions`: 3072,
            `vector.similarity_function`: 'cosine'
          }
        }
        """,
    ]

    # Execute each index creation query, catching and logging any errors
    for index_query in vector_indices:
        try:
            await session.run(index_query)
        except Exception as e:
            logger.error(f"Error creating vector index: {str(e)}")

    logger.info("Vector indices created successfully")


async def migrate_embeddings(knowledge_repo, embedding_service):
    """
    Migrate existing node embeddings to the proper vector format for Neo4j vector search.
    Modified to use batch embedding generation for efficiency.

    This function identifies knowledge nodes that need embedding updates, generates new
    embeddings using the provided embedding service, and updates both the database and
    embedding cache. The migration is necessary when switching to vector search or when
    updating the embedding model.

    Parameters:
    -----------
    knowledge_repo : Repository
        Data access object for the knowledge graph
    embedding_service : EmbeddingService
        Service for generating text embeddings

    Returns:
    --------
    int
        The number of nodes that were migrated
    """
    logger.info("Migrating embeddings to multi-embedding format...")

    # Query to get all knowledge nodes (limited to 1000 to avoid memory issues)
    query = """
    MATCH (n)
    WHERE (n:OBSERVATION OR n:CAUSE OR n:SOLUTION)
    RETURN n.id as id, n.name as name, n.description as description,
           n.visual_observation as visual, n.auditory_observation as auditory,
           n.positional_observation as positional, labels(n) as labels
    LIMIT 1000
    """

    results = await knowledge_repo.execute_read_query(query, {})

    # Group nodes by type
    observation_nodes = []
    other_nodes = []

    for result in results:
        labels = result["labels"]
        if "OBSERVATION" in labels:
            observation_nodes.append(result)
        else:
            other_nodes.append(result)

    migrated = 0

    # Process OBSERVATION nodes with batch embedding
    if observation_nodes:
        # Collect texts for different observation types
        visual_texts = []
        auditory_texts = []
        positional_texts = []
        node_ids = []

        for node in observation_nodes:
            node_id = node["id"]
            name = node["name"] or ""

            # Get observation texts
            visual = node.get("visual") or name
            auditory = node.get("auditory") or name
            positional = node.get("positional") or name

            # Add to batch lists
            visual_texts.append(visual)
            auditory_texts.append(auditory)
            positional_texts.append(positional)
            node_ids.append(node_id)

        # Generate embeddings in batch
        visual_embeddings = embedding_service.generate_embeddings_batch(visual_texts)
        auditory_embeddings = embedding_service.generate_embeddings_batch(
            auditory_texts
        )
        positional_embeddings = embedding_service.generate_embeddings_batch(
            positional_texts
        )

        # Update database and cache for each node
        for i, node_id in enumerate(node_ids):
            if i >= len(visual_embeddings):
                continue

            # Update node with batch-generated embeddings
            update_query = """
            MATCH (n:OBSERVATION {id: $id})
            SET n.visual_embedding = $visual_embedding,
                n.auditory_embedding = $auditory_embedding,
                n.positional_embedding = $positional_embedding,
                n.embedding_updated_at = $updated_at
            """

            await knowledge_repo.execute_write_query(
                update_query,
                {
                    "id": node_id,
                    "visual_embedding": visual_embeddings[i].tolist(),
                    "auditory_embedding": auditory_embeddings[i].tolist(),
                    "positional_embedding": positional_embeddings[i].tolist(),
                    "updated_at": datetime.utcnow().isoformat(),
                },
            )

            # Update cache
            embedding_service.embedding_cache.set(
                node_id, visual_embeddings[i], "visual"
            )
            embedding_service.embedding_cache.set(
                node_id, auditory_embeddings[i], "auditory"
            )
            embedding_service.embedding_cache.set(
                node_id, positional_embeddings[i], "positional"
            )

            migrated += 1

    # Process non-OBSERVATION nodes with batch embedding
    if other_nodes:
        # Collect texts
        texts = []
        node_ids = []

        for node in other_nodes:
            node_id = node["id"]
            name = node["name"] or ""
            description = node["description"] or ""

            # Create combined text
            text = f"{name} {description}"
            texts.append(text)
            node_ids.append(node_id)

        # Generate embeddings in batch
        embeddings = embedding_service.generate_embeddings_batch(texts)

        # Update database and cache for each node
        for i, node_id in enumerate(node_ids):
            if i >= len(embeddings):
                continue

            # Update node with batch-generated embedding
            update_query = """
            MATCH (n {id: $id})
            SET n.embedding = $embedding,
                n.embedding_updated_at = $updated_at
            """

            await knowledge_repo.execute_write_query(
                update_query,
                {
                    "id": node_id,
                    "embedding": embeddings[i].tolist(),
                    "updated_at": datetime.utcnow().isoformat(),
                },
            )

            # Update cache
            embedding_service.embedding_cache.set(node_id, embeddings[i])

            migrated += 1

        # Log progress periodically
        if migrated % 20 == 0:
            logger.info(f"Migrated {migrated} node embeddings")

    logger.info(f"Completed migration of {migrated} node embeddings")
    return migrated
