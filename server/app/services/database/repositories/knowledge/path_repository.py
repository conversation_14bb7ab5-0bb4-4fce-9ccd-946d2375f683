# app/services/database/repositories/knowledge/path_repository.py
from typing import Any, Dict, List, Optional
from fastapi import Depends
import logging

from app.models.domain.knowledge import KnowledgePath, RelationshipType
from app.services.database.neo4j import Neo4jService
from app.services.database.repositories.base import BaseRepository
from app.core.exceptions import DatabaseError
from app.utils.knowledge_utils import (
    KnowledgeNodeConverter,
    KnowledgeRelationshipConverter,
)

logger = logging.getLogger(__name__)


class PathRepository(BaseRepository):
    """Repository for knowledge path operations."""

    def __init__(self, db: Neo4jService = Depends()):
        super().__init__(db)
        self.node_converter = KnowledgeNodeConverter()
        self.rel_converter = KnowledgeRelationshipConverter()

    async def get_knowledge_paths(
        self,
        observation_name: Optional[str] = None,
        door_type_id: Optional[str] = None,
        limit: int = 5,
    ) -> List[KnowledgePath]:
        """
        Get knowledge paths from observation to solution.
        Updated for OCS structure (Observation -> Cause -> Solution).
        """
        try:
            where_clauses = []
            params = {"limit": limit}

            if observation_name:
                where_clauses.append(
                    "(toLower(o.name) CONTAINS toLower($observation_name) OR "
                    "toLower(o.description) CONTAINS toLower($observation_name))"
                )
                params["observation_name"] = observation_name

            where_clause = (
                f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
            )

            # Build query for OCS structure
            query = f"""
            MATCH (o:OBSERVATION)-[r1:OBSERVED_WITH]->(c:CAUSE)-[r2:RESOLVED_BY]->(sol:SOLUTION)
            {where_clause}
            WITH o, c, sol, r1, r2,
                r1.trust_score as trust1,
                r2.trust_score as trust2,
                labels(o) as o_labels,
                labels(c) as c_labels,
                labels(sol) as sol_labels
            """

            # Add door type filter if specified
            if door_type_id:
                query += """
                MATCH (sol)-[r3:APPLIES_TO]->(dt:DOOR_TYPE {id: $door_type_id})
                WITH o, c, sol, dt, r1, r2, r3, trust1, trust2, r3.trust_score as trust3,
                    o_labels, c_labels, sol_labels, labels(dt) as dt_labels
                WITH o, c, sol, dt, r1, r2, r3, (trust1 + trust2 + trust3)/3 as path_trust,
                    o_labels, c_labels, sol_labels, dt_labels
                ORDER BY path_trust DESC
                LIMIT $limit
                RETURN o, c, sol, dt, r1, r2, r3, path_trust,
                       o_labels, c_labels, sol_labels, dt_labels
                """
                params["door_type_id"] = door_type_id
            else:
                query += """
                WITH o, c, sol, r1, r2, (trust1 + trust2)/2 as path_trust,
                    o_labels, c_labels, sol_labels
                ORDER BY path_trust DESC
                LIMIT $limit
                RETURN o, c, sol, r1, r2, path_trust,
                       o_labels, c_labels, sol_labels
                """

            results = await self.execute_read_query(query, params)
            return self._convert_results_to_knowledge_paths(
                results, door_type_id
            )

        except Exception as e:
            logger.error(f"Error getting knowledge paths: {str(e)}")
            raise DatabaseError(f"Failed to get knowledge paths: {str(e)}")

    def _convert_results_to_knowledge_paths(
        self, results: List[Dict[str, Any]], door_type_id: Optional[str]
    ) -> List[KnowledgePath]:
        """Convert query results to KnowledgePath objects."""
        knowledge_paths = []

        for result in results:
            # Extract and prepare nodes
            o_node = self._prepare_node_with_labels(
                result["o"], result["o_labels"]
            )
            c_node = self._prepare_node_with_labels(
                result["c"], result["c_labels"]
            )
            sol_node = self._prepare_node_with_labels(
                result["sol"], result["sol_labels"]
            )

            # Convert to domain models
            observation_node = self.node_converter.dict_to_knowledge_node(
                o_node
            )
            cause_node = self.node_converter.dict_to_knowledge_node(c_node)
            solution_node = self.node_converter.dict_to_knowledge_node(
                sol_node
            )

            trust = result["path_trust"]

            # Create relationships
            observation_to_cause = (
                self.rel_converter.dict_to_knowledge_relationship(
                    result["r1"],
                    observation_node.id,
                    cause_node.id,
                    RelationshipType.OBSERVED_WITH,
                )
            )

            cause_to_solution = (
                self.rel_converter.dict_to_knowledge_relationship(
                    result["r2"],
                    cause_node.id,
                    solution_node.id,
                    RelationshipType.RESOLVED_BY,
                )
            )

            # Prepare nodes and relationships
            nodes = [observation_node, cause_node, solution_node]
            relationships = [observation_to_cause, cause_to_solution]

            # Add door type if present
            if door_type_id and "dt" in result and result["dt"]:
                door_node_data = self._prepare_node_with_labels(
                    result["dt"], result.get("dt_labels", ["DOOR_TYPE"])
                )
                door_node = self.node_converter.dict_to_knowledge_node(
                    door_node_data
                )

                solution_to_door = (
                    self.rel_converter.dict_to_knowledge_relationship(
                        result["r3"],
                        solution_node.id,
                        door_node.id,
                        RelationshipType.APPLIES_TO,
                    )
                )

                nodes.append(door_node)
                relationships.append(solution_to_door)

            # Create KnowledgePath
            knowledge_path = KnowledgePath(nodes, relationships, trust)

            # Add resolved_by_relationships for frontend feedback
            resolved_by_rels = [
                {"source_id": rel.source_id, "target_id": rel.target_id}
                for rel in relationships
                if rel.type == RelationshipType.RESOLVED_BY
            ]

            if hasattr(knowledge_path, "__dict__"):
                knowledge_path.resolved_by_relationships = resolved_by_rels

            knowledge_paths.append(knowledge_path)

        return knowledge_paths

    def _prepare_node_with_labels(
        self, node_data: Dict[str, Any], labels: List[str]
    ) -> Dict[str, Any]:
        """Prepare node data with labels attached."""
        node_data["labels"] = labels
        return node_data
