# app/services/database/repositories/knowledge/relationship_repository.py
import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from fastapi import Depends
import logging

from app.models.domain.knowledge import KnowledgeRelationship, RelationshipType
from app.services.database.neo4j import Neo4jService
from app.services.database.repositories.base import BaseRepository
from app.core.exceptions import DatabaseError, EntityNotFoundError
from app.utils.knowledge_utils import KnowledgeRelationshipConverter

logger = logging.getLogger(__name__)


class RelationshipRepository(BaseRepository):
    """Repository for knowledge relationship operations."""

    def __init__(self, db: Neo4jService = Depends()):
        super().__init__(db)
        self.converter = KnowledgeRelationshipConverter()

    async def create_relationship(
        self, relationship: KnowledgeRelationship
    ) -> KnowledgeRelationship:
        """Create a knowledge relationship or update if exists."""
        try:
            # Get source and target node types
            source_label, target_label = await self._get_node_labels(
                relationship.source_id, relationship.target_id
            )

            # Check for existing relationship
            existing_rel = await self.get_relationship(
                relationship.source_id,
                relationship.target_id,
                relationship.type.value,
            )

            if existing_rel:
                return await self._update_existing_relationship(
                    existing_rel, relationship
                )
            else:
                return await self._create_new_relationship(
                    relationship, source_label, target_label
                )

        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error creating knowledge relationship: {str(e)}")
            raise DatabaseError(
                f"Failed to create knowledge relationship: {str(e)}"
            )

    async def get_relationship(
        self, source_id: str, target_id: str, relationship_type: str
    ) -> Optional[Dict[str, Any]]:
        """Get a specific relationship by source_id, target_id, and type."""
        try:
            results = await self.db.get_relationships(
                source_id=source_id,
                target_id=target_id,
                relationship_type=relationship_type,
                is_current=True,
            )

            if not results:
                return None

            result = results[0]
            raw_rel = result["r"]

            # Extract relationship properties
            if hasattr(raw_rel, "properties"):
                rel_data = dict(raw_rel.properties)
            elif isinstance(raw_rel, tuple) and hasattr(
                raw_rel[1], "properties"
            ):
                rel_data = dict(raw_rel[1].properties)
            else:
                rel_data = raw_rel if isinstance(raw_rel, dict) else {}

            # Parse feedback entries and properties
            feedback_entries = self._parse_json_field(
                rel_data.get("feedback_entries", "[]"), []
            )
            properties = self._parse_json_field(
                rel_data.get("properties_json", "{}"), {}
            )

            return {
                "source_id": source_id,
                "target_id": target_id,
                "type": relationship_type,
                "trust_score": float(rel_data.get("trust_score", 0.0) or 0.0),
                "feedback_entries": feedback_entries,
                "properties": properties,
                "created_at": rel_data.get("created_at"),
                "updated_at": rel_data.get("updated_at"),
                "created_by": rel_data.get("created_by"),
                "valid_from": rel_data.get("valid_from"),
                "valid_to": rel_data.get("valid_to"),
                "is_current": bool(rel_data.get("is_current", True)),
            }

        except Exception as e:
            logger.error(f"Error getting knowledge relationship: {str(e)}")
            raise DatabaseError(
                f"Failed to get knowledge relationship: {str(e)}"
            )

    async def update_relationship(
        self,
        source_id: str,
        target_id: str,
        relationship_type: Union[str, RelationshipType],
        updates: Dict[str, Any],
    ) -> bool:
        """Update a relationship with new data."""
        try:
            rel_type_str = (
                relationship_type.value
                if isinstance(relationship_type, RelationshipType)
                else relationship_type
            )

            update_props = {}

            if "feedback_entries" in updates:
                update_props["feedback_entries"] = json.dumps(
                    updates["feedback_entries"], default=str
                )

            if "trust_score" in updates:
                update_props["trust_score"] = float(updates["trust_score"])

            if "properties" in updates:
                update_props["properties_json"] = json.dumps(
                    updates["properties"], default=str
                )

            if not update_props:
                return True

            result = await self.db.update_relationship(
                source_id, target_id, rel_type_str, update_props
            )
            return result is not None

        except Exception as e:
            logger.error(f"Error updating knowledge relationship: {e}")
            raise DatabaseError(
                f"Failed to update knowledge relationship: {e}"
            )

    async def get_relationships(
        self,
        source_id: Optional[str] = None,
        target_id: Optional[str] = None,
        relationship_type: Optional[RelationshipType] = None,
        is_current: bool = True,
    ) -> List[KnowledgeRelationship]:
        """Get knowledge relationships matching criteria."""
        try:
            rel_type = relationship_type.value if relationship_type else None

            results = await self.db.get_relationships(
                source_id=source_id,
                target_id=target_id,
                relationship_type=rel_type,
                is_current=is_current,
            )

            relationships = []
            for result in results:
                source_id = result["a"]["id"]
                target_id = result["b"]["id"]
                rel_data = result["r"]
                rel_type_str = rel_data.type
                rel_type = RelationshipType(rel_type_str)

                relationship = self.converter.dict_to_knowledge_relationship(
                    rel_data, source_id, target_id, rel_type
                )
                relationships.append(relationship)

            return relationships
        except Exception as e:
            logger.error(f"Error getting knowledge relationships: {str(e)}")
            raise DatabaseError(
                f"Failed to get knowledge relationships: {str(e)}"
            )

    async def version_relationship(
        self,
        source_id: str,
        target_id: str,
        relationship_type: RelationshipType,
    ) -> None:
        """Version a relationship by setting existing ones to not current."""
        try:
            query = f"""
            MATCH (a {{id: $source_id}})-[r:{relationship_type.value}]->(b {{id: $target_id}})
            WHERE r.is_current = true
            SET r.is_current = false,
                r.valid_to = $valid_to,
                r.updated_at = $updated_at
            """

            now = datetime.utcnow().isoformat()
            params = {
                "source_id": source_id,
                "target_id": target_id,
                "valid_to": now,
                "updated_at": now,
            }

            await self.execute_write_query(query, params)
        except Exception as e:
            logger.error(f"Error versioning relationship: {str(e)}")
            raise DatabaseError(f"Failed to version relationship: {str(e)}")

    async def _get_node_labels(
        self, source_id: str, target_id: str
    ) -> tuple[str, str]:
        """Get the labels for source and target nodes."""
        from app.services.database.repositories.knowledge.node_repository import (
            NodeRepository,
        )

        # This would typically be injected, but for simplicity we'll create it here
        node_repo = NodeRepository(self.db)

        source_node = await node_repo.get_node(source_id)
        if not source_node:
            raise EntityNotFoundError("Knowledge node", source_id)
        source_label = source_node.type.value

        # Check for door types specifically
        door_query = """
        MATCH (d:DOOR_TYPE {id: $id})
        RETURN d
        """
        door_results = await self.execute_read_query(
            door_query, {"id": target_id}
        )

        if door_results:
            target_label = "DOOR_TYPE"
        else:
            target_node = await node_repo.get_node(target_id)
            if not target_node:
                raise EntityNotFoundError("Knowledge node", target_id)
            target_label = target_node.type.value

        return source_label, target_label

    async def _update_existing_relationship(
        self,
        existing_rel: Dict[str, Any],
        new_relationship: KnowledgeRelationship,
    ) -> KnowledgeRelationship:
        """Update an existing relationship with new trust score."""
        current_score = existing_rel.get("trust_score", 0.0)

        # Calculate new trust score using asymptotic approach
        learning_rate = await self._get_mechanic_experience_level(
            new_relationship.created_by
        )
        new_score = current_score + (0.95 - current_score) * learning_rate

        update_data = {
            "trust_score": new_score,
            "updated_at": datetime.utcnow().isoformat(),
        }

        if new_relationship.properties:
            update_data["properties_json"] = json.dumps(
                new_relationship.properties
            )

        update_data["feedback_entries"] = json.dumps([])

        logger.info(f"Trust score updated from {current_score} to {new_score}")

        updated_rel = await self.db.update_relationship(
            new_relationship.source_id,
            new_relationship.target_id,
            new_relationship.type.value,
            update_data,
        )

        return self.converter.dict_to_knowledge_relationship(
            updated_rel,
            new_relationship.source_id,
            new_relationship.target_id,
            new_relationship.type,
        )

    async def _create_new_relationship(
        self,
        relationship: KnowledgeRelationship,
        source_label: str,
        target_label: str,
    ) -> KnowledgeRelationship:
        """Create a new relationship."""
        try:
            if relationship.is_current:
                await self.version_relationship(
                    relationship.source_id,
                    relationship.target_id,
                    relationship.type,
                )

            relationship_props = {
                "trust_score": relationship.trust_score,
                "success_count": relationship.success_count,
                "feedback_entries": json.dumps([]),
                "created_at": relationship.created_at.isoformat(),
                "updated_at": relationship.updated_at.isoformat(),
                "created_by": relationship.created_by,
                "valid_from": relationship.valid_from.isoformat(),
                "is_current": relationship.is_current,
            }

            if relationship.valid_to:
                relationship_props["valid_to"] = (
                    relationship.valid_to.isoformat()
                )

            if relationship.properties:
                relationship_props["properties_json"] = json.dumps(
                    relationship.properties
                )

            logger.info(
                f"Creating relationship: {source_label}({relationship.source_id}) "
                f"-[{relationship.type.value}]-> {target_label}({relationship.target_id})"
            )

            rel_data = await self.db.create_relationship(
                source_label,
                relationship.source_id,
                target_label,
                relationship.target_id,
                relationship.type.value,
                relationship_props,
            )

            return self.converter.dict_to_knowledge_relationship(
                rel_data,
                relationship.source_id,
                relationship.target_id,
                relationship.type,
            )
        except Exception as e:
            logger.error(f"Error creating relationship in database: {str(e)}")
            raise DatabaseError(
                f"Failed to create relationship: {relationship.source_id} -> {relationship.target_id}"
            )

    async def _get_mechanic_experience_level(self, mechanic_id: str) -> float:
        """Get a modifier based on mechanic's experience level."""
        try:
            query = """
            MATCH (m:Mechanic {id: $id})
            RETURN m.role as role, m.experience_years as experience_years, m.experience as experience
            """

            result = await self.execute_read_query(query, {"id": mechanic_id})

            if not result:
                return 0.15

            mechanic_data = result[0]
            role = mechanic_data.get("role", "")
            experience_years = mechanic_data.get("experience_years", 0)
            experience = mechanic_data.get("experience", 0.5)

            # Calculate learning rate based on role and experience
            base_rates = {
                "ADMIN": 0.25,
                "SENIOR": 0.2,
                "JUNIOR": 0.1,
            }
            base_rate = base_rates.get(role, 0.15)

            experience_bonus = min(0.05, experience_years * 0.005)
            experience_bonus += (experience - 0.5) * 0.1

            return min(0.25, max(0.1, base_rate + experience_bonus))
        except Exception as e:
            logger.error(f"Error getting mechanic experience: {str(e)}")
            return 0.15

    def _parse_json_field(self, json_str: str, default: Any) -> Any:
        """Parse a JSON field with fallback to default."""
        try:
            return json.loads(json_str) if json_str else default
        except (json.JSONDecodeError, TypeError):
            return default
