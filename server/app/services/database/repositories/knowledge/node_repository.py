# app/services/database/repositories/knowledge/node_repository.py
import json
import uuid
import numpy as np
from typing import Any, Dict, List, Optional
from datetime import datetime
from fastapi import Depends
import logging

from app.models.domain.knowledge import KnowledgeNode, KnowledgeType
from app.services.database.neo4j import Neo4jService
from app.services.embeddings.embedding import EmbeddingService
from app.services.cache.embedding_cache import get_embedding_cache
from app.services.database.repositories.base import BaseRepository
from app.core.exceptions import DatabaseError
from app.utils.knowledge_utils import KnowledgeNodeConverter

logger = logging.getLogger(__name__)


class NodeRepository(BaseRepository):
    """Repository for knowledge node operations."""

    def __init__(
        self,
        db: Neo4jService = Depends(),
        embedding_service: EmbeddingService = Depends(),
        embedding_cache=Depends(get_embedding_cache),
    ):
        super().__init__(db, embedding_service, embedding_cache)
        self.converter = KnowledgeNodeConverter()
        self.embedding_cache = embedding_cache

    async def create_node(
        self, node: KnowledgeNode, check_duplicates: bool = True
    ) -> KnowledgeNode:
        """Create a knowledge node with improved NumPy array handling."""
        try:
            # TODO: Implement duplication checking in future iteration
            if check_duplicates and node.type != KnowledgeType.SOLUTION:
                # similar_nodes = await self.find_similar_nodes(...)
                pass

            # Generate embedding
            node_text = f"{node.name} {node.description}"
            embedding = self.embedding_service.generate_embedding(node_text)

            # Create clean properties
            clean_props = self._prepare_node_properties(node, embedding)

            # Create the node in database
            logger.info(f"Creating {node.type.value} node in database")
            label = node.type.value
            node_data = await self.db.create_node(label, clean_props)

            # Convert to domain model
            created_node = self.converter.dict_to_knowledge_node(node_data)

            # Cache the embedding
            self.embedding_cache.set(created_node.id, embedding)

            # Log the created node ID
            logger.info(f"Created node with ID: {created_node.id}")
            
            return created_node
        except Exception as e:
            logger.error(f"Error creating knowledge node: {str(e)}")
            raise DatabaseError(f"Failed to create knowledge node: {str(e)}")

    async def get_node(self, node_id: str) -> Optional[KnowledgeNode]:
        """Get a knowledge node by ID."""
        query = """
        MATCH (n)
        WHERE n.id = $id AND (n:OBSERVATION OR n:CAUSE OR n:SOLUTION OR
            n:GENERAL OR n:TECHNIQUE OR n:SHORTCUT OR n:DOOR_TYPE OR n:DOOR_PART OR n:ENVIRONMENT)
        RETURN n, labels(n) as labels
        """

        try:
            results = await self.execute_read_query(query, {"id": node_id})
            if not results:
                return None

            node_data = results[0]["n"]
            node_data["labels"] = results[0]["labels"]

            node = self.converter.dict_to_knowledge_node(node_data)

            # Cache embedding if it exists
            if "embedding" in node_data:
                embedding = np.array(node_data["embedding"])
                self.embedding_cache.set(node_id, embedding)

            return node

        except Exception as e:
            logger.error(f"Error getting knowledge node: {str(e)}")
            return None

    async def update_node(
        self, node_id: str, properties: Dict[str, Any]
    ) -> Optional[KnowledgeNode]:
        """Update a knowledge node."""
        node = await self.get_node(node_id)
        if not node:
            return None

        label = node.type.value

        # Update embedding if name or description changed
        if "name" in properties or "description" in properties:
            current_name = node.name
            current_description = node.description
            updated_name = properties.get("name", current_name)
            updated_description = properties.get("description", current_description)

            node_text = f"{updated_name} {updated_description}"
            embedding = self.embedding_service.generate_embedding(node_text)

            properties["embedding"] = embedding.tolist()
            self.embedding_cache.set(node_id, embedding)

        try:
            updated_node = await self.db.update_node(label, node_id, properties)
            if not updated_node:
                return None

            return self.converter.dict_to_knowledge_node(updated_node)
        except Exception as e:
            logger.error(f"Error updating knowledge node: {str(e)}")
            raise DatabaseError(f"Failed to update knowledge node: {str(e)}")

    async def delete_node(self, node_id: str) -> bool:
        """Delete a knowledge node."""
        node = await self.get_node(node_id)
        if not node:
            return False

        label = node.type.value

        try:
            result = await self.db.delete_node(label, node_id)

            if result:
                # Remove from cache
                try:
                    self.embedding_cache.node_embeddings.pop(node_id, None)
                except:
                    pass

            return result

        except Exception as e:
            logger.error(f"Error deleting knowledge node: {str(e)}")
            raise DatabaseError(f"Failed to delete knowledge node: {str(e)}")

    async def update_node_embedding(self, node_id: str, embedding: np.ndarray) -> bool:
        """Update the embedding for a knowledge node."""
        try:
            embedding_list = embedding.tolist()

            query = """
            MATCH (n {id: $id})
            SET n.embedding = $embedding,
                n.embedding_updated_at = $updated_at
            RETURN n
            """

            result = await self.execute_write_query(
                query,
                {
                    "id": node_id,
                    "embedding": embedding_list,
                    "updated_at": datetime.utcnow().isoformat(),
                },
            )

            self.embedding_cache.set(node_id, embedding)
            return len(result) > 0

        except Exception as e:
            logger.error(f"Error updating node embedding: {str(e)}")
            return False

    async def get_nodes_with_embeddings(
        self, node_type: Optional[str] = None, limit: int = 1000
    ) -> List[KnowledgeNode]:
        """Get nodes with their embeddings for efficient similarity search."""
        try:
            if node_type:
                query = f"""
                MATCH (n:{node_type})
                RETURN n, n.embedding as node_embedding, labels(n) as labels
                LIMIT $limit
                """
            else:
                query = """
                MATCH (n)
                WHERE (n:OBSERVATION OR n:CAUSE OR n:SOLUTION)
                RETURN n, n.embedding as node_embedding, labels(n) as labels
                LIMIT $limit
                """

            results = await self.execute_read_query(query, {"limit": limit})

            nodes = []
            cache_hits = db_hits = new_embeddings = 0

            for result in results:
                node_data = result["n"]
                node_data["labels"] = result["labels"]
                node = self.converter.dict_to_knowledge_node(node_data)

                # Handle embedding retrieval with caching
                cached_embedding = self.embedding_cache.get(node.id)
                if cached_embedding is not None:
                    node.embedding = cached_embedding
                    cache_hits += 1
                elif result["node_embedding"] is not None:
                    embedding = np.array(result["node_embedding"])
                    node.embedding = embedding
                    self.embedding_cache.set(node.id, embedding)
                    db_hits += 1
                elif hasattr(node, "embedding") and node.embedding is not None:
                    embedding = np.array(node.embedding)
                    self.embedding_cache.set(node.id, embedding)
                    db_hits += 1
                else:
                    # Generate new embedding
                    try:
                        from app.utils.knowledge_utils import (
                            prepare_text_for_similarity,
                        )

                        node_text = prepare_text_for_similarity(
                            node.type,
                            node.name,
                            node.description,
                            node.properties,
                        )
                        embedding = self.embedding_service.generate_embedding(node_text)
                        node.embedding = embedding

                        self.embedding_cache.set(node.id, embedding)

                        # Update in database
                        await self.update_node_embedding(node.id, embedding)
                        new_embeddings += 1
                    except Exception as e:
                        logger.error(
                            f"Error generating embedding for node {node.id}: {str(e)}"
                        )

                nodes.append(node)

            logger.info(
                f"Embedding retrieval stats: {cache_hits} from cache, {db_hits} from DB, {new_embeddings} generated"
            )
            return nodes

        except Exception as e:
            logger.error(f"Error getting nodes with embeddings: {str(e)}")
            return []

    def _prepare_node_properties(
        self, node: KnowledgeNode, embedding: np.ndarray
    ) -> Dict[str, Any]:
        """Prepare clean properties for node creation."""
        clean_props = {
            "id": node.id,
            "name": node.name,
            "description": node.description,
            "created_at": (
                node.created_at.isoformat()
                if hasattr(node, "created_at")
                else datetime.utcnow().isoformat()
            ),
            "updated_at": (
                node.updated_at.isoformat()
                if hasattr(node, "updated_at")
                else datetime.utcnow().isoformat()
            ),
            "created_by": (node.created_by if hasattr(node, "created_by") else None),
        }

        # Add embedding
        if hasattr(embedding, "tolist"):
            clean_props["embedding"] = embedding.tolist()
        else:
            clean_props["embedding"] = list(embedding)

        # Add properties from node.properties
        if node.properties:
            logger.debug(f"Flattening node properties: {node.properties}")
            for key, value in node.properties.items():
                if key.startswith("_"):
                    continue

                clean_props[key] = self._serialize_property_value(key, value)

        return clean_props

    def _serialize_property_value(self, key: str, value: Any) -> Any:
        """Serialize a property value for Neo4j storage."""
        if value is None:
            return None
        elif isinstance(value, (str, int, float, bool)):
            return value
        elif isinstance(value, list):
            try:
                return [
                    (
                        str(item)
                        if not isinstance(item, (str, int, float, bool))
                        else item
                    )
                    for item in value
                ]
            except:
                return [str(item) for item in value]
        elif hasattr(value, "tolist"):
            return value.tolist()
        else:
            try:
                return json.dumps(value)
            except:
                return str(value)
