# app/services/database/repositories/knowledge_repo.py
"""
Refactored Knowledge Repository - Main orchestrator for knowledge operations.
This class serves as the main interface while delegating to specialized repositories and services.
"""
import json
import uuid
from typing import List, Dict, Any, Optional, Tuple, Union
from fastapi import Depends
import logging
from datetime import datetime

from app.models.domain.knowledge import (
    KnowledgeNode,
    KnowledgeRelationship,
    KnowledgePath,
    KnowledgeType,
    RelationshipType,
)
from app.services.database.neo4j import Neo4jService
from app.services.embeddings.embedding import EmbeddingService
from app.services.cache.embedding_cache import get_embedding_cache

# Import specialized repositories and services
from app.services.database.repositories.knowledge.node_repository import (
    NodeRepository,
)
from app.services.database.repositories.knowledge.relationship_repository import (
    RelationshipRepository,
)
from app.services.database.repositories.knowledge.path_repository import (
    PathRepository,
)
from app.services.knowledge.similarity_service import SimilarityService

from app.core.exceptions import DatabaseError, EntityNotFoundError

logger = logging.getLogger(__name__)


class KnowledgeRepository:
    """
    Main Knowledge Repository that orchestrates knowledge operations.
    Delegates to specialized repositories and services for better separation of concerns.
    """

    def __init__(
        self,
        db: Neo4jService = Depends(),
        embedding_service: EmbeddingService = Depends(),
        embedding_cache=Depends(get_embedding_cache),
    ):
        # Core dependencies
        self.db = db
        self.embedding_service = embedding_service
        self.embedding_cache = embedding_cache

        # Initialize specialized repositories
        self.node_repo = NodeRepository(db, embedding_service, embedding_cache)
        self.relationship_repo = RelationshipRepository(db)
        self.path_repo = PathRepository(db)

        # Initialize services
        self.similarity_service = SimilarityService(embedding_service, self.node_repo)

    # ========== NODE OPERATIONS ==========

    async def create_knowledge_node(
        self, node: KnowledgeNode, check_duplicates: bool = True
    ) -> KnowledgeNode:
        """Create a knowledge node."""
        return await self.node_repo.create_node(node, check_duplicates)

    async def get_knowledge_node(self, node_id: str) -> Optional[KnowledgeNode]:
        """Get a knowledge node by ID."""
        return await self.node_repo.get_node(node_id)

    async def update_knowledge_node(
        self, node_id: str, properties: Dict[str, Any]
    ) -> Optional[KnowledgeNode]:
        """Update a knowledge node."""
        return await self.node_repo.update_node(node_id, properties)

    async def delete_knowledge_node(self, node_id: str) -> bool:
        """Delete a knowledge node."""
        return await self.node_repo.delete_node(node_id)

    async def update_knowledge_node_embedding(
        self, node_id: str, embedding: Any
    ) -> bool:
        """Update the embedding for a knowledge node."""
        return await self.node_repo.update_node_embedding(node_id, embedding)

    # ========== RELATIONSHIP OPERATIONS ==========

    async def create_knowledge_relationship(
        self, relationship: KnowledgeRelationship
    ) -> KnowledgeRelationship:
        """Create a knowledge relationship."""
        return await self.relationship_repo.create_relationship(relationship)

    async def get_knowledge_relationship(
        self, source_id: str, target_id: str, relationship_type: str
    ) -> Optional[Dict[str, Any]]:
        """Get a specific relationship by source_id, target_id, and type."""
        return await self.relationship_repo.get_relationship(
            source_id, target_id, relationship_type
        )

    async def update_knowledge_relationship(
        self,
        source_id: str,
        target_id: str,
        relationship_type: Union[str, RelationshipType],
        updates: Dict[str, Any],
    ) -> bool:
        """Update a relationship with new data."""
        return await self.relationship_repo.update_relationship(
            source_id, target_id, relationship_type, updates
        )

    async def get_knowledge_relationships(
        self,
        source_id: Optional[str] = None,
        target_id: Optional[str] = None,
        relationship_type: Optional[RelationshipType] = None,
        is_current: bool = True,
    ) -> List[KnowledgeRelationship]:
        """Get knowledge relationships matching criteria."""
        return await self.relationship_repo.get_relationships(
            source_id, target_id, relationship_type, is_current
        )

    # ========== PATH OPERATIONS ==========

    async def get_knowledge_paths(
        self,
        observation_name: Optional[str] = None,
        door_type_id: Optional[str] = None,
        limit: int = 5,
    ) -> List[KnowledgePath]:
        """Get knowledge paths from observation to solution."""
        return await self.path_repo.get_knowledge_paths(
            observation_name, door_type_id, limit
        )

    # ========== SIMILARITY OPERATIONS ==========

    async def find_similar_nodes_by_embedding(
        self,
        query_text: str,
        node_type: Optional[KnowledgeType] = None,
        limit: int = 10,
        threshold: float = 0.6,
    ) -> List[Tuple[KnowledgeNode, float]]:
        """Find similar nodes using stored embeddings."""
        return await self.similarity_service.find_similar_nodes_by_embedding(
            query_text, node_type, limit, threshold
        )

    async def find_similar_nodes(
        self,
        node_type: KnowledgeType,
        name: str,
        description: str = "",
        properties: Dict[str, Any] = None,
        similarity_threshold: float = 0.8,
    ) -> List[KnowledgeNode]:
        """Find nodes similar to the provided parameters."""
        return await self.similarity_service.find_similar_nodes(
            node_type, name, description, properties, similarity_threshold
        )

    # ========== UTILITY OPERATIONS ==========

    async def execute_read_query(
        self, query: str, params: dict = None
    ) -> List[Dict[str, Any]]:
        """Execute a read query against the Neo4j database."""
        return await self.node_repo.execute_read_query(query, params)

    async def execute_write_query(
        self, query: str, params: dict = None
    ) -> List[Dict[str, Any]]:
        """Execute a write query against the Neo4j database."""
        return await self.node_repo.execute_write_query(query, params)

    # ========== DEPRECATED METHODS (backward compatibility) ==========

    async def increment_success_count(
        self,
        source_id: str,
        target_id: str,
        relationship_type: RelationshipType,
    ) -> Optional[KnowledgeRelationship]:
        """DEPRECATED: Use the new feedback system instead."""
        logger.warning(
            "increment_success_count is deprecated. Use the new feedback system instead."
        )

        try:
            query = f"""
            MATCH (a {{id: $source_id}})-[r:{relationship_type.value}]->(b {{id: $target_id}})
            SET r.success_count = COALESCE(r.success_count, 0) + 1,
                r.updated_at = $updated_at
            RETURN r
            """

            params = {
                "source_id": source_id,
                "target_id": target_id,
                "updated_at": datetime.utcnow().isoformat(),
            }

            results = await self.execute_write_query(query, params)
            if not results:
                return None

            from app.utils.knowledge_utils import (
                KnowledgeRelationshipConverter,
            )

            converter = KnowledgeRelationshipConverter()
            return converter.dict_to_knowledge_relationship(
                results[0]["r"], source_id, target_id, relationship_type
            )
        except Exception as e:
            logger.error(f"Error incrementing success count: {str(e)}")
            raise DatabaseError(f"Failed to increment success count: {str(e)}")

    async def create_relationship_feedback(
        self,
        relationship_ids: List[Dict[str, str]],
        intent: str,
    ) -> List[Dict[str, Any]]:
        """DEPRECATED: Use the new feedback system instead."""
        logger.warning(
            "create_relationship_feedback is deprecated. Use the new feedback system instead."
        )

        created_feedback = []
        timestamp = datetime.now().isoformat()

        for rel_info in relationship_ids:
            source_id = rel_info["source_id"]
            target_id = rel_info["target_id"]
            feedback_id = str(uuid.uuid4())

            # Get relationship details
            get_rel_query = """
            MATCH (source)-[r]->(target)
            WHERE source.id = $source_id AND target.id = $target_id
            RETURN r.type as rel_type, r.trust_score as trust_score,
                source.name as source_name, target.name as target_name
            """

            rel_result = await self.execute_read_query(
                get_rel_query, {"source_id": source_id, "target_id": target_id}
            )

            feedback_data = {
                "feedback_id": feedback_id,
                "source_id": source_id,
                "target_id": target_id,
                "intent": intent,
                "timestamp": timestamp,
            }

            if rel_result:
                rel_data = rel_result[0]
                feedback_data.update(
                    {
                        "source_name": rel_data.get("source_name", "unknown"),
                        "target_name": rel_data.get("target_name", "unknown"),
                        "relationship_type": rel_data.get("rel_type", "unknown"),
                        "trust_score": rel_data.get("trust_score", 0.0),
                    }
                )
            else:
                feedback_data.update(
                    {
                        "source_name": "not_found",
                        "target_name": "not_found",
                        "relationship_type": "not_found",
                        "trust_score": 0.0,
                    }
                )

            # Create feedback record
            create_feedback_query = """
            CREATE (f:RelationshipFeedback {
                id: $feedback_id,
                source_id: $source_id,
                target_id: $target_id,
                intent: $intent,
                timestamp: $timestamp,
                created_at: datetime()
            })
            RETURN f
            """

            await self.execute_write_query(
                create_feedback_query,
                {
                    "feedback_id": feedback_id,
                    "source_id": source_id,
                    "target_id": target_id,
                    "intent": intent,
                    "timestamp": timestamp,
                },
            )

            created_feedback.append(feedback_data)

        return created_feedback

    async def delete_relationship_feedback(self, feedback_id: str) -> bool:
        """DEPRECATED: Use the new feedback system instead."""
        logger.warning(
            "delete_relationship_feedback is deprecated. Use the new feedback system instead."
        )

        delete_query = """
        MATCH (f:RelationshipFeedback {id: $feedback_id})
        DELETE f
        RETURN count(f) as deleted_count
        """

        result = await self.execute_write_query(
            delete_query, {"feedback_id": feedback_id}
        )
        return result[0]["deleted_count"] > 0 if result else False

    async def get_relationship_feedback_history(
        self, source_id: str, target_id: str, limit: int = 10
    ) -> List[Dict[str, Any]]:
        """DEPRECATED: Use get_knowledge_relationship instead."""
        logger.warning(
            "get_relationship_feedback_history is deprecated. Use get_knowledge_relationship instead."
        )

        query = """
        MATCH (f:RelationshipFeedback)
        WHERE f.source_id = $source_id AND f.target_id = $target_id
        RETURN f.id as feedback_id, f.intent as intent, f.timestamp as timestamp, f.created_at as created_at
        ORDER BY f.created_at DESC
        LIMIT $limit
        """

        result = await self.execute_read_query(
            query,
            {"source_id": source_id, "target_id": target_id, "limit": limit},
        )

        return [dict(record) for record in result] if result else []

    # ========== PRIVATE HELPER METHODS ==========

    def _is_numpy_array(self, value):
        """Check if a value is a NumPy array without triggering boolean ambiguity errors."""
        from app.utils.knowledge_utils import is_numpy_array

        return is_numpy_array(value)
