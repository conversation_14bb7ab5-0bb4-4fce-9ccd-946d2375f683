# app/services/database/repositories/base.py
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from app.services.database.neo4j import Neo4jService
from app.services.embeddings.embedding import EmbeddingService
from app.core.exceptions import DatabaseError
import logging

logger = logging.getLogger(__name__)


class BaseRepository(ABC):
    """Base repository class with common database operations."""

    def __init__(
        self,
        db: Neo4jService,
        embedding_service: Optional[EmbeddingService] = None,
        embedding_cache=None,
    ):
        self.db = db
        self.embedding_service = embedding_service
        self.embedding_cache = embedding_cache

    async def execute_read_query(
        self, query: str, params: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Execute a read query against the Neo4j database."""
        try:
            results = await self.db.execute_query(query, params or {})
            if "path" in query.lower():
                logger.info(
                    f"Query returning path, first result type: {type(results[0] if results else 'No results')}"
                )
                if results and "path" in results[0]:
                    logger.info(
                        f"Path in first result has type: {type(results[0]['path'])}"
                    )
            return results
        except Exception as e:
            logger.error(f"Database query error: {str(e)}")
            raise DatabaseError(f"Failed to execute query: {str(e)}")

    async def execute_write_query(
        self, query: str, params: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Execute a write query against the Neo4j database."""
        try:
            results = await self.db.execute_query(query, params or {})
            return results
        except Exception as e:
            logger.error(f"Database write query error: {str(e)}")
            raise DatabaseError(f"Failed to execute write query: {str(e)}")
