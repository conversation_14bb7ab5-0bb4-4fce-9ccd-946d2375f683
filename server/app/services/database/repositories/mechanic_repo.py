from typing import List, Dict, Any, Optional
from fastapi import Depends
import logging
from datetime import datetime
import uuid

from app.models.domain.mechanic import Mechanic, MechanicRole
from app.services.database.neo4j import Neo4jService
from app.core.exceptions import DatabaseError, EntityNotFoundError
from app.core.security import get_password_hash, verify_password

logger = logging.getLogger(__name__)


class MechanicRepository:
    """
    Repository for mechanic-related database operations.
    """

    def __init__(self, db: Neo4jService = Depends()):
        self.db = db

    async def create_mechanic(self, mechanic: Mechanic, password: str) -> Mechanic:
        """
        Create a mechanic.

        Args:
            mechanic: The mechanic to create.
            password: The plain text password.

        Returns:
            Mechanic: The created mechanic.

        Raises:
            DatabaseError: If mechanic creation fails.
        """
        # Hash password
        hashed_password = get_password_hash(password)

        properties = mechanic.to_dict()
        properties["hashed_password"] = hashed_password

        try:
            mechanic_data = await self.db.create_node("Mechanic", properties)
            return self._dict_to_mechanic(mechanic_data)
        except Exception as e:
            logger.error(f"Error creating mechanic: {str(e)}")
            raise DatabaseError(f"Failed to create mechanic: {str(e)}")

    async def get_by_id(self, mechanic_id: str) -> Optional[Mechanic]:
        """
        Get a mechanic by ID.

        Args:
            mechanic_id: The mechanic ID.

        Returns:
            Optional[Mechanic]: The mechanic or None if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            mechanic_data = await self.db.get_node_by_id("Mechanic", mechanic_id)
            if not mechanic_data:
                return None

            return self._dict_to_mechanic(mechanic_data)
        except Exception as e:
            logger.error(f"Error getting mechanic by ID: {str(e)}")
            raise DatabaseError(f"Failed to get mechanic by ID: {str(e)}")

    async def get_by_email(self, email: str) -> Optional[Mechanic]:
        """
        Get a mechanic by email.

        Args:
            email: The mechanic email.

        Returns:
            Optional[Mechanic]: The mechanic or None if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            query = """
            MATCH (m:Mechanic {email: $email})
            RETURN m
            """

            results = await self.db.execute_query(query, {"email": email})
            if not results:
                return None

            return self._dict_to_mechanic(results[0]["m"])
        except Exception as e:
            logger.error(f"Error getting mechanic by email: {str(e)}")
            raise DatabaseError(f"Failed to get mechanic by email: {str(e)}")

    async def get_all(self, skip: int = 0, limit: int = 100) -> List[Mechanic]:
        """
        Get all mechanics.

        Args:
            skip: Number of mechanics to skip.
            limit: Maximum number of mechanics to return.

        Returns:
            List[Mechanic]: The list of mechanics.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            query = """
            MATCH (m:Mechanic)
            RETURN m
            ORDER BY m.name
            SKIP $skip
            LIMIT $limit
            """

            results = await self.db.execute_query(query, {"skip": skip, "limit": limit})

            return [self._dict_to_mechanic(result["m"]) for result in results]
        except Exception as e:
            logger.error(f"Error getting all mechanics: {str(e)}")
            raise DatabaseError(f"Failed to get all mechanics: {str(e)}")

    async def update_mechanic(
        self,
        mechanic_id: str,
        properties: Dict[str, Any],
        password: Optional[str] = None,
    ) -> Optional[Mechanic]:
        """
        Update a mechanic.

        Args:
            mechanic_id: The mechanic ID.
            properties: The properties to update.
            password: Optional new password.

        Returns:
            Optional[Mechanic]: The updated mechanic or None if not found.

        Raises:
            DatabaseError: If update fails.
        """
        update_props = properties.copy()

        # Hash new password if provided
        if password:
            update_props["hashed_password"] = get_password_hash(password)

        try:
            updated_mechanic = await self.db.update_node(
                "Mechanic", mechanic_id, update_props
            )
            if not updated_mechanic:
                return None

            return self._dict_to_mechanic(updated_mechanic)
        except Exception as e:
            logger.error(f"Error updating mechanic: {str(e)}")
            raise DatabaseError(f"Failed to update mechanic: {str(e)}")

    async def delete_mechanic(self, mechanic_id: str) -> bool:
        """
        Delete a mechanic.

        Args:
            mechanic_id: The mechanic ID.

        Returns:
            bool: True if deleted, False if not found.

        Raises:
            DatabaseError: If deletion fails.
        """
        try:
            return await self.db.delete_node("Mechanic", mechanic_id)
        except Exception as e:
            logger.error(f"Error deleting mechanic: {str(e)}")
            raise DatabaseError(f"Failed to delete mechanic: {str(e)}")

    async def update_experience_years(
        self, mechanic_id: str, delta: float
    ) -> Optional[Mechanic]:
        """
        Update a mechanic's experience score.

        Args:
            mechanic_id: The mechanic ID.
            delta: The amount to adjust the score by.

        Returns:
            Optional[Mechanic]: The updated mechanic or None if not found.

        Raises:
            DatabaseError: If update fails.
        """
        try:
            query = """
            MATCH (m:Mechanic {id: $id})
            SET m.experience_years = COALESCE(m.experience_years, 0) + $delta,
                m.updated_at = $updated_at
            RETURN m
            """

            params = {
                "id": mechanic_id,
                "delta": delta,
                "updated_at": datetime.utcnow().isoformat(),
            }

            results = await self.db.execute_query(query, params)
            if not results:
                return None

            return self._dict_to_mechanic(results[0]["m"])
        except Exception as e:
            logger.error(f"Error updating experience years: {str(e)}")
            raise DatabaseError(f"Failed to update experience years: {str(e)}")

    # Modify the authenticate method to print debugging info

    async def authenticate(self, email: str, password: str) -> Optional[Mechanic]:
        """
        Authenticate a mechanic.
        """
        mechanic = await self.get_by_email(email)
        if not mechanic:
            return None

        # The issue is here - get the hashed_password from the right place
        # Try to get it from different possible locations
        query = """
        MATCH (m:Mechanic {email: $email})
        RETURN m.hashed_password as hashed_password
        """

        results = await self.db.execute_query(query, {"email": email})
        if not results or "hashed_password" not in results[0]:
            return None

        hashed_password = results[0]["hashed_password"]

        if not verify_password(password, hashed_password):
            return None

        return mechanic

    def _dict_to_mechanic(self, mechanic_dict: Dict[str, Any]) -> Mechanic:
        """Convert a Neo4j node dictionary to a Mechanic."""
        role_str = mechanic_dict.get("role", "JUNIOR")
        try:
            role = MechanicRole(role_str)
        except ValueError:
            role = MechanicRole.JUNIOR

        return Mechanic(
            id=mechanic_dict.get("id"),
            name=mechanic_dict.get("name", ""),
            email=mechanic_dict.get("email", ""),
            role=role,
            experience_years=mechanic_dict.get("experience_years", 0.0),
            properties=mechanic_dict.get("properties", {}),
            created_at=(
                datetime.fromisoformat(mechanic_dict.get("created_at"))
                if mechanic_dict.get("created_at")
                else datetime.utcnow()
            ),
            updated_at=(
                datetime.fromisoformat(mechanic_dict.get("updated_at"))
                if mechanic_dict.get("updated_at")
                else datetime.utcnow()
            ),
        )
