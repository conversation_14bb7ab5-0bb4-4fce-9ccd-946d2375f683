from typing import List, Dict, Any, Optional
from fastapi import Depends
import logging
from datetime import datetime

from app.models.domain.door import DoorType, DoorPart, Environment
from app.services.database.neo4j import Neo4jService
from app.core.exceptions import DatabaseError

logger = logging.getLogger(__name__)


class DoorRepository:
    """
    Repository for door-related database operations.
    """

    def __init__(self, db: Neo4jService = Depends()):
        self.db = db

    # Door Type Operations
    async def create_door_type(self, door_type: DoorType) -> DoorType:
        """
        Create a door type.

        Args:
            door_type: The door type to create.

        Returns:
            DoorType: The created door type.

        Raises:
            DatabaseError: If door type creation fails.
        """
        properties = door_type.to_dict()

        try:
            door_type_data = await self.db.create_node("DOOR_TYPE", properties)
            return self._dict_to_door_type(door_type_data)
        except Exception as e:
            logger.error(f"Error creating door type: {str(e)}")
            raise DatabaseError(f"Failed to create door type: {str(e)}")

    async def get_door_type_by_id(
        self, door_type_id: str
    ) -> Optional[DoorType]:
        """
        Get a door type by ID.

        Args:
            door_type_id: The door type ID.

        Returns:
            Optional[DoorType]: The door type or None if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            door_type_data = await self.db.get_node_by_id(
                "DOOR_TYPE", door_type_id
            )
            if not door_type_data:
                return None

            return self._dict_to_door_type(door_type_data)
        except Exception as e:
            logger.error(f"Error getting door type by ID: {str(e)}")
            raise DatabaseError(f"Failed to get door type by ID: {str(e)}")

    async def get_door_type_by_model(self, model: str):
        """
        Get a door type by model name.

        Args:
            model: The door model name

        Returns:
            The door type or None if not found
        """
        try:
            # Search for door type with the exact model name
            query = """
            MATCH (d:DOOR_TYPE)
            WHERE d.model = $model
            RETURN d
            """

            results = await self.db.execute_query(query, {"model": model})
            if (
                results
            ):  # Simplified check, equivalent to 'if results and len(results) > 0'
                door_data = results[0]["d"]

                # If multiple results are unexpected, warn about it
                if len(results) > 1:
                    logger.warning(
                        f"Multiple door types found for model '{model}'. Using first one."
                    )

                # Create a door type object with the data
                from app.models.domain.door import DoorType

                door_type = DoorType(
                    id=door_data.get("id"),
                    model=door_data.get("model", ""),
                    manufacturer=door_data.get("manufacturer", ""),
                )

                return door_type

            return None
        except Exception as e:
            logger.error(f"Error getting door type by model: {str(e)}")
            return None

    async def get_all_door_types(
        self, skip: int = 0, limit: int = 100
    ) -> List[DoorType]:
        """
        Get all door types.

        Args:
            skip: Number of door types to skip.
            limit: Maximum number of door types to return.

        Returns:
            List[DoorType]: The list of door types.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            query = """
            MATCH (d:DOOR_TYPE)
            RETURN d
            ORDER BY d.model
            SKIP $skip
            LIMIT $limit
            """

            results = await self.db.execute_query(
                query, {"skip": skip, "limit": limit}
            )

            return [self._dict_to_door_type(result["d"]) for result in results]
        except Exception as e:
            logger.error(f"Error getting all door types: {str(e)}")
            raise DatabaseError(f"Failed to get all door types: {str(e)}")

    async def update_door_type(
        self, door_type_id: str, properties: Dict[str, Any]
    ) -> Optional[DoorType]:
        """
        Update a door type.

        Args:
            door_type_id: The door type ID.
            properties: The properties to update.

        Returns:
            Optional[DoorType]: The updated door type or None if not found.

        Raises:
            DatabaseError: If update fails.
        """
        try:
            updated_door_type = await self.db.update_node(
                "DOOR_TYPE", door_type_id, properties
            )
            if not updated_door_type:
                return None

            return self._dict_to_door_type(updated_door_type)
        except Exception as e:
            logger.error(f"Error updating door type: {str(e)}")
            raise DatabaseError(f"Failed to update door type: {str(e)}")

    async def delete_door_type(self, door_type_id: str) -> bool:
        """
        Delete a door type.

        Args:
            door_type_id: The door type ID.

        Returns:
            bool: True if deleted, False if not found.

        Raises:
            DatabaseError: If deletion fails.
        """
        try:
            return await self.db.delete_node("DOOR_TYPE", door_type_id)
        except Exception as e:
            logger.error(f"Error deleting door type: {str(e)}")
            raise DatabaseError(f"Failed to delete door type: {str(e)}")

    # Door Part Operations
    async def create_door_part(self, door_part: DoorPart) -> DoorPart:
        """
        Create a door part.

        Args:
            door_part: The door part to create.

        Returns:
            DoorPart: The created door part.

        Raises:
            DatabaseError: If door part creation fails.
        """
        properties = door_part.to_dict()

        try:
            door_part_data = await self.db.create_node("DOOR_PART", properties)
            return self._dict_to_door_part(door_part_data)
        except Exception as e:
            logger.error(f"Error creating door part: {str(e)}")
            raise DatabaseError(f"Failed to create door part: {str(e)}")

    async def get_door_part_by_id(
        self, door_part_id: str
    ) -> Optional[DoorPart]:
        """
        Get a door part by ID.

        Args:
            door_part_id: The door part ID.

        Returns:
            Optional[DoorPart]: The door part or None if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            door_part_data = await self.db.get_node_by_id(
                "DOOR_PART", door_part_id
            )
            if not door_part_data:
                return None

            return self._dict_to_door_part(door_part_data)
        except Exception as e:
            logger.error(f"Error getting door part by ID: {str(e)}")
            raise DatabaseError(f"Failed to get door part by ID: {str(e)}")

    async def get_all_door_parts(
        self, skip: int = 0, limit: int = 100
    ) -> List[DoorPart]:
        """
        Get all door parts.

        Args:
            skip: Number of door parts to skip.
            limit: Maximum number of door parts to return.

        Returns:
            List[DoorPart]: The list of door parts.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            query = """
            MATCH (p:DOOR_PART)
            RETURN p
            ORDER BY p.name
            SKIP $skip
            LIMIT $limit
            """

            results = await self.db.execute_query(
                query, {"skip": skip, "limit": limit}
            )

            return [self._dict_to_door_part(result["p"]) for result in results]
        except Exception as e:
            logger.error(f"Error getting all door parts: {str(e)}")
            raise DatabaseError(f"Failed to get all door parts: {str(e)}")

    async def update_door_part(
        self, door_part_id: str, properties: Dict[str, Any]
    ) -> Optional[DoorPart]:
        """
        Update a door part.

        Args:
            door_part_id: The door part ID.
            properties: The properties to update.

        Returns:
            Optional[DoorPart]: The updated door part or None if not found.

        Raises:
            DatabaseError: If update fails.
        """
        try:
            updated_door_part = await self.db.update_node(
                "DOOR_PART", door_part_id, properties
            )
            if not updated_door_part:
                return None

            return self._dict_to_door_part(updated_door_part)
        except Exception as e:
            logger.error(f"Error updating door part: {str(e)}")
            raise DatabaseError(f"Failed to update door part: {str(e)}")

    async def delete_door_part(self, door_part_id: str) -> bool:
        """
        Delete a door part.

        Args:
            door_part_id: The door part ID.

        Returns:
            bool: True if deleted, False if not found.

        Raises:
            DatabaseError: If deletion fails.
        """
        try:
            return await self.db.delete_node("DOOR_PART", door_part_id)
        except Exception as e:
            logger.error(f"Error deleting door part: {str(e)}")
            raise DatabaseError(f"Failed to delete door part: {str(e)}")

    # Environment Operations
    async def create_environment(
        self, environment: Environment
    ) -> Environment:
        """
        Create an environment.

        Args:
            environment: The environment to create.

        Returns:
            Environment: The created environment.

        Raises:
            DatabaseError: If environment creation fails.
        """
        properties = environment.to_dict()

        try:
            environment_data = await self.db.create_node(
                "ENVIRONMENT", properties
            )
            return self._dict_to_environment(environment_data)
        except Exception as e:
            logger.error(f"Error creating environment: {str(e)}")
            raise DatabaseError(f"Failed to create environment: {str(e)}")

    async def get_environment_by_id(
        self, environment_id: str
    ) -> Optional[Environment]:
        """
        Get an environment by ID.

        Args:
            environment_id: The environment ID.

        Returns:
            Optional[Environment]: The environment or None if not found.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            environment_data = await self.db.get_node_by_id(
                "ENVIRONMENT", environment_id
            )
            if not environment_data:
                return None

            return self._dict_to_environment(environment_data)
        except Exception as e:
            logger.error(f"Error getting environment by ID: {str(e)}")
            raise DatabaseError(f"Failed to get environment by ID: {str(e)}")

    async def get_all_environments(
        self, skip: int = 0, limit: int = 100
    ) -> List[Environment]:
        """
        Get all environments.

        Args:
            skip: Number of environments to skip.
            limit: Maximum number of environments to return.

        Returns:
            List[Environment]: The list of environments.

        Raises:
            DatabaseError: If query execution fails.
        """
        try:
            query = """
            MATCH (e:ENVIRONMENT)
            RETURN e
            ORDER BY e.condition
            SKIP $skip
            LIMIT $limit
            """

            results = await self.db.execute_query(
                query, {"skip": skip, "limit": limit}
            )

            return [
                self._dict_to_environment(result["e"]) for result in results
            ]
        except Exception as e:
            logger.error(f"Error getting all environments: {str(e)}")
            raise DatabaseError(f"Failed to get all environments: {str(e)}")

    async def update_environment(
        self, environment_id: str, properties: Dict[str, Any]
    ) -> Optional[Environment]:
        """
        Update an environment.

        Args:
            environment_id: The environment ID.
            properties: The properties to update.

        Returns:
            Optional[Environment]: The updated environment or None if not found.

        Raises:
            DatabaseError: If update fails.
        """
        try:
            updated_environment = await self.db.update_node(
                "ENVIRONMENT", environment_id, properties
            )
            if not updated_environment:
                return None

            return self._dict_to_environment(updated_environment)
        except Exception as e:
            logger.error(f"Error updating environment: {str(e)}")
            raise DatabaseError(f"Failed to update environment: {str(e)}")

    async def delete_environment(self, environment_id: str) -> bool:
        """
        Delete an environment.

        Args:
            environment_id: The environment ID.

        Returns:
            bool: True if deleted, False if not found.

        Raises:
            DatabaseError: If deletion fails.
        """
        try:
            return await self.db.delete_node("ENVIRONMENT", environment_id)
        except Exception as e:
            logger.error(f"Error deleting environment: {str(e)}")
            raise DatabaseError(f"Failed to delete environment: {str(e)}")

    # Helper methods for converting dictionaries to domain models
    def _dict_to_door_type(self, door_type_dict: Dict[str, Any]) -> DoorType:
        """Convert a Neo4j node dictionary to a DoorType."""
        return DoorType(
            id=door_type_dict.get("id"),
            model=door_type_dict.get("model", ""),
            manufacturer=door_type_dict.get("manufacturer", ""),
            properties=door_type_dict.get("properties", {}),
            created_at=(
                datetime.fromisoformat(door_type_dict.get("created_at"))
                if door_type_dict.get("created_at")
                else datetime.utcnow()
            ),
            updated_at=(
                datetime.fromisoformat(door_type_dict.get("updated_at"))
                if door_type_dict.get("updated_at")
                else datetime.utcnow()
            ),
        )

    def _dict_to_door_part(self, door_part_dict: Dict[str, Any]) -> DoorPart:
        """Convert a Neo4j node dictionary to a DoorPart."""
        return DoorPart(
            id=door_part_dict.get("id"),
            name=door_part_dict.get("name", ""),
            function=door_part_dict.get("function", ""),
            properties=door_part_dict.get("properties", {}),
            created_at=(
                datetime.fromisoformat(door_part_dict.get("created_at"))
                if door_part_dict.get("created_at")
                else datetime.utcnow()
            ),
            updated_at=(
                datetime.fromisoformat(door_part_dict.get("updated_at"))
                if door_part_dict.get("updated_at")
                else datetime.utcnow()
            ),
        )

    def _dict_to_environment(
        self, environment_dict: Dict[str, Any]
    ) -> Environment:
        """Convert a Neo4j node dictionary to an Environment."""
        return Environment(
            id=environment_dict.get("id"),
            condition=environment_dict.get("condition", ""),
            description=environment_dict.get("description", ""),
            properties=environment_dict.get("properties", {}),
            created_at=(
                datetime.fromisoformat(environment_dict.get("created_at"))
                if environment_dict.get("created_at")
                else datetime.utcnow()
            ),
            updated_at=(
                datetime.fromisoformat(environment_dict.get("updated_at"))
                if environment_dict.get("updated_at")
                else datetime.utcnow()
            ),
        )
