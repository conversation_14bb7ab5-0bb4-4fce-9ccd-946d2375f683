import asyncio
import functools
import inspect
import time
import json
import os
from datetime import datetime
from typing import Callable, Any


def _create_timer_log_entry(
    class_name: str,
    function_name: str,
    execution_time: float,
    status: str = "success",
    error: str = None,
) -> dict:
    """
    Create a standardized log entry for timer decorators.

    Args:
        class_name: Name of the class (if applicable)
        function_name: Name of the function being timed
        execution_time: Execution time in seconds
        status: Status of execution ("success" or "error")
        error: Error message if status is "error"

    Returns:
        Dictionary containing the log entry
    """

    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "class": class_name,
        "function": function_name,
        "execution_time_ms": round(execution_time * 1000, 2),
        "execution_time_secs": round(execution_time, 2),
        "status": status,
    }

    if error and status == "error":
        log_entry["error"] = str(error)

    return log_entry


def _write_simple_log_to_file(log_entry: dict, log_file: str):
    """Write a log entry to a specified JSON file."""
    try:
        # Create directory if it doesn't exist (only if there's actually a directory)
        log_file = os.path.join("logs", log_file)
        dir_path = os.path.dirname(log_file)
        if dir_path:  # Only create if there's actually a directory to create
            os.makedirs(dir_path, exist_ok=True)

        # Append to log file
        with open(log_file, "a") as f:
            f.write(json.dumps(log_entry) + "\n")
    except Exception as e:
        # Using print as fallback since logger isn't defined in this context
        print(f"Error writing to log file {log_file}: {str(e)}")


def timer(log_file: str = "function_timings.txt"):
    """
    Smart timer decorator that automatically detects async vs sync functions
    and logs execution time appropriately.

    Args:
        log_file: Path to the JSON file where timings will be stored
    """

    def decorator(func: Callable) -> Callable:
        # Auto-detect if function is async or sync
        is_async = asyncio.iscoroutinefunction(func)

        if is_async:
            # Async wrapper
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs) -> Any:
                # Get basic function info
                function_name = func.__name__
                class_name = None
                if args and hasattr(args[0], "__class__"):
                    class_name = args[0].__class__.__name__

                # Start timing
                start_time = time.perf_counter()

                try:
                    # Execute the async function
                    result = await func(*args, **kwargs)

                    # End timing
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time

                    # Create log entry
                    log_entry = _create_timer_log_entry(
                        class_name, function_name, execution_time, status="success"
                    )

                    # Add result count if applicable
                    if hasattr(result, "__len__"):
                        log_entry["result_count"] = len(result)

                    # Write to log
                    _write_simple_log_to_file(log_entry, log_file)

                    return result

                except Exception as e:
                    # End timing on error
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time

                    # Create error log entry
                    log_entry = _create_timer_log_entry(
                        class_name,
                        function_name,
                        execution_time,
                        status="error",
                        error=str(e),
                    )

                    # Write to log
                    _write_simple_log_to_file(log_entry, log_file)

                    # Re-raise the exception
                    raise e

            return async_wrapper

        else:
            # Sync wrapper
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs) -> Any:
                # Get basic function info
                function_name = func.__name__
                class_name = None
                if args and hasattr(args[0], "__class__"):
                    class_name = args[0].__class__.__name__

                # Start timing
                start_time = time.perf_counter()

                try:
                    # Execute the sync function
                    result = func(*args, **kwargs)

                    # End timing
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time

                    # Create log entry
                    log_entry = _create_timer_log_entry(
                        class_name, function_name, execution_time, status="success"
                    )

                    # Add result count if applicable
                    if hasattr(result, "__len__"):
                        log_entry["result_count"] = len(result)

                    # Write to log
                    _write_simple_log_to_file(log_entry, log_file)

                    return result

                except Exception as e:
                    # End timing on error
                    end_time = time.perf_counter()
                    execution_time = end_time - start_time

                    # Create error log entry
                    log_entry = _create_timer_log_entry(
                        class_name,
                        function_name,
                        execution_time,
                        status="error",
                        error=str(e),
                    )

                    # Write to log
                    _write_simple_log_to_file(log_entry, log_file)

                    # Re-raise the exception
                    raise e

            return sync_wrapper

    return decorator


# Example usage:
if __name__ == "__main__":
    # Works with sync functions
    @smart_timer("my_timings.txt")
    def sync_function(n):
        return sum(range(n))

    # Works with async functions
    @smart_timer("my_timings.txt")
    async def async_function(n):
        await asyncio.sleep(0.1)
        return sum(range(n))

    # Works with class methods
    class MyClass:
        @smart_timer("class_timings.txt")
        def sync_method(self, data):
            return len(data)

        @smart_timer("class_timings.txt")
        async def async_method(self, data):
            await asyncio.sleep(0.1)
            return len(data)

    # Test the decorators
    print(sync_function(1000))

    async def test_async():
        result = await async_function(1000)
        print(result)

        obj = MyClass()
        print(obj.sync_method([1, 2, 3, 4, 5]))
        result2 = await obj.async_method([1, 2, 3, 4, 5])
        print(result2)

    # Run async test
    asyncio.run(test_async())
