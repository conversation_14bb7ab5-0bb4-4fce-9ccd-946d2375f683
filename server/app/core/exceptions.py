from fastapi import HTTPException, status


class DatabaseError(Exception):
    """
    Base exception for database operations.
    """

    def __init__(self, message: str = "Database operation failed"):
        self.message = message
        super().__init__(self.message)


class Neo4jConnectionError(DatabaseError):
    """
    Exception raised for Neo4j connection errors.
    """

    def __init__(self, message: str = "Failed to connect to Neo4j database"):
        self.message = message
        super().__init__(self.message)


class KnowledgeExtractionError(Exception):
    """
    Exception raised for knowledge extraction errors.
    """

    def __init__(self, message: str = "Failed to extract knowledge"):
        self.message = message
        super().__init__(self.message)


class TranscriptionError(Exception):
    """
    Exception raised for speech transcription errors.
    """

    def __init__(self, message: str = "Failed to transcribe speech"):
        self.message = message
        super().__init__(self.message)


class QueryProcessingError(Exception):
    """
    Exception raised for query processing errors.
    """

    def __init__(self, message: str = "Failed to process query"):
        self.message = message
        super().__init__(self.message)


class EntityNotFoundError(Exception):
    """
    Exception raised when an entity is not found.
    """

    def __init__(self, entity_type: str, entity_id: str):
        self.message = f"{entity_type} with ID {entity_id} not found"
        self.entity_type = entity_type
        self.entity_id = entity_id
        super().__init__(self.message)


# HTTP Exceptions
def entity_not_found_exception(
    entity_type: str, entity_id: str
) -> HTTPException:
    """
    Create an HTTP exception for entity not found errors.

    Args:
        entity_type: The type of entity (e.g., "Mechanic", "Door").
        entity_id: The ID of the entity.

    Returns:
        HTTPException: The HTTP exception.
    """
    return HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=f"{entity_type} with ID {entity_id} not found",
    )


def database_operation_exception() -> HTTPException:
    """
    Create an HTTP exception for database operation errors.

    Returns:
        HTTPException: The HTTP exception.
    """
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Database operation failed",
    )


def knowledge_extraction_exception() -> HTTPException:
    """
    Create an HTTP exception for knowledge extraction errors.

    Returns:
        HTTPException: The HTTP exception.
    """
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Failed to extract knowledge",
    )


def transcription_exception() -> HTTPException:
    """
    Create an HTTP exception for speech transcription errors.

    Returns:
        HTTPException: The HTTP exception.
    """
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Failed to transcribe speech",
    )


def query_processing_exception() -> HTTPException:
    """
    Create an HTTP exception for query processing errors.

    Returns:
        HTTPException: The HTTP exception.
    """
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Failed to process query",
    )
