"""
Centralized logging configuration for Revolving Door Knowledge System.

This module provides a hierarchical logging system with separate files for different
application components while maintaining a unified console output through a parent logger.
"""

import logging
import logging.config
import os
from pathlib import Path
from typing import Optional, Dict, Any


# Logger registry - ADD NEW LOGGERS HERE!
# Each logger has: file_level, console_level, and propagate settings
LOGGER_REGISTRY = {
    "extraction": {
        "file_level": "DEBUG",  # Level for extraction.log file
        "console_level": "DEBUG",  # Minimum level to show on console for this logger
        "propagate": True,  # Whether to send messages to console (root logger)
    },
    "retrieval": {
        "file_level": "DEBUG",  # Level for retrieval.log file
        "console_level": "DEBUG",  # Minimum level to show on console for this logger
        "propagate": True,  # Whether to send messages to console (root logger)
    },
    "database": {
        "file_level": "DEBUG",  # Level for database.log file
        "console_level": "DEBUG",  # Minimum level to show on console for this logger
        "propagate": True,  # Whether to send messages to console (root logger)
    },
    "api": {
        "file_level": "DEBUG",  # Level for api.log file
        "console_level": "DEBUG",  # Minimum level to show on console for this logger
        "propagate": True,  # Whether to send messages to console (root logger)
    },
    "other": {
        "file_level": "DEBUG",  # Level for other.log file (fallback for misc logging)
        "console_level": "DEBUG",  # Minimum level to show on console for this logger
        "propagate": True,  # Whether to send messages to console (root logger)
    },
    # TO ADD NEW LOGGER, JUST ADD ENTRY LIKE THIS:
    # "processing": {
    #     "file_level": "DEBUG",      # What level gets written to processing.log
    #     "console_level": "WARNING", # Only WARNING+ from processing shows on console
    #     "propagate": True,          # Set to False to prevent console output
    # },
    # THEN, add convenience function on line 409 and down, and import it to your .py file
}


class ColoredFormatter(logging.Formatter):
    """Custom formatter that adds colors to filename and function name for console only."""

    # ANSI color codes
    COLORS = {
        "filename": "\033[36m",  # Cyan
        "function": "\033[35m",  # Magenta
        "reset": "\033[0m",  # Reset to default
        "line": "\033[33m",  # Yellow for line numbers
    }

    def format(self, record):
        # Get the original formatted message
        original = super().format(record)

        # Add colors to filename and function
        colored = original.replace(
            f"{record.filename}:{record.lineno}",
            f"{self.COLORS['filename']}{record.filename}{self.COLORS['reset']}:{self.COLORS['line']}{record.lineno}{self.COLORS['reset']}",
        ).replace(
            f"{record.funcName}()",
            f"{self.COLORS['function']}{record.funcName}(){self.COLORS['reset']}",
        )

        return colored


class LoggingConfig:
    """Centralized logging configuration class."""

    @classmethod
    def get_config(
        cls,
        console_level: str = "INFO",
        component_levels: Optional[Dict[str, str]] = None,
        log_directory: str = "logs",
    ) -> Dict[str, Any]:
        """
        Generate logging configuration dictionary.

        Args:
            console_level: Global console logging level (DEBUG, INFO, WARNING, ERROR)
            component_levels: Override individual component file levels (optional)
            log_directory: Directory for log files

        Returns:
            Complete logging configuration dictionary
        """
        # Build effective component levels (registry defaults + overrides)
        effective_levels = {}
        for logger_name, config in LOGGER_REGISTRY.items():
            # Use override if provided, otherwise use registry default
            effective_levels[logger_name] = (
                component_levels.get(logger_name, config["file_level"]).upper()
                if component_levels
                else config["file_level"].upper()
            )

        # Dynamically create handlers for all registered loggers
        handlers = {
            "console": {
                "class": "logging.StreamHandler",
                "level": console_level.upper(),  # CONSOLE LEVEL: Change what gets displayed on console
                "formatter": "detailed_console",  # add colors
                "stream": "ext://sys.stdout",
            }
        }

        # Add file handlers for each registered logger
        for logger_name, config in LOGGER_REGISTRY.items():
            handlers[f"{logger_name}_file"] = {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "DEBUG",  # File handler captures everything the logger processes
                "formatter": "detailed_file",
                "filename": f"{log_directory}/{logger_name}.log",  # Automatic filename: name.log
                "maxBytes": 10485760,
                "backupCount": 5,
            }

        # Dynamically create loggers for all registered loggers
        loggers = {}
        for logger_name, config in LOGGER_REGISTRY.items():
            loggers[f"root.{logger_name}"] = {
                "level": effective_levels[
                    logger_name
                ],  # Controls what gets written to file
                "handlers": [f"{logger_name}_file"],
                "propagate": config.get(
                    "propagate", True
                ),  # Use propagate setting from registry
                # NOTE: If propagate=True, messages go to root console handler
                # If propagate=False, messages only go to the file handler
            }

        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "detailed_console": {
                    "()": ColoredFormatter,
                    "format": "%(asctime)s | %(name)s | %(levelname)s | %(filename)s:%(lineno)d | %(funcName)s() | %(message)s",
                    "datefmt": "%H:%M:%S",
                },
                "detailed_file": {
                    "format": "%(asctime)s | %(levelname)s | %(filename)s:%(lineno)d | %(funcName)s() | %(message)s",
                    "datefmt": "%H:%M:%S",
                },
                "simple": {
                    "format": "%(asctime)s | %(levelname)s | %(message)s",
                    "datefmt": "%H:%M:%S",
                },
            },
            "handlers": handlers,
            "loggers": loggers,
            "root": {
                "level": console_level.upper(),  # CONSOLE LEVEL: Root logger level - messages below this won't reach console
                "handlers": ["console"],
            },
            "settings": {
                "console_level": console_level.upper(),
                "default_file_level": "DEBUG",
                "log_directory": log_directory,
                "component_levels": effective_levels,
                "registry_config": LOGGER_REGISTRY,  # Store original registry config
            },
        }

# Singleton lo
class LoggingManager:
    """Manages the centralized logging system."""

    _instance = None
    _initialized = False
    _loggers: Dict[str, logging.Logger] = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._config: Optional[Dict[str, Any]] = None
            LoggingManager._initialized = True

    def setup_logging(
        self,
        console_level: str = "INFO",
        component_levels: Optional[Dict[str, str]] = None,
        log_directory: str = "logs",
        add_run_separator: bool = True,
    ) -> None:
        """
        Initialize logging configuration.

        Args:
            console_level: Console logging level (DEBUG, INFO, WARNING, ERROR)
            component_levels: Dict of component names to log levels (e.g. {"extraction": "DEBUG", "api": "INFO"})
            log_directory: Directory for log files
            add_run_separator: Whether to add visual separator in log files for each new run
        """
        # Generate configuration using LoggingConfig class
        self._config = LoggingConfig.get_config(
            console_level=console_level,
            component_levels=component_levels,
            log_directory=log_directory,
        )

        # Create logs directory
        log_dir = Path(self._config["settings"]["log_directory"])
        log_dir.mkdir(exist_ok=True)

        # Apply logging configuration
        logging.config.dictConfig(self._config)

        # Cache loggers
        self._cache_loggers()

        # Add visual separator to log files if requested
        if add_run_separator:
            self._add_run_separator(log_dir)

        # Log initialization
        root_logger = logging.getLogger("root")
        root_logger.info("Logging system initialized successfully")

    def _add_run_separator(self, log_dir: Path) -> None:
        """Add visual separator to each log file to mark new run."""
        import datetime

        separator = f"\n{'='*80}\n NEW RUN STARTED: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n{'='*80}\n"

        # Get log files from registry (auto-generate filenames as name.log)
        for logger_name in LOGGER_REGISTRY.keys():
            log_filename = f"{logger_name}.log"
            log_path = log_dir / log_filename
            try:
                # Create file if it doesn't exist, append separator if it does
                with open(log_path, "a", encoding="utf-8") as f:
                    f.write(separator)
            except Exception:
                # Silently ignore if we can't write separator (e.g., permissions)
                pass

    def _cache_loggers(self) -> None:
        """Pre-cache commonly used loggers."""
        for logger_name in LOGGER_REGISTRY.keys():
            full_logger_name = f"root.{logger_name}"
            self._loggers[logger_name] = logging.getLogger(full_logger_name)

    def get_logger(self, category: str) -> logging.Logger:
        """
        Get logger for specific category.

        Args:
            category: Logger category (extraction, retrieval, database, api)

        Returns:
            Logger instance for the specified category
        """
        if category not in self._loggers:
            logger_name = f"root.{category}"
            self._loggers[category] = logging.getLogger(logger_name)

        return self._loggers[category]

    def set_console_level(self, level: str) -> None:
        """
        Change console logging level at runtime.

        Args:
            level: New console level (DEBUG, INFO, WARNING, ERROR)
        """
        console_handler = logging.getLogger().handlers[
            0
        ]  # Assuming first handler is console
        console_handler.setLevel(getattr(logging, level.upper()))

    def set_component_level(self, component: str, level: str) -> None:
        """
        Change a specific component's logging level at runtime.

        Args:
            component: Component name (extraction, retrieval, database, api)
            level: New log level (DEBUG, INFO, WARNING, ERROR)
        """
        if component in self._loggers:
            self._loggers[component].setLevel(getattr(logging, level.upper()))


# Global logging manager instance
_logging_manager = LoggingManager()


# Convenience functions for easy access without setup in individual files
def setup_logging(
    console_level: str = "INFO",
    component_levels: Optional[Dict[str, str]] = None,
    log_directory: str = "logs",
    add_run_separator: bool = True,
) -> None:
    """Initialize the logging system."""
    _logging_manager.setup_logging(
        console_level, component_levels, log_directory, add_run_separator
    )


def get_logger(category: str) -> logging.Logger:
    """Get logger for specific category."""
    return _logging_manager.get_logger(category)


def set_console_level(level: str) -> None:
    """Change console logging level."""
    _logging_manager.set_console_level(level)


def set_component_level(component: str, level: str) -> None:
    """Change component logging level."""
    _logging_manager.set_component_level(component, level)


def set_logger_console_level(logger_name: str, level: str) -> None:
    """
    Set individual logger level to control console output.

    Args:
        logger_name: Name of the logger (e.g., "extraction", "api", "other")
        level: Minimum level for this logger (DEBUG, INFO, WARNING, ERROR)

    Example:
        # Only show WARNING+ from extraction logger on console
        set_logger_console_level("extraction", "WARNING")

        # Only show ERROR+ from api logger on console
        set_logger_console_level("api", "ERROR")
    """
    if logger_name not in LOGGER_REGISTRY:
        available = list(LOGGER_REGISTRY.keys())
        raise ValueError(f"Logger '{logger_name}' not found. Available: {available}")

    logger = get_logger(logger_name)
    logger.setLevel(getattr(logging, level.upper()))


# Dynamic logger getter functions
def get_registered_loggers() -> Dict[str, Dict[str, str]]:
    """Get all registered logger names and their configurations."""
    return LOGGER_REGISTRY.copy()


def print_logger_info() -> None:
    """Print information about all registered loggers and their current levels."""
    print("\n📋 Registered Loggers Configuration:")
    print("=" * 60)

    for name, config in LOGGER_REGISTRY.items():
        filename = f"{name}.log"
        print(f"🔹 {name}")
        print(f"   📁 File: logs/{filename}")
        print(f"   📄 File Level: {config['file_level']}")
        print(f"   🖥️  Console Level: {config['console_level']} (recommended)")

        # Show actual logger level if available
        try:
            logger = get_logger(name)
            actual_level = logging.getLevelName(logger.level)
            print(f"   ⚡ Current Level: {actual_level}")
        except:
            pass
        print()

    print("   To control console output per logger:")
    print("   set_logger_console_level('logger_name', 'WARNING')")
    print("   setup_logging(console_level='INFO')")
    print()


# Auto-generated convenience functions for all registered loggers
def __getattr__(name: str):
    """
    Dynamically create logger convenience functions.

    Usage:
        log_extraction() -> gets extraction logger
        log_api() -> gets api logger
        log_processing() -> gets processing logger (if registered)
    """
    if name.startswith("log_"):
        logger_name = name[4:]  # Remove 'log_' prefix
        if logger_name in LOGGER_REGISTRY:
            return lambda: get_logger(logger_name)

    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


# Pre-defined convenience functions for the default loggers (for IDE support)
def log_extraction() -> logging.Logger:
    """Get extraction logger."""
    return get_logger("extraction")


def log_retrieval() -> logging.Logger:
    """Get retrieval logger."""
    return get_logger("retrieval")


def log_database() -> logging.Logger:
    """Get database logger."""
    return get_logger("database")


def log_api() -> logging.Logger:
    """Get API logger."""
    return get_logger("api")


def log_other() -> logging.Logger:
    """Get 'other' logger for miscellaneous logging that doesn't fit other categories."""
    return get_logger("other")


def log_console() -> logging.Logger:
    """Get root logger for console-only logging (no file output)."""
    return logging.getLogger("root")


# Decorator for automatic logging with context
def auto_log(category: str, level: str = "INFO"):
    """
    Decorator for automatic function entry/exit logging.

    Args:
        category: Logger category (any registered logger name)
        level: Log level for the messages
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            if category not in LOGGER_REGISTRY:
                raise ValueError(
                    f"Logger category '{category}' not registered. Available: {list(LOGGER_REGISTRY.keys())}"
                )

            logger = get_logger(category)
            log_level = getattr(logging, level.upper())
            logger.log(
                log_level, f"Entering {func.__name__} with args={args}, kwargs={kwargs}"
            )

            try:
                result = func(*args, **kwargs)
                logger.log(log_level, f"Exiting {func.__name__} successfully")
                return result
            except Exception as e:
                logger.error(f"Error in {func.__name__}: {str(e)}")
                raise

        return wrapper

    return decorator



# Add this import at the top after the existing imports
# 📚 USAGE EXAMPLES:
# ==================================================================================
#
# 🔹 Adding a New Logger (EASY!):
# --------------------------------
# Just add to LOGGER_REGISTRY:
#
# "processing": {
#     "file_level": "DEBUG",      # Everything goes to processing.log
#     "console_level": "WARNING", # Recommended: only WARNING+ on console
#     "propagate": True,          # Set to False to prevent console output entirely
# },
#
# 🔹 Using Your New Logger:
# -------------------------
# logger = log_processing()  # Auto-generated function!
# logger.info("Processing started")
#
# 🔹 Fallback/Misc Logging:
# --------------------------
# other_logger = log_other()  # For misc stuff that doesn't fit other categories
# other_logger.info("Something random happened")
#
# # Or get_logger() automatically falls back to "other" for unknown categories:
# random_logger = get_logger("unknown_category")  # Returns "other" logger
#
# 🔹 Console-Only Logging:
# -------------------------
# console_logger = log_console()  # Direct access to root logger (console only)
# console_logger.info("This only appears on console")
# console_logger.error("Console-only error message")
#
# 🔹 Disable Console Output for Specific Logger:
# -----------------------------------------------
# In LOGGER_REGISTRY, set "propagate": False:
# "background_tasks": {
#     "file_level": "DEBUG",
#     "console_level": "INFO",  # This is ignored when propagate=False
#     "propagate": False,       # No console output, only file logging
# },
#
# 🔹 Setup with Custom Levels:
# -----------------------------
# setup_logging(
#     console_level="INFO",
#     component_levels={
#         "extraction": "DEBUG",    # Override file_level
#         "processing": "WARNING",  # Override file_level
#         "other": "INFO",          # Control fallback logger
#     }
# )
#
# 🔹 Runtime Console Control:
# ----------------------------
# set_logger_console_level("extraction", "ERROR")  # Only errors on console
# set_console_level("DEBUG")                       # Show everything on console
#
# 🔹 See Current Config:
# -----------------------
# print_logger_info()  # Shows all loggers and their levels
#
# ==================================================================================
