from typing import Optional
from datetime import datetime
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from jose import JWTError, jwt
from pydantic import ValidationError

from app.core.config import get_settings
from app.models.schemas.mechanic import MechanicInDB
from app.models.domain.mechanic import MechanicRole
from app.services.database.repositories.mechanic_repo import MechanicRepository
from app.core.security import (
    decode_access_token,
    get_password_hash,
    get_password_hash,
)

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/mechanics/token", auto_error=False
)


def create_dev_user() -> MechanicInDB:
    """Create a default user for development mode."""
    return MechanicInDB(
        id="dev-user-001",
        name="Development User",
        email="<EMAIL>",
        role=MechanicRole.ADMIN,
        reliability_score=1.0,
        experience_years=10,
        properties={},
        hashed_password=get_password_hash(
            "dev123"
        ),  # Add required hashed_password field
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        created_by="system",
    )


async def get_current_mechanic(
    token: str = Depends(oauth2_scheme),
    mechanic_repo: MechanicRepository = Depends(),
    settings=Depends(get_settings),
) -> MechanicInDB:
    """
    Get the current authenticated mechanic.
    In development mode, returns a default user if no token is provided.

    Args:
        token: The JWT token.
        mechanic_repo: The mechanic repository.
        settings: Application settings.

    Returns:
        MechanicInDB: The authenticated mechanic.

    Raises:
        HTTPException: If authentication fails.
    """
    # Development mode bypass
    if settings.DEV_MODE:
        if not token:
            return create_dev_user()
        # If token is provided in dev mode, still try to validate it
        # but fall back to dev user if it fails
        try:
            payload = decode_access_token(token)
            mechanic_id = payload.get("sub")
            if mechanic_id:
                mechanic = await mechanic_repo.get_by_id(mechanic_id)
                if mechanic:
                    return mechanic
        except (JWTError, ValidationError, Exception):
            # In dev mode, fall back to dev user if token validation fails
            return create_dev_user()

        # If we reach here in dev mode, return dev user
        return create_dev_user()

    # Production mode - require valid token
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        payload = decode_access_token(token)
        mechanic_id = payload.get("sub")
        if mechanic_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except (JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    mechanic = await mechanic_repo.get_by_id(mechanic_id)
    if mechanic is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Mechanic not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return mechanic


async def get_current_mechanic_optional(
    token: Optional[str] = Depends(oauth2_scheme),
    mechanic_repo: MechanicRepository = Depends(),
    settings=Depends(get_settings),
) -> Optional[MechanicInDB]:
    """
    Get the current authenticated mechanic, but don't require authentication.
    Returns None if no valid token is provided and not in dev mode.
    In development mode, always returns a default user.

    Args:
        token: The JWT token (optional).
        mechanic_repo: The mechanic repository.
        settings: Application settings.

    Returns:
        Optional[MechanicInDB]: The authenticated mechanic or None.
    """
    # Development mode always returns dev user
    if settings.DEV_MODE:
        return create_dev_user()

    # Production mode - try to authenticate but don't fail if no token
    if not token:
        return None

    try:
        payload = decode_access_token(token)
        mechanic_id = payload.get("sub")
        if mechanic_id is None:
            return None

        mechanic = await mechanic_repo.get_by_id(mechanic_id)
        return mechanic
    except (JWTError, ValidationError, Exception):
        return None
