from typing import Optional
from functools import lru_cache
from pydantic_settings import BaseSettings



class Settings(BaseSettings):
    """
    Application settings.
    """

    # App Configuration
    APP_ENV: str = "development"
    APP_NAME: str = "revolving-door-knowledge-system"
    APP_VERSION: str = "0.1.0"
    LOG_LEVEL: str = "INFO"
    
    DEFAULT_USER_ID: str = "dev-user-001"

    # Development Mode - bypasses authentication when True
    DEV_MODE: bool = True  # Set to False in production
    
    SAVE_AUDIO: bool = True # save raw audio to file for debugging (output/audio_files/)

    # Neo4j Configuration
    NEO4J_URI: str = "http://localhost:7474"
    NEO4J_USERNAME: str = "neo4j"
    NEO4J_PASSWORD: str = "password"

    # OpenAI API Configuration (for speech-to-text and NLP)
    OPENAI_API_KEY: Optional[str] = None

    # Realtime GPT Configuration
    VOICE: str = "echo"
    TURN_DETECTION_TYPE: str = "server_vad"
    REALTIME_GPT_MODEL: str = "gpt-4o-realtime-preview"
    INPUT_AUDIO_TRANSCRIPTION_MODEL: str = "gpt-4o-transcribe"
    TRANSCRIPTION_LANGUAGE: str = "nl"

    # JWT Configuration (for authentication)
    JWT_SECRET: str
    JWT_ALGORITHM: str = "HS256"
    JWT_EXPIRATION_MINUTES: int = 60
    

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings from environment variables.
    Using lru_cache to avoid loading .env file multiple times.

    Returns:
        Settings: Application settings.
    """
    return Settings()
