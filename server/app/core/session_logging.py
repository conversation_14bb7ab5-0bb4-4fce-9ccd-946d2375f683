import json
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel
from app.core.logging import log_other

logger = log_other()


class SessionLogger:
    """Simple session logging to JSON files"""

    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.extraction_file = self.output_dir / "extraction_sessions.json"
        self.query_file = self.output_dir / "query_sessions.json"
        self.feedback_file = self.output_dir / "feedback_sessions.json"
        self.current_session_id = None

    def set_session_id(self, session_id: str) -> None:
        """Set the current session ID"""
        self.current_session_id = session_id

    def generate_session_id(self) -> str:
        """Generate a unique session ID that can be reused across pipelines"""
        self.current_session_id = uuid.uuid4().hex
        return self.current_session_id

    def get_current_session_id(self) -> str:
        """Get the current session ID or generate a new one if none exists"""
        if not self.current_session_id:
            logger.warning("Session ID not set - generating new one")
            return self.generate_session_id()
        return self.current_session_id

    def _load_json_file(self, file_path: Path) -> Dict:
        """Load existing JSON file or return empty dict"""
        if not file_path.exists():
            return {}

        if file_path.stat().st_size == 0:
            return {}

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except json.JSONDecodeError:
            logger.warning(
                f"Could not parse existing JSON in {file_path}, starting fresh"
            )
            return {}

    def _save_json_file(self, file_path: Path, data: Dict):
        """Save data to JSON file"""
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    def _convert_to_dict(self, obj: Any) -> Any:
        """Convert Pydantic models to dicts"""
        if hasattr(obj, "model_dump"):
            return obj.model_dump()
        elif isinstance(obj, BaseModel):
            return obj.dict()
        return obj

    def _convert_knowledge_path_to_dict(self, obj: Any) -> Any:
        """Convert KnowledgePath objects to dicts"""
        if hasattr(obj, "nodes") and hasattr(obj, "trust_score"):
            # This is a KnowledgePath object
            return {
                "nodes": [
                    node.__dict__ if hasattr(node, "__dict__") else str(node)
                    for node in obj.nodes
                ],
                "relationships": [
                    rel.__dict__ if hasattr(rel, "__dict__") else str(rel)
                    for rel in getattr(obj, "relationships", [])
                ],
                "trust_score": obj.trust_score,
            }
        else:
            # Fallback to object attributes
            return obj.__dict__

    def _extract_node_pointers(self, llm_output: Any) -> List[str]:
        """Extract node IDs from LLM output for tracking"""
        # Convert to dict if it's a Pydantic model
        output_dict = self._convert_to_dict(llm_output)

        if not isinstance(output_dict, dict):
            return []

        return self._extract_pointers_from_knowledge_paths(output_dict)

    # def _convert_output_to_dict(self, llm_output: Any) -> Any:
    #     """Convert LLM output to dictionary format if it's a Pydantic model"""
    #     if hasattr(llm_output, "model_dump"):
    #         return llm_output.model_dump()
    #     return llm_output

    def _extract_pointers_from_knowledge_paths(self, output_dict: Dict) -> List[str]:
        """Extract node pointers from knowledge paths in the output dictionary"""
        if "knowledge_paths" not in output_dict:
            return []

        node_pointers = []
        for path in output_dict["knowledge_paths"]:
            if isinstance(path, dict):
                path_pointers = self._extract_pointers_from_path(path)
                node_pointers.extend(path_pointers)

        return node_pointers

    def _extract_pointers_from_path(self, path: Dict) -> List[str]:
        """Extract node pointers from a single knowledge path"""
        pointers = []

        for section in ["observation", "cause", "solution"]:
            section_pointers = self._extract_pointers_from_section(path, section)
            pointers.extend(section_pointers)

        return pointers

    def _extract_pointers_from_section(self, path: Dict, section: str) -> List[str]:
        """Extract node pointers from a specific section of a knowledge path"""
        if section not in path or not isinstance(path[section], dict):
            return []

        section_data = path[section]

        # Try to get ID first, then fall back to name
        if "id" in section_data:
            return [f"{section}:{section_data['id']}"]
        elif "name" in section_data:
            return [f"{section}:{section_data['name']}"]

        return []

    def log_extraction_session(
        self,
        session_id: str,
        mechanic_id: Optional[str] = None,
        service_order_number: Optional[str] = None,
        transcription_text: Optional[str] = None,
        door_type: Optional[str] = None,
        llm_output: Any = None,
        llm_output_data_analysis: Any = None,
        admin_summary: Optional[str] = None,
        node_pointers: Optional[List[str]] = None,
        error: Optional[str] = None,
        frontend_extraction_changes: Optional[bool] = None,
    ) -> None:
        """
        Log extraction session data with support for incremental updates.
        Only session_id is required; all other fields are optional and won't overwrite existing data if not provided.

        Args:
            session_id: Session identifier (required)
            mechanic_id: The ID of the mechanic (optional)
            service_order_number: Service order number from frontend (optional)
            transcription_text: The raw transcript text (optional)
            door_type: The door type (string representation) (optional)
            llm_output: The raw output from the LLM (optional)
            llm_output_data_analysis: The Pydantic model response for data analysis (optional)
            admin_summary: Admin summary text (optional)
            node_pointers: List of node IDs created during this operation (optional)
            error: Error message if any occurred (optional)
            frontend_extraction_changes: Whether frontend made changes to extraction (optional)
        """
        timestamp = datetime.utcnow().isoformat()

        # Load existing data
        all_data = self._load_json_file(self.extraction_file)

        # Check if we already have an entry for this session
        existing_entry = all_data.get(session_id, {})

        # Start with existing data
        data = existing_entry.copy()

        # Always update timestamp
        data.update(
            {
                "timestamp": timestamp,
            }
        )

        # Handle node pointers specially - merge with existing
        if node_pointers:
            existing_node_pointers = existing_entry.get("node_pointers", [])
            logger.info(
                f"Adding node pointers to session {session_id}: {node_pointers}"
            )
            # Add new node pointers without duplicates
            for pointer in node_pointers:
                if pointer not in existing_node_pointers:
                    existing_node_pointers.append(pointer)
            data["node_pointers"] = existing_node_pointers

        # Only update these fields if they're explicitly provided
        field_mappings = {
            "mechanic_id": mechanic_id,
            "service_order_number": service_order_number,
            "door_type": door_type,
            "transcription_text": transcription_text,
            "llm_output_paths": (
                self._convert_to_dict(llm_output) if llm_output is not None else None
            ),
            "llm_output_data_analysis": (
                self._convert_to_dict(llm_output_data_analysis)
                if llm_output_data_analysis is not None
                else None
            ),
            "llm_output_admin_summary": admin_summary,
            "error": error,
        }

        for field_name, field_value in field_mappings.items():
            if field_value is not None:
                data[field_name] = field_value

        # track changes in frontend

        if frontend_extraction_changes is not None:
            data["frontend_extraction_changes"] = frontend_extraction_changes

        # Always set pipeline to extraction
        data["pipeline"] = "extraction"

        # Ensure node_pointers exists even if empty
        if "node_pointers" not in data:
            data["node_pointers"] = []

        # Add/update session data
        all_data[session_id] = data

        # Save to file
        self._save_json_file(self.extraction_file, all_data)

        logger.info(f"Logged extraction session {session_id} to {self.extraction_file}")

    def log_query_session(
        self,
        session_id: str,
        mechanic_id: Optional[str] = None,
        service_order_number: Optional[str] = None,
        query_text: Optional[str] = None,
        door_model: Optional[str] = None,
        context_info: Optional[Dict] = None,
        knowledge_needed: Optional[bool] = None,
        extracted_entities: Optional[Dict] = None,
        paths_found: Optional[List[Dict]] = None,
        answer: Optional[str] = None,
        node_pointers: Optional[List[str]] = None,
        error: Optional[str] = None,
    ) -> None:
        """
        Log query session data with support for incremental updates.
        Only session_id is required; all other fields are optional and won't overwrite existing data if not provided.

        Args:
            session_id: Session identifier (required)
            mechanic_id: The ID of the mechanic (optional)
            service_order_number: Service order number from frontend (optional)
            query_text: The original query text (optional)
            door_model: Door model queried (optional)
            context_info: Additional context provided (optional)
            knowledge_needed: Whether knowledge was needed (optional)
            extracted_entities: Entities extracted from the query (optional)
            paths_found: Knowledge paths returned (optional)
            answer: Generated answer (optional)
            node_pointers: Node IDs involved in the query (optional)
            error: Error message if any occurred (optional)
        """
        timestamp = datetime.utcnow().isoformat()

        # Load existing data
        all_data = self._load_json_file(self.query_file)

        # Check if we already have an entry for this session
        existing_entry = all_data.get(session_id, {})

        # Start with existing data
        data = existing_entry.copy()

        # Always update timestamp
        data.update(
            {
                "timestamp": timestamp,
            }
        )

        # Handle node_pointers specially - merge with existing or extract from paths
        if node_pointers is not None:
            existing_node_pointers = existing_entry.get("node_pointers", [])
            # Add new node pointers without duplicates
            for pointer in node_pointers:
                if pointer not in existing_node_pointers:
                    existing_node_pointers.append(pointer)
            data["node_pointers"] = list(
                set(existing_node_pointers)
            )  # Remove duplicates
        elif paths_found is not None and "node_pointers" not in data:
            # Extract node pointers from paths if not explicitly provided
            extracted_pointers = []
            for path in paths_found:
                path_dict = self._convert_to_dict(path)
                if isinstance(path_dict, dict):
                    # Extract node IDs from path structure
                    if "nodes" in path_dict:
                        for node in path_dict["nodes"]:
                            if isinstance(node, dict) and "id" in node:
                                extracted_pointers.append(f"node:{node['id']}")  # TODO
                            elif isinstance(node, dict) and "name" in node:
                                extracted_pointers.append(f"node:{node['name']}")

                    # Also check relationships
                    if "relationships" in path_dict:
                        for rel in path_dict["relationships"]:
                            if isinstance(rel, dict):
                                if "source_id" in rel:
                                    extracted_pointers.append(
                                        f"rel_source:{rel['source_id']}"
                                    )
                                if "target_id" in rel:
                                    extracted_pointers.append(
                                        f"rel_target:{rel['target_id']}"
                                    )

            if extracted_pointers:
                data["node_pointers"] = list(
                    set(extracted_pointers)
                )  # Remove duplicates

        # Only update these fields if they're explicitly provided
        field_mappings = {
            "session_id": session_id,
            "mechanic_id": mechanic_id,
            "service_order_number": service_order_number,
            "query_text": query_text,
            "door_model": door_model,
            "context_info": context_info,
            "knowledge_needed_response": knowledge_needed,
            "answer": answer,
            "error": error,
            "extracted_entities": extracted_entities,
        }

        for field_name, field_value in field_mappings.items():
            if field_value is not None:
                data[field_name] = field_value

        # Handle paths_found specially - convert to dicts if provided
        if paths_found is not None:
            paths_dict = [
                self._convert_knowledge_path_to_dict(path) for path in paths_found
            ]
            data["paths_found"] = paths_dict
            data["paths_count"] = len(paths_dict)

        # Always set pipeline to retrieval
        data["pipeline"] = "retrieval"

        # Ensure node_pointers exists even if empty
        if "node_pointers" not in data:
            data["node_pointers"] = []

        # Add/update session data
        all_data[session_id] = data

        # Save to file
        self._save_json_file(self.query_file, all_data)

        logger.info(f"Logged query session {session_id} to {self.query_file}")

    # Add this new method to the SessionLogger class
    def log_feedback_session(
        self,
        session_id: str,
        thumbs_up: bool,
        comment: Optional[str] = None,
        mechanic_id: Optional[str] = None,
    ) -> None:
        """
        Log feedback session data.

        Args:
            session_id: Session identifier (required)
            thumbs_up: Whether feedback is positive (True) or negative (False)
            comment: Optional feedback comment
            mechanic_id: The ID of the mechanic (optional)
        """
        timestamp = datetime.utcnow().isoformat()

        # Load existing data
        all_data = self._load_json_file(self.feedback_file)

        # Create feedback entry
        data = {
            "session_id": session_id,
            "timestamp": timestamp,
            "thumbs_up": thumbs_up,
            "comment": comment,
            "mechanic_id": mechanic_id,
        }

        # Use timestamp as unique key since session_id might have multiple feedback entries

        # Add feedback data
        all_data[session_id] = data

        # Save to file
        self._save_json_file(self.feedback_file, all_data)

        logger.info(f"Logged feedback for session {session_id} to {self.feedback_file}")

    def get_session_data(self, session_id: str, pipeline_file: str = "both") -> Dict:
        """
        Get session data for analysis for a given session ID and pipeline file

        Args:
            session_id: Session ID to lookup
            pipeline_file: "extraction", "query", or "both"

        Returns:
            Dictionary with session data
        """
        result = {}

        if pipeline_file in ["extraction", "both"]:
            extraction_data = self._load_json_file(self.extraction_file)
            if session_id in extraction_data:
                result["extraction"] = extraction_data[session_id]

        if pipeline_file in ["query", "both"]:
            query_data = self._load_json_file(self.query_file)
            if session_id in query_data:
                result["query"] = query_data[session_id]

        return result


# Global instance for easy import
session_logger = SessionLogger()


# Convenience functions
def log_extraction_session(*args, **kwargs):
    """Convenience function for logging extraction sessions"""
    return session_logger.log_extraction_session(*args, **kwargs)


def log_query_session(*args, **kwargs):
    """Convenience function for logging query sessions"""
    return session_logger.log_query_session(*args, **kwargs)


def log_feedback_session(*args, **kwargs):
    """Convenience function for logging feedback sessions"""
    return session_logger.log_feedback_session(*args, **kwargs)


def get_session_data(session_id: str, pipeline_file: str = "both") -> Dict:
    """Convenience function for getting session data"""
    return session_logger.get_session_data(session_id, pipeline_file)


def set_session_id(session_id: str) -> None:
    """Set the current session ID"""
    session_logger.set_session_id(session_id)
    logger.info(f"Set session ID to {session_id}")


def get_current_session_id() -> str:
    """Get the current session ID or generate a new one if none exists"""
    return session_logger.get_current_session_id()


def detect_and_handle_extraction_changes(
    session_id: str,
    new_data: Dict,
    what_changed: str = "llm_output_paths",
) -> Dict:
    """
    Detect changes in extraction data and return change status with updated data.

    Args:
        session_id: The session identifier
        new_data: New extraction data to compare
        what_changed: What part of the extraction changed (llm_output_paths, transcription_text...)



    Returns:
        dict: A dictionary containing the updated data if changes were detected, otherwise an empty dictionary.
    """
    session_data = get_session_data(session_id, pipeline_file="extraction")

    # Check if extraction data exists in session
    original_data = session_data.get("extraction", {}).get(what_changed, {})

    if not original_data:
        logger.error(f"No original extraction data found. Has new data: {has_changes}")
        return {"NO ORIGINAL EXTRACTION DATA FOUND"}

    else:
        # Compare original with new data
        has_changes = original_data != new_data

        logger.debug(f"Original data: {original_data}")
        logger.debug(f"New extracted data: {new_data}")
        logger.debug(f"Frontend extraction changes detected: {has_changes}")

    if has_changes:
        return new_data

    return {}


def save_audio(
    audio_data: bytes,
    file_format: str,
) -> None:
    """
    Save audio data to file with session id as name.

    Args:
        audio_data: Audio data to save
        file_format: File format of audio data
        output_dir: Output directory for audio files

    """
    # OUTPUT DIR WITH PATHLIB
    output_dir = Path("output/audio_files")
    try:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        session_id = get_current_session_id()

        # save audio data to file with session id as name
        with open(f"{output_dir}/audio_file_{session_id}.{file_format}", "wb") as f:
            f.write(audio_data)

        logger.info(f"Saved audio to {f.name}")
    except Exception as e:
        logger.error(f"Error saving audio: {e}")
