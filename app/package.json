{"name": "app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@openai/realtime-api-beta": "github:openai/openai-realtime-api-beta", "@types/d3": "^7.4.3", "axios": "^1.8.4", "chart.js": "^4.4.9", "d3": "^7.9.0", "lucide-react": "^0.503.0", "next": "15.3.1", "openai": "^4.96.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.1", "typescript": "^5"}}