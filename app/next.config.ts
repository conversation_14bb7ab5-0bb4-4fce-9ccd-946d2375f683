// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: true,
    // Expose environment variables to the client
    env: {
        USE_REAL_GPT: process.env.USE_REAL_GPT || "false",
        OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    },
    // Configure image domains if needed
    images: {
        domains: ["localhost"],
    },
    eslint: {
        ignoreDuringBuilds: true,
    },
    typescript: {
        // Dangerously allows production builds to successfully complete even if
        // your project has type errors.
        ignoreBuildErrors: true,
    },
};

module.exports = nextConfig;
