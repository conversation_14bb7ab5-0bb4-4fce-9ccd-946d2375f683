// components/Dashboard/DashboardComponent.tsx
import React, { useEffect, useState } from "react";
import {
    Box,
    Container,
    Typography,
    Paper,
    Tab,
    Tabs,
    Chip,
    Grid,
    Tooltip,
    IconButton,
    <PERSON>ton,
} from "@mui/material";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import MicIcon from "@mui/icons-material/Mic";
import SearchIcon from "@mui/icons-material/Search";
import DataObjectIcon from "@mui/icons-material/DataObject";
import DownloadIcon from "@mui/icons-material/Download";
import Layout from "../Layout/Layout";
import TranscriptionDisplay from "../Voice/TranscriptionDisplay";
import DoorTypeSelector from "../UI/DoorTypeSelector";
import ResetButton from "../UI/ResetButton";
import QueryProcessor from "../Voice/QueryProcessor";
import AI<PERSON>allMode from "../Voice/AICallMode";
import AI<PERSON>allButton from "../Voice/AICallButton";
import KnowledgeExtractor from "../Knowledge/KnowledgeExtractor";
import ExtractedKnowledgeDisplay from "../Knowledge/ExtractedKnowledgeDisplay";
import { useKnowledge } from "../../contexts/KnowledgeContext";
import audioSessionManager from "../../services/audioSessionManager";
import { useAuth } from "@/contexts/AuthContext";
import LogoutIcon from "@mui/icons-material/Logout";
import PersonIcon from "@mui/icons-material/Person";

const DashboardComponent: React.FC = () => {
    const { user, logout, isDevMode } = useAuth();
    const [tabValue, setTabValue] = useState(0);
    const [pathsSource, setPathsSource] = useState<"extraction" | null>(null);

    const [aiModeEnabled, setAiModeEnabled] = useState(false);
    const [sessionState, setSessionState] = useState(
        audioSessionManager.getState()
    );
    const [hasTranscribed, setHasTranscribed] = useState(false);
    const [isDownloadReady, setIsDownloadReady] = useState(false);

    const {
        loadDoorTypes,
        loadKnowledgeGraph,
        knowledgeGraph,
        queryResult,
        extractedKnowledge,
        uploadMixedAudio,
    } = useKnowledge();

    // Set up audio session manager callbacks
    useEffect(() => {
        audioSessionManager.setCallbacks({
            onStart: () => {
                console.log("Session started callback");
                setSessionState(audioSessionManager.getState());
                setHasTranscribed(false); // Reset on new session
                setIsDownloadReady(false);
            },
            onPause: () => {
                console.log("Session paused callback");
                setSessionState(audioSessionManager.getState());
            },
            onResume: () => {
                console.log("Session resumed callback");
                setSessionState(audioSessionManager.getState());
            },
            onStop: () => {
                console.log("Session stopped callback");
                setSessionState(audioSessionManager.getState());
                // Only auto-transcribe on final stop (not pause)
                if (aiModeEnabled) {
                    handleAutoTranscribe();
                }
            },
            onError: (error) => {
                console.error("Session error:", error);
                setSessionState(audioSessionManager.getState());
            },
            onRecordingStart: () => {
                console.log("Recording started callback");
                setSessionState(audioSessionManager.getState());
            },
            onRecordingStop: () => {
                console.log("Recording stopped callback");
                setSessionState(audioSessionManager.getState());
            },
        });

        // Cleanup on unmount
        return () => {
            audioSessionManager.destroy();
        };
    }, [aiModeEnabled]); // Include aiModeEnabled in dependencies

    // Update AI mode in session manager when toggled
    useEffect(() => {
        audioSessionManager.setAiMode(aiModeEnabled);
    }, [aiModeEnabled]);

    // Handle automatic transcription after session fully ends
    const handleAutoTranscribe = async () => {
        if (hasTranscribed) {
            console.log("Already transcribed, skipping");
            return;
        }

        console.log(
            "Starting automatic transcription of concatenated recording..."
        );
        setHasTranscribed(true);

        try {
            // Get the concatenated recording blob
            const concatenatedBlob =
                await audioSessionManager.getConcatenatedRecordingBlob();

            if (!concatenatedBlob) {
                console.log("No concatenated recording available");
                return;
            }

            console.log(
                `Concatenated blob size: ${concatenatedBlob.size} bytes`
            );

            // Create a file from the concatenated blob
            const timestamp = new Date()
                .toISOString()
                .replace(/[:.]/g, "-")
                .slice(0, -5);
            const filename = `mixed-recording-concatenated-${timestamp}.wav`;
            const mixedFile = new File([concatenatedBlob], filename, {
                type: "audio/wav",
            });

            // Upload using the mixed audio upload method in KnowledgeContext
            // We need to temporarily replace the getCurrentRecordingBlob to return our concatenated blob
            const originalGetBlob =
                audioSessionManager.getCurrentRecordingBlob;
            audioSessionManager.getCurrentRecordingBlob = () =>
                concatenatedBlob;

            await uploadMixedAudio();

            // Restore original method
            audioSessionManager.getCurrentRecordingBlob = originalGetBlob;

            setIsDownloadReady(true);
            console.log("Automatic transcription completed successfully");
        } catch (error) {
            console.error("Automatic transcription failed:", error);
            setHasTranscribed(false); // Reset if it failed
        }
    };

    const handleCallStart = async () => {
        await audioSessionManager.startSession(localStorage.getItem("token"));
    };

    const handleCallEnd = async () => {
        await audioSessionManager.stopSession();
    };

    // Enhanced download function for concatenated recordings
    const handleDownloadRecording = async () => {
        const timestamp = new Date()
            .toISOString()
            .replace(/[:.]/g, "-")
            .slice(0, -5);
        const filename = `conversation-concatenated-${timestamp}`;

        await audioSessionManager.saveRecording(filename);
        console.log("Concatenated recording downloaded");
    };

    useEffect(() => {
        // Load door types and knowledge graph when component mounts
        loadDoorTypes();
        loadKnowledgeGraph();
    }, [loadDoorTypes, loadKnowledgeGraph]);

    // When extractedKnowledge changes, update paths source to 'extraction'
    useEffect(() => {
        if (
            extractedKnowledge &&
            extractedKnowledge.paths &&
            extractedKnowledge.paths.length > 0
        ) {
            setPathsSource("extraction");
        }
    }, [extractedKnowledge]);

    const handleTabChange = (
        event: React.SyntheticEvent,
        newValue: number
    ) => {
        setTabValue(newValue);
    };

    // Get extraction paths only
    const getExtractionPaths = () => {
        if (
            extractedKnowledge &&
            extractedKnowledge.paths &&
            extractedKnowledge.paths.length > 0
        ) {
            return extractedKnowledge.paths;
        }
        return [];
    };

    const extractionPaths = getExtractionPaths();

    return (
        <Layout>
            {/* Header Banner */}
            <Box
                sx={{
                    backgroundColor: "primary.main",
                    color: "white",
                    py: 3,
                }}
            >
                <Container maxWidth="lg">
                    <Grid
                        container
                        alignItems="center"
                        justifyContent="space-between"
                    >
                        <Grid
                            item
                            xs={12}
                            md={6}
                        >
                            <Typography
                                variant="h4"
                                component="h1"
                                sx={{ fontWeight: 500 }}
                            >
                                Boon Brain
                            </Typography>
                            <Typography
                                variant="body1"
                                sx={{ mt: 1, opacity: 0.9 }}
                            >
                                Capturing and sharing tacit knowledge
                            </Typography>
                        </Grid>

                        <Grid
                            item
                            xs={12}
                            md={6}
                            sx={{
                                mt: { xs: 2, md: 0 },
                                textAlign: { xs: "left", md: "right" },
                                display: "flex",
                                flexDirection: { xs: "column", md: "row" },
                                alignItems: { xs: "flex-start", md: "center" },
                                justifyContent: {
                                    xs: "flex-start",
                                    md: "flex-end",
                                },
                                gap: 1,
                            }}
                        >
                            {/* User Info */}
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                    mb: { xs: 1, md: 0 },
                                }}
                            >
                                <PersonIcon
                                    sx={{ fontSize: 16, opacity: 0.8 }}
                                />
                                <Typography
                                    variant="body2"
                                    sx={{ opacity: 0.9 }}
                                >
                                    {user?.name || "Unknown User"}
                                </Typography>
                                {isDevMode && (
                                    <Chip
                                        label="Dev Mode"
                                        size="small"
                                        sx={{
                                            backgroundColor:
                                                "rgba(255, 193, 7, 0.2)",
                                            color: "rgba(255, 193, 7, 1)",
                                            fontWeight: 500,
                                            fontSize: "0.7rem",
                                        }}
                                    />
                                )}
                            </Box>

                            {/* Action Buttons */}
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 1,
                                }}
                            >
                                <Chip
                                    label="Demo Version"
                                    size="small"
                                    sx={{
                                        backgroundColor:
                                            "rgba(255, 255, 255, 0.2)",
                                        color: "white",
                                        fontWeight: 500,
                                    }}
                                />

                                <Tooltip title="Logout">
                                    <IconButton
                                        size="small"
                                        sx={{
                                            color: "white",
                                            backgroundColor:
                                                "rgba(255, 255, 255, 0.1)",
                                            "&:hover": {
                                                backgroundColor:
                                                    "rgba(255, 255, 255, 0.2)",
                                            },
                                        }}
                                        onClick={logout}
                                    >
                                        <LogoutIcon fontSize="small" />
                                    </IconButton>
                                </Tooltip>

                                <Tooltip title="Reset the demo to initial state">
                                    <IconButton
                                        size="small"
                                        sx={{
                                            color: "white",
                                            backgroundColor:
                                                "rgba(255, 255, 255, 0.1)",
                                            "&:hover": {
                                                backgroundColor:
                                                    "rgba(255, 255, 255, 0.2)",
                                            },
                                        }}
                                        onClick={() => {
                                            // Call the reset function from ResetButton
                                            const resetBtn =
                                                document.getElementById(
                                                    "reset-demo-btn"
                                                );
                                            if (resetBtn) {
                                                (
                                                    resetBtn as HTMLButtonElement
                                                ).click();
                                            }
                                        }}
                                    >
                                        <RestartAltIcon fontSize="small" />
                                    </IconButton>
                                </Tooltip>
                            </Box>
                        </Grid>
                    </Grid>
                </Container>
            </Box>

            <Box
                sx={{
                    backgroundColor: "#f8fafc",
                    py: 4,
                    minHeight: "calc(100vh - 64px - 72px)",
                }}
            >
                <Container>
                    {/* Top Row: Voice Input (Left) and Knowledge Query (Right) */}
                    <Box
                        flex={1}
                        sx={{
                            display: "flex",
                            flexDirection: "row",
                            mb: 3,
                        }}
                    >
                        {/* Voice Input Panel */}
                        <Box
                            flex={1}
                            width={1 / 2}
                            sx={{ mr: 2 }}
                        >
                            <Paper
                                sx={{
                                    p: 0,
                                    height: "100%",
                                    overflow: "hidden",
                                    border: "1px solid rgba(0, 0, 0, 0.06)",
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                }}
                                elevation={0}
                            >
                                <Box
                                    sx={{
                                        p: 2,
                                        borderBottom:
                                            "1px solid rgba(0, 0, 0, 0.06)",
                                        display: "flex",
                                        alignItems: "center",
                                        backgroundColor:
                                            "rgba(0, 132, 61, 0.03)",
                                    }}
                                >
                                    <MicIcon
                                        sx={{ color: "primary.main", mr: 1.5 }}
                                    />
                                    <Typography
                                        variant="subtitle1"
                                        sx={{ fontWeight: 500 }}
                                    >
                                        Voice Input
                                    </Typography>
                                    {sessionState.isRecording && (
                                        <Box
                                            sx={{
                                                ml: 2,
                                                display: "flex",
                                                alignItems: "center",
                                                gap: 1,
                                            }}
                                        >
                                            <Chip
                                                label={`${audioSessionManager.formatDuration(
                                                    sessionState.recordingDuration
                                                )}`}
                                                size="small"
                                                color="error"
                                                sx={{
                                                    fontFamily: "monospace",
                                                }}
                                            />
                                            {sessionState.isPaused ? (
                                                <Chip
                                                    label="Paused"
                                                    size="small"
                                                    sx={{
                                                        backgroundColor:
                                                            "rgba(255, 193, 7, 0.1)",
                                                        color: "warning.main",
                                                        fontWeight: 500,
                                                    }}
                                                />
                                            ) : (
                                                <Chip
                                                    label="Full Conversation"
                                                    size="small"
                                                    sx={{
                                                        backgroundColor:
                                                            "rgba(0, 132, 61, 0.1)",
                                                        color: "primary.main",
                                                        fontWeight: 500,
                                                    }}
                                                />
                                            )}
                                        </Box>
                                    )}
                                    <Tooltip title="Record audio to extract knowledge or ask questions. AI Call Mode captures both your voice and AI responses for full conversation transcription.">
                                        <IconButton
                                            size="small"
                                            sx={{ ml: "auto" }}
                                        >
                                            <HelpOutlineIcon fontSize="small" />
                                        </IconButton>
                                    </Tooltip>
                                </Box>

                                <Box
                                    sx={{
                                        p: 2,
                                        flexGrow: 1,
                                        display: "flex",
                                        flexDirection: "column",
                                    }}
                                >
                                    <Box sx={{ mb: 2 }}>
                                        <Typography
                                            variant="body2"
                                            color="text.secondary"
                                            sx={{ mb: 1 }}
                                        >
                                            Select door model:
                                        </Typography>
                                        <DoorTypeSelector />
                                    </Box>

                                    {/* AI Call Mode Toggle */}
                                    <Box sx={{ mb: 3 }}>
                                        <AICallMode
                                            onToggle={setAiModeEnabled}
                                        />
                                    </Box>

                                    <Box
                                        sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            mb: 2,
                                        }}
                                    >
                                        {/* Enhanced call/record button that changes based on mode */}
                                        <AICallButton
                                            aiModeEnabled={aiModeEnabled}
                                            onCallStart={handleCallStart}
                                            onCallEnd={handleCallEnd}
                                        />

                                        <Box sx={{ flexGrow: 1, ml: 2 }}>
                                            {/* Enhanced transcription display that shows waves during calls */}
                                            <TranscriptionDisplay
                                                aiModeEnabled={aiModeEnabled}
                                                isCallActive={
                                                    sessionState.isActive
                                                }
                                                callCompleted={
                                                    !sessionState.isActive &&
                                                    hasTranscribed
                                                }
                                            />
                                        </Box>
                                    </Box>

                                    {/* Enhanced Download Button (shown after call completed and transcribed) */}
                                    {!sessionState.isActive &&
                                        hasTranscribed &&
                                        isDownloadReady && (
                                            <Box sx={{ mb: 2 }}>
                                                <Typography
                                                    variant="body2"
                                                    color="text.secondary"
                                                    sx={{ mb: 1 }}
                                                >
                                                    Full conversation recorded,
                                                    concatenated with silence
                                                    gaps, and transcribed
                                                    automatically
                                                </Typography>
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        gap: 1,
                                                    }}
                                                >
                                                    <Button
                                                        variant="outlined"
                                                        startIcon={
                                                            <DownloadIcon />
                                                        }
                                                        onClick={
                                                            handleDownloadRecording
                                                        }
                                                        size="small"
                                                    >
                                                        Download Full
                                                        Conversation
                                                    </Button>
                                                    <Chip
                                                        label="WAV Format"
                                                        size="small"
                                                        variant="outlined"
                                                        sx={{
                                                            alignSelf:
                                                                "center",
                                                            fontFamily:
                                                                "monospace",
                                                            fontSize: "0.7rem",
                                                        }}
                                                    />
                                                </Box>
                                            </Box>
                                        )}

                                    {/* Hidden reset button for function calling */}
                                    <Box sx={{ display: "none" }}>
                                        <ResetButton id="reset-demo-btn" />
                                    </Box>
                                </Box>
                            </Paper>
                        </Box>

                        {/* Knowledge Query Panel */}
                        <Box
                            flex={1}
                            width={1 / 2}
                        >
                            <Paper
                                sx={{
                                    p: 0,
                                    height: "100%",
                                    overflow: "hidden",
                                    border: "1px solid rgba(0, 0, 0, 0.06)",
                                    display: "flex",
                                    flexDirection: "column",
                                }}
                                elevation={0}
                            >
                                <Box
                                    sx={{
                                        p: 2,
                                        borderBottom:
                                            "1px solid rgba(0, 0, 0, 0.06)",
                                        display: "flex",
                                        alignItems: "center",
                                        backgroundColor:
                                            "rgba(0, 132, 61, 0.03)",
                                    }}
                                >
                                    <SearchIcon
                                        sx={{ color: "primary.main", mr: 1.5 }}
                                    />
                                    <Typography
                                        variant="subtitle1"
                                        sx={{ fontWeight: 500 }}
                                    >
                                        Retrieve Knowledge
                                    </Typography>
                                    <Tooltip title="Search the knowledge base for revolving door information">
                                        <IconButton
                                            size="small"
                                            sx={{ ml: "auto" }}
                                        >
                                            <HelpOutlineIcon fontSize="small" />
                                        </IconButton>
                                    </Tooltip>
                                </Box>

                                <Box
                                    sx={{
                                        p: 2,
                                        flexGrow: 1,
                                        display: "flex",
                                        flexDirection: "column",
                                    }}
                                >
                                    <QueryProcessor />
                                </Box>
                            </Paper>
                        </Box>
                    </Box>

                    {/* Middle Row: Manual Knowledge Entry (Full Width) */}
                    <Box flex={1}>
                        <Grid item>
                            <Paper
                                sx={{
                                    p: 0,
                                    overflow: "hidden",
                                    border: "1px solid rgba(0, 0, 0, 0.06)",
                                    display: "flex",
                                    flexDirection: "column",
                                }}
                                elevation={0}
                                id="knowledge-extractor"
                            >
                                <Box
                                    sx={{
                                        p: 2,
                                        borderBottom:
                                            "1px solid rgba(0, 0, 0, 0.06)",
                                        display: "flex",
                                        alignItems: "center",
                                        backgroundColor:
                                            "rgba(0, 132, 61, 0.03)",
                                    }}
                                >
                                    <DataObjectIcon
                                        sx={{ color: "primary.main", mr: 1.5 }}
                                    />
                                    <Typography
                                        variant="subtitle1"
                                        sx={{ fontWeight: 500 }}
                                    >
                                        Manual Knowledge Entry
                                    </Typography>
                                    <Tooltip title="Enter technical information manually to extract knowledge">
                                        <IconButton
                                            size="small"
                                            sx={{ ml: "auto" }}
                                        >
                                            <HelpOutlineIcon fontSize="small" />
                                        </IconButton>
                                    </Tooltip>
                                </Box>

                                <Box sx={{ p: 2 }}>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{ mb: 2 }}
                                    >
                                        Enter technical information or
                                        maintenance reports to extract
                                        knowledge
                                    </Typography>
                                    <KnowledgeExtractor compact={true} />
                                </Box>
                            </Paper>
                        </Grid>
                    </Box>

                    {/* Path Visualizer (Full Width) */}
                    <ExtractedKnowledgeDisplay />
                </Container>
            </Box>
        </Layout>
    );
};

export default DashboardComponent;
