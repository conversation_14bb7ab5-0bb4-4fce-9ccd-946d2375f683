// components/Database/KeyMetrics.tsx
import React from "react";
import {
    Box,
    Grid,
    Typography,
    LinearProgress,
    Card,
    CardContent,
    Button,
} from "@mui/material";
import { useKnowledge } from "../../contexts/KnowledgeContext";
import HubIcon from "@mui/icons-material/Hub";
import ConnectWithoutContactIcon from "@mui/icons-material/ConnectWithoutContact";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import BuildIcon from "@mui/icons-material/Build";
import StorageIcon from "@mui/icons-material/Storage";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import { Doughnut } from "react-chartjs-2";
import {
    Chart as ChartJS,
    ArcElement,
    Tooltip as ChartTooltip,
    Legend as ChartLegend,
    CategoryScale,
    LinearScale,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
    ArcElement,
    ChartTooltip,
    ChartLegend,
    CategoryScale,
    LinearScale
);

interface MetricCardProps {
    title: string;
    value: number | string;
    description: string;
    icon: React.ReactNode;
    color: string;
    lightColor: string;
    progress?: number;
    total?: number;
}

const MetricCard: React.FC<MetricCardProps> = ({
    title,
    value,
    description,
    icon,
    color,
    lightColor,
    progress,
    total,
}) => (
    <Card
        sx={{
            height: "100%",
            display: "flex",
            flexDirection: "column",
            transition: "all 0.2s ease-in-out",
            border: "1px solid rgba(0, 0, 0, 0.06)",
            borderLeft: `4px solid ${color}`,
            "&:hover": {
                boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.08)",
                transform: "translateY(-2px)",
            },
        }}
        elevation={0}
    >
        <CardContent
            sx={{ p: 2.5, flex: 1, display: "flex", flexDirection: "column" }}
        >
            <Box
                sx={{
                    display: "flex",
                    alignItems: "flex-start",
                    justifyContent: "space-between",
                    mb: 2,
                }}
            >
                <Box>
                    <Typography
                        variant="subtitle2"
                        sx={{
                            fontWeight: 500,
                            color: "text.secondary",
                            fontSize: "0.875rem",
                        }}
                    >
                        {title}
                    </Typography>
                    <Typography
                        variant="h4"
                        sx={{
                            fontWeight: 600,
                            color,
                            mt: 0.5,
                        }}
                    >
                        {value}
                    </Typography>
                </Box>
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        p: 1,
                        borderRadius: "50%",
                        backgroundColor: lightColor,
                        color,
                    }}
                >
                    {icon}
                </Box>
            </Box>

            {progress !== undefined && total !== undefined && (
                <Box sx={{ mb: 2 }}>
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            mb: 0.5,
                        }}
                    >
                        <Typography
                            variant="caption"
                            color="text.secondary"
                        >
                            Progress
                        </Typography>
                        <Typography
                            variant="caption"
                            color="text.secondary"
                            fontWeight={500}
                        >
                            {progress}/{total}
                        </Typography>
                    </Box>
                    <LinearProgress
                        variant="determinate"
                        value={(progress / total) * 100}
                        sx={{
                            height: 6,
                            borderRadius: 3,
                            backgroundColor: `${lightColor}80`,
                            "& .MuiLinearProgress-bar": {
                                backgroundColor: color,
                            },
                        }}
                    />
                </Box>
            )}

            <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mt: "auto" }}
            >
                {description}
            </Typography>
        </CardContent>
    </Card>
);

const KeyMetrics: React.FC = () => {
    const { extractedKnowledge } = useKnowledge();

    // Calculate metrics
    const totalNodes = extractedKnowledge
        ? extractedKnowledge.nodes.length
        : 0;
    const totalRelationships = extractedKnowledge
        ? extractedKnowledge.relationships.length
        : 0;

    const symptomCount = extractedKnowledge
        ? extractedKnowledge.nodes.filter(
              (node: any) => node.type === "SYMPTOM"
          ).length
        : 0;

    const problemCount = extractedKnowledge
        ? extractedKnowledge.nodes.filter(
              (node: any) => node.type === "PROBLEM"
          ).length
        : 0;

    const causeCount = extractedKnowledge
        ? extractedKnowledge.nodes.filter((node: any) => node.type === "CAUSE")
              .length
        : 0;

    const solutionCount = extractedKnowledge
        ? extractedKnowledge.nodes.filter(
              (node: any) => node.type === "SOLUTION"
          ).length
        : 0;

    // Prepare chart data for the knowledge distribution
    const doughnutData = {
        labels: ["Symptoms", "Problems", "Causes", "Solutions"],
        datasets: [
            {
                data: [symptomCount, problemCount, causeCount, solutionCount],
                backgroundColor: [
                    "#3b82f6", // Blue for Symptoms
                    "#8b5cf6", // Purple for Problems
                    "#f97316", // Orange for Causes
                    "#00843D", // Green for Solutions
                ],
                borderWidth: 1,
                borderColor: "#ffffff",
            },
        ],
    };

    const doughnutOptions = {
        responsive: true,
        maintainAspectRatio: false,
        cutout: "70%",
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                callbacks: {
                    label: function (context: any) {
                        const label = context.label || "";
                        const value = context.raw || 0;
                        const total = context.dataset.data.reduce(
                            (a: number, b: number) => a + b,
                            0
                        );
                        const percentage =
                            total > 0 ? Math.round((value / total) * 100) : 0;
                        return `${label}: ${value} (${percentage}%)`;
                    },
                },
            },
        },
    };

    // Calculate additional metrics
    const relationshipsPerNode =
        totalNodes > 0 ? (totalRelationships / totalNodes).toFixed(1) : "0";

    // Target values (for demo purposes)
    const targetNodes = 50;
    const targetRelationships = 100;

    return (
        <Box>
            {!extractedKnowledge && (
                <Box
                    sx={{
                        py: 6,
                        textAlign: "center",
                        borderRadius: 2,
                        backgroundColor: "#f8fafc",
                    }}
                >
                    <StorageIcon
                        sx={{ fontSize: 40, color: "text.disabled", mb: 2 }}
                    />
                    <Typography
                        variant="h6"
                        color="text.primary"
                        gutterBottom
                    >
                        No Metrics Available
                    </Typography>
                    <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ mb: 3, maxWidth: 450, mx: "auto" }}
                    >
                        Record a description or add knowledge about revolving
                        doors to see knowledge metrics.
                    </Typography>
                    <Button
                        variant="outlined"
                        color="primary"
                        startIcon={<TrendingUpIcon />}
                        onClick={() => {
                            // Scroll to knowledge extractor
                            const extractor = document.getElementById(
                                "knowledge-extractor"
                            );
                            if (extractor) {
                                extractor.scrollIntoView({
                                    behavior: "smooth",
                                });
                            }
                        }}
                    >
                        Add Knowledge
                    </Button>
                </Box>
            )}

            {extractedKnowledge && (
                <>
                    <Grid
                        container
                        spacing={3}
                    >
                        {/* Main metrics */}
                        <Grid
                            item
                            xs={12}
                            md={8}
                        >
                            <Grid
                                container
                                spacing={3}
                            >
                                <Grid
                                    item
                                    xs={12}
                                    sm={6}
                                >
                                    <MetricCard
                                        title="Knowledge Items"
                                        value={totalNodes}
                                        description="Total knowledge nodes in the database"
                                        icon={<HubIcon />}
                                        color="#1A73E8"
                                        lightColor="rgba(26, 115, 232, 0.1)"
                                        progress={totalNodes}
                                        total={targetNodes}
                                    />
                                </Grid>
                                <Grid
                                    item
                                    xs={12}
                                    sm={6}
                                >
                                    <MetricCard
                                        title="Relationships"
                                        value={totalRelationships}
                                        description="Connections between knowledge items"
                                        icon={<ConnectWithoutContactIcon />}
                                        color="#9333EA"
                                        lightColor="rgba(147, 51, 234, 0.1)"
                                        progress={totalRelationships}
                                        total={targetRelationships}
                                    />
                                </Grid>

                                <Grid
                                    item
                                    xs={12}
                                    sm={6}
                                >
                                    <MetricCard
                                        title="Symptoms Identified"
                                        value={symptomCount}
                                        description="Unique symptoms documented"
                                        icon={<ErrorOutlineIcon />}
                                        color="#3b82f6"
                                        lightColor="rgba(59, 130, 246, 0.1)"
                                    />
                                </Grid>
                                <Grid
                                    item
                                    xs={12}
                                    sm={6}
                                >
                                    <MetricCard
                                        title="Solutions Available"
                                        value={solutionCount}
                                        description="Unique solutions in the knowledge base"
                                        icon={<CheckCircleOutlineIcon />}
                                        color="#00843D"
                                        lightColor="rgba(0, 132, 61, 0.1)"
                                    />
                                </Grid>
                            </Grid>
                        </Grid>

                        {/* Chart */}
                        <Grid
                            item
                            xs={12}
                            md={4}
                        >
                            <Card
                                sx={{
                                    height: "100%",
                                    display: "flex",
                                    flexDirection: "column",
                                    border: "1px solid rgba(0, 0, 0, 0.06)",
                                }}
                                elevation={0}
                            >
                                <CardContent
                                    sx={{
                                        p: 2,
                                        pb: "16px !important",
                                        display: "flex",
                                        flexDirection: "column",
                                        height: "100%",
                                    }}
                                >
                                    <Typography
                                        variant="subtitle2"
                                        sx={{ mb: 1, fontWeight: 500 }}
                                    >
                                        Knowledge Distribution
                                    </Typography>

                                    {totalNodes > 0 ? (
                                        <Box
                                            sx={{
                                                position: "relative",
                                                height: 200,
                                                mt: 1,
                                            }}
                                        >
                                            <Doughnut
                                                data={doughnutData}
                                                options={doughnutOptions}
                                            />
                                            <Box
                                                sx={{
                                                    position: "absolute",
                                                    top: "50%",
                                                    left: "50%",
                                                    transform:
                                                        "translate(-50%, -50%)",
                                                    textAlign: "center",
                                                }}
                                            >
                                                <Typography
                                                    variant="h5"
                                                    sx={{
                                                        fontWeight: 600,
                                                        color: "text.primary",
                                                    }}
                                                >
                                                    {totalNodes}
                                                </Typography>
                                                <Typography
                                                    variant="caption"
                                                    color="text.secondary"
                                                >
                                                    Total Items
                                                </Typography>
                                            </Box>
                                        </Box>
                                    ) : (
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                                height: 200,
                                                bgcolor: "#f8fafc",
                                                borderRadius: 1,
                                            }}
                                        >
                                            <Typography
                                                variant="body2"
                                                color="text.secondary"
                                            >
                                                No data available
                                            </Typography>
                                        </Box>
                                    )}

                                    <Box sx={{ mt: 2 }}>
                                        <Grid
                                            container
                                            spacing={1}
                                        >
                                            <Grid
                                                item
                                                xs={6}
                                            >
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                        mb: 1,
                                                    }}
                                                >
                                                    <Box
                                                        sx={{
                                                            width: 12,
                                                            height: 12,
                                                            borderRadius:
                                                                "50%",
                                                            bgcolor: "#3b82f6",
                                                            mr: 1,
                                                        }}
                                                    />
                                                    <Typography
                                                        variant="caption"
                                                        color="text.secondary"
                                                    >
                                                        Symptoms
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                            <Grid
                                                item
                                                xs={6}
                                            >
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                        mb: 1,
                                                    }}
                                                >
                                                    <Box
                                                        sx={{
                                                            width: 12,
                                                            height: 12,
                                                            borderRadius:
                                                                "50%",
                                                            bgcolor: "#8b5cf6",
                                                            mr: 1,
                                                        }}
                                                    />
                                                    <Typography
                                                        variant="caption"
                                                        color="text.secondary"
                                                    >
                                                        Problems
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                            <Grid
                                                item
                                                xs={6}
                                            >
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                    }}
                                                >
                                                    <Box
                                                        sx={{
                                                            width: 12,
                                                            height: 12,
                                                            borderRadius:
                                                                "50%",
                                                            bgcolor: "#f97316",
                                                            mr: 1,
                                                        }}
                                                    />
                                                    <Typography
                                                        variant="caption"
                                                        color="text.secondary"
                                                    >
                                                        Causes
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                            <Grid
                                                item
                                                xs={6}
                                            >
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                    }}
                                                >
                                                    <Box
                                                        sx={{
                                                            width: 12,
                                                            height: 12,
                                                            borderRadius:
                                                                "50%",
                                                            bgcolor: "#00843D",
                                                            mr: 1,
                                                        }}
                                                    />
                                                    <Typography
                                                        variant="caption"
                                                        color="text.secondary"
                                                    >
                                                        Solutions
                                                    </Typography>
                                                </Box>
                                            </Grid>
                                        </Grid>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>

                    {/* Additional metrics */}
                    <Grid
                        container
                        spacing={3}
                        sx={{ mt: 0.5 }}
                    >
                        <Grid
                            item
                            xs={12}
                            sm={6}
                            md={3}
                        >
                            <MetricCard
                                title="Problems Identified"
                                value={problemCount}
                                description="Identified issues requiring solutions"
                                icon={<WarningAmberIcon />}
                                color="#8b5cf6"
                                lightColor="rgba(139, 92, 246, 0.1)"
                            />
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            sm={6}
                            md={3}
                        >
                            <MetricCard
                                title="Causes Documented"
                                value={causeCount}
                                description="Root causes of problems identified"
                                icon={<BuildIcon />}
                                color="#f97316"
                                lightColor="rgba(249, 115, 22, 0.1)"
                            />
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            sm={6}
                            md={3}
                        >
                            <MetricCard
                                title="Connections Per Item"
                                value={relationshipsPerNode}
                                description="Average connections per knowledge item"
                                icon={<ConnectWithoutContactIcon />}
                                color="#06B6D4"
                                lightColor="rgba(6, 182, 212, 0.1)"
                            />
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            sm={6}
                            md={3}
                        >
                            <MetricCard
                                title="Knowledge Completion"
                                value={`${Math.round(
                                    (totalNodes / targetNodes) * 100
                                )}%`}
                                description="Progress towards knowledge target"
                                icon={<TrendingUpIcon />}
                                color="#00843D"
                                lightColor="rgba(0, 132, 61, 0.1)"
                            />
                        </Grid>
                    </Grid>
                </>
            )}
        </Box>
    );
};

export default KeyMetrics;
