// components/Database/CommonIssues.tsx
import React, { useState } from "react";
import {
    Box,
    Typography,
    Grid,
    Paper,
    Card,
    CardContent,
    Chip,
    Tooltip,
    IconButton,
    Button,
    Menu,
    MenuItem,
    ListItemIcon,
    ListItemText,
} from "@mui/material";
import { useKnowledge } from "../../contexts/KnowledgeContext";
import { Bar } from "react-chartjs-2";
import BarChartIcon from "@mui/icons-material/BarChart";
import ViewListIcon from "@mui/icons-material/ViewList";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import SortIcon from "@mui/icons-material/Sort";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import ReportProblemIcon from "@mui/icons-material/ReportProblem";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import SettingsIcon from "@mui/icons-material/Settings";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip as ChartTooltip,
    Legend,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    ChartTooltip,
    Legend
);

const CommonIssues: React.FC = () => {
    const { extractedKnowledge } = useKnowledge();
    const [viewMode, setViewMode] = useState<"chart" | "list">("chart");
    const [sortBy, setSortBy] = useState<"frequency" | "alphabetical">(
        "frequency"
    );
    const [sortMenuAnchor, setSortMenuAnchor] = useState<null | HTMLElement>(
        null
    );
    const [issueMenuAnchor, setIssueMenuAnchor] = useState<{
        [key: string]: HTMLElement | null;
    }>({});

    // Prepare data
    const symptoms = extractedKnowledge
        ? extractedKnowledge.nodes.filter(
              (node: any) => node.type === "SYMPTOM"
          )
        : [];

    // Create a more realistic distribution for demo purposes
    const generateRealisticData = (count: number) => {
        // Zipf-like distribution for more realistic frequency data
        return Array(count)
            .fill(0)
            .map((_, i) => {
                // Adding some randomness but preserving the general trend that common issues are more frequent
                return Math.floor(10 / (i / 3 + 1) + Math.random() * 3);
            });
    };

    const frequencies =
        symptoms.length > 0 ? generateRealisticData(symptoms.length) : [];

    const sortedSymptoms = [...symptoms].sort((a, b) => {
        if (sortBy === "frequency") {
            const indexA = symptoms.findIndex((s) => s.id === a.id);
            const indexB = symptoms.findIndex((s) => s.id === b.id);
            return frequencies[indexB] - frequencies[indexA];
        } else {
            return a.name.localeCompare(b.name);
        }
    });

    // Generate severity levels for demo purposes
    const getSeverityLevel = (frequency: number) => {
        if (frequency > 7) return { level: "High", color: "#ef4444" };
        if (frequency > 4) return { level: "Medium", color: "#f97316" };
        return { level: "Low", color: "#3b82f6" };
    };

    // Chart data
    const chartData = {
        labels: sortedSymptoms.map((s: any) =>
            s.name.length > 20 ? s.name.substring(0, 18) + "..." : s.name
        ),
        datasets: [
            {
                label: "Occurrence Count",
                data: sortedSymptoms.map((s: any) => {
                    const index = symptoms.findIndex((sym) => sym.id === s.id);
                    return frequencies[index] || 0;
                }),
                backgroundColor: sortedSymptoms.map((s: any) => {
                    const index = symptoms.findIndex((sym) => sym.id === s.id);
                    return getSeverityLevel(frequencies[index] || 0).color;
                }),
                borderRadius: 4,
                maxBarThickness: 40,
            },
        ],
    };

    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                callbacks: {
                    label: function (context: any) {
                        return `Occurrences: ${context.raw}`;
                    },
                    title: function (context: any) {
                        const index = context[0].dataIndex;
                        return sortedSymptoms[index]?.name || "";
                    },
                    afterLabel: function (context: any) {
                        const index = context.dataIndex;
                        const symptom = sortedSymptoms[index];
                        return symptom?.description
                            ? `Description: ${symptom.description}`
                            : "";
                    },
                },
            },
        },
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: "Occurrence Frequency",
                    font: {
                        size: 12,
                    },
                    color: "#64748b",
                },
            },
            x: {
                ticks: {
                    maxRotation: 45,
                    minRotation: 45,
                },
            },
        },
    };

    const handleSortMenuOpen = (
        event: React.MouseEvent<HTMLButtonElement>
    ) => {
        setSortMenuAnchor(event.currentTarget);
    };

    const handleSortMenuClose = () => {
        setSortMenuAnchor(null);
    };

    const handleSortChange = (sortType: "frequency" | "alphabetical") => {
        setSortBy(sortType);
        handleSortMenuClose();
    };

    const handleIssueMenuOpen = (
        event: React.MouseEvent<HTMLButtonElement>,
        id: string
    ) => {
        setIssueMenuAnchor({
            ...issueMenuAnchor,
            [id]: event.currentTarget,
        });
    };

    const handleIssueMenuClose = (id: string) => {
        setIssueMenuAnchor({
            ...issueMenuAnchor,
            [id]: null,
        });
    };

    // Empty state
    if (!extractedKnowledge || symptoms.length === 0) {
        return (
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    py: 6,
                    textAlign: "center",
                    backgroundColor: "#f8fafc",
                    borderRadius: 1,
                }}
            >
                <ReportProblemIcon
                    sx={{ fontSize: 40, color: "text.disabled", mb: 2 }}
                />
                <Typography
                    variant="h6"
                    color="text.primary"
                    gutterBottom
                >
                    No Issues Data Available
                </Typography>
                <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ maxWidth: 450, mb: 3 }}
                >
                    Record a description or add knowledge about revolving door
                    issues to see common symptoms and their frequency analysis.
                </Typography>
                <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<ErrorOutlineIcon />}
                    onClick={() => {
                        // Scroll to knowledge extractor
                        const extractor = document.getElementById(
                            "knowledge-extractor"
                        );
                        if (extractor) {
                            extractor.scrollIntoView({ behavior: "smooth" });
                        }
                    }}
                >
                    Add Knowledge
                </Button>
            </Box>
        );
    }

    return (
        <Box>
            {/* Controls */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 2,
                    flexWrap: "wrap",
                    gap: 1,
                }}
            >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                        variant="subtitle1"
                        sx={{ fontWeight: 500, mr: 2 }}
                    >
                        {sortedSymptoms.length} Common Issues
                    </Typography>

                    <Box
                        sx={{
                            display: "flex",
                            bgcolor: "#f8fafc",
                            borderRadius: 1,
                            overflow: "hidden",
                        }}
                    >
                        <Tooltip title="Chart View">
                            <IconButton
                                size="small"
                                onClick={() => setViewMode("chart")}
                                sx={{
                                    borderRadius: 0,
                                    bgcolor:
                                        viewMode === "chart"
                                            ? "rgba(0, 132, 61, 0.08)"
                                            : "transparent",
                                    color:
                                        viewMode === "chart"
                                            ? "primary.main"
                                            : "text.secondary",
                                }}
                            >
                                <BarChartIcon fontSize="small" />
                            </IconButton>
                        </Tooltip>
                        <Tooltip title="List View">
                            <IconButton
                                size="small"
                                onClick={() => setViewMode("list")}
                                sx={{
                                    borderRadius: 0,
                                    bgcolor:
                                        viewMode === "list"
                                            ? "rgba(0, 132, 61, 0.08)"
                                            : "transparent",
                                    color:
                                        viewMode === "list"
                                            ? "primary.main"
                                            : "text.secondary",
                                }}
                            >
                                <ViewListIcon fontSize="small" />
                            </IconButton>
                        </Tooltip>
                    </Box>
                </Box>

                <Button
                    size="small"
                    startIcon={<SortIcon />}
                    endIcon={<KeyboardArrowDownIcon />}
                    onClick={handleSortMenuOpen}
                    variant="text"
                    sx={{
                        color: "text.secondary",
                        textTransform: "none",
                        fontWeight: 400,
                    }}
                >
                    Sort by:{" "}
                    {sortBy === "frequency" ? "Frequency" : "Alphabetical"}
                </Button>

                <Menu
                    anchorEl={sortMenuAnchor}
                    open={Boolean(sortMenuAnchor)}
                    onClose={handleSortMenuClose}
                >
                    <MenuItem
                        onClick={() => handleSortChange("frequency")}
                        selected={sortBy === "frequency"}
                    >
                        <ListItemIcon>
                            <BarChartIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>Sort by Frequency</ListItemText>
                    </MenuItem>
                    <MenuItem
                        onClick={() => handleSortChange("alphabetical")}
                        selected={sortBy === "alphabetical"}
                    >
                        <ListItemIcon>
                            <SortIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText>Sort Alphabetically</ListItemText>
                    </MenuItem>
                </Menu>
            </Box>

            {/* Chart View */}
            {viewMode === "chart" && (
                <Box sx={{ height: 400, pt: 1 }}>
                    <Bar
                        options={chartOptions}
                        data={chartData}
                    />
                </Box>
            )}

            {/* List View */}
            {viewMode === "list" && (
                <Box>
                    <Grid
                        container
                        spacing={2}
                    >
                        {sortedSymptoms.map((symptom: any, index: number) => {
                            const frequency =
                                frequencies[
                                    symptoms.findIndex(
                                        (s) => s.id === symptom.id
                                    )
                                ] || 0;
                            const severity = getSeverityLevel(frequency);

                            return (
                                <Grid
                                    item
                                    xs={12}
                                    sm={6}
                                    md={4}
                                    key={symptom.id}
                                >
                                    <Card
                                        sx={{
                                            borderLeft: `4px solid ${severity.color}`,
                                            transition: "all 0.2s",
                                            "&:hover": {
                                                boxShadow:
                                                    "0 4px 8px rgba(0,0,0,0.1)",
                                                transform: "translateY(-2px)",
                                            },
                                        }}
                                        variant="outlined"
                                    >
                                        <CardContent sx={{ p: 2 }}>
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    justifyContent:
                                                        "space-between",
                                                    alignItems: "flex-start",
                                                    mb: 1,
                                                }}
                                            >
                                                <Typography
                                                    variant="subtitle2"
                                                    sx={{
                                                        fontWeight: 600,
                                                        mb: 0.5,
                                                    }}
                                                >
                                                    {symptom.name}
                                                </Typography>
                                                <IconButton
                                                    size="small"
                                                    sx={{ mt: -0.5, mr: -0.5 }}
                                                    onClick={(e) =>
                                                        handleIssueMenuOpen(
                                                            e,
                                                            symptom.id
                                                        )
                                                    }
                                                >
                                                    <MoreVertIcon fontSize="small" />
                                                </IconButton>
                                                <Menu
                                                    anchorEl={
                                                        issueMenuAnchor[
                                                            symptom.id
                                                        ]
                                                    }
                                                    open={Boolean(
                                                        issueMenuAnchor[
                                                            symptom.id
                                                        ]
                                                    )}
                                                    onClose={() =>
                                                        handleIssueMenuClose(
                                                            symptom.id
                                                        )
                                                    }
                                                >
                                                    <MenuItem
                                                        onClick={() =>
                                                            handleIssueMenuClose(
                                                                symptom.id
                                                            )
                                                        }
                                                    >
                                                        <ListItemIcon>
                                                            <SettingsIcon fontSize="small" />
                                                        </ListItemIcon>
                                                        <ListItemText>
                                                            View Details
                                                        </ListItemText>
                                                    </MenuItem>
                                                </Menu>
                                            </Box>

                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    gap: 1,
                                                    mb: 1.5,
                                                    alignItems: "center",
                                                }}
                                            >
                                                <Chip
                                                    label={`${frequency} occurrences`}
                                                    size="small"
                                                    sx={{
                                                        bgcolor:
                                                            "rgba(0,0,0,0.05)",
                                                        fontWeight: 500,
                                                        fontSize: "0.75rem",
                                                    }}
                                                />
                                                <Chip
                                                    label={severity.level}
                                                    size="small"
                                                    sx={{
                                                        bgcolor: `${severity.color}20`,
                                                        color: severity.color,
                                                        fontWeight: 500,
                                                        fontSize: "0.75rem",
                                                    }}
                                                />
                                            </Box>

                                            <Typography
                                                variant="body2"
                                                color="text.secondary"
                                                noWrap
                                            >
                                                {symptom.description ||
                                                    "No description available"}
                                            </Typography>
                                        </CardContent>
                                    </Card>
                                </Grid>
                            );
                        })}
                    </Grid>
                </Box>
            )}

            {/* Legend */}
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    flexWrap: "wrap",
                    gap: 2,
                    mt: 3,
                    pt: 2,
                    borderTop: "1px solid rgba(0,0,0,0.06)",
                }}
            >
                <Typography
                    variant="caption"
                    color="text.secondary"
                >
                    Severity levels:
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box
                        sx={{
                            width: 12,
                            height: 12,
                            borderRadius: "50%",
                            bgcolor: "#ef4444",
                            mr: 1,
                        }}
                    />
                    <Typography
                        variant="caption"
                        color="text.secondary"
                    >
                        High
                    </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box
                        sx={{
                            width: 12,
                            height: 12,
                            borderRadius: "50%",
                            bgcolor: "#f97316",
                            mr: 1,
                        }}
                    />
                    <Typography
                        variant="caption"
                        color="text.secondary"
                    >
                        Medium
                    </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box
                        sx={{
                            width: 12,
                            height: 12,
                            borderRadius: "50%",
                            bgcolor: "#3b82f6",
                            mr: 1,
                        }}
                    />
                    <Typography
                        variant="caption"
                        color="text.secondary"
                    >
                        Low
                    </Typography>
                </Box>
            </Box>
        </Box>
    );
};

export default CommonIssues;
