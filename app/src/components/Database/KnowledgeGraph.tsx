// components/Database/KnowledgeGraph.tsx
import React, { useEffect, useRef, useState, useCallback } from "react";
import {
    Box,
    Typography,
    CircularProgress,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    FormControlLabel,
    Switch,
    Tooltip,
} from "@mui/material";
import { useKnowledge } from "../../contexts/KnowledgeContext";
import * as d3 from "d3";
import {
    fetchKnowledgeGraph,
    fetchDoorTypes,
} from "../../services/knowledgeService";

interface Node {
    id: string;
    name: string;
    type: string;
    description?: string;
    properties?: any;
    x?: number;
    y?: number;
}

interface Link {
    source: string | Node;
    target: string | Node;
    type: string;
    trust_score?: number;
    source_type?: string;
    target_type?: string;
    source_name?: string;
    target_name?: string;
}

interface GraphData {
    nodes: Node[];
    links: Link[];
}

interface DoorType {
    id: string;
    model: string;
}

// More refined color palette for better visual harmony
const NODE_COLORS = {
    SYMPTOM: "#3b82f6", // Blue - softer, more professional
    PROBLEM: "#8b5cf6", // Purple - more muted
    CAUSE: "#f97316", // Orange - less harsh
    SOLUTION: "#10b981", // Green - calmer tone
    TECHNIQUE: "#ef4444", // Red - less aggressive
    SHORTCUT: "#f59e0b", // Amber - warmer
    OBSERVATION: "#14b8a6", // Teal - unique but professional
    DOOR_TYPE: "#6366f1", // Indigo - distinctive
    DOOR_PART: "#64748b", // Slate - neutral
    GENERAL: "#78716c", // Stone - subtle
};

// Helper function to get contrasting text color
const getTextColor = (backgroundColor: string): string => {
    // Convert hex to RGB
    const r = parseInt(backgroundColor.slice(1, 3), 16);
    const g = parseInt(backgroundColor.slice(3, 5), 16);
    const b = parseInt(backgroundColor.slice(5, 7), 16);

    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Return white for dark backgrounds, black for light backgrounds
    return luminance > 0.6 ? "#000000" : "#ffffff";
};

const KnowledgeGraph: React.FC = () => {

    const svgRef = useRef<SVGSVGElement>(null);
    const { extractedKnowledge } = useKnowledge();
    const [dbGraphData, setDbGraphData] = useState<GraphData | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [nodeLimit, setNodeLimit] = useState(50);
    const [showExtractedOnly, setShowExtractedOnly] = useState(false);
    const [doorTypeId, setDoorTypeId] = useState<string>("");
    const [doorTypes, setDoorTypes] = useState<DoorType[]>([]);
    const [zoomLevel, setZoomLevel] = useState(1);

    // Fetch door types for the filter
    useEffect(() => {
        const getDoorTypes = async () => {
            try {
                const token = localStorage.getItem("token") || "";
                const data = await fetchDoorTypes(token);
                setDoorTypes(data);
            } catch (err) {
                console.error("Error fetching door types:", err);
            }
        };

        getDoorTypes();
    }, []);

    // Fetch knowledge graph data from Neo4j
    const loadGraphData = useCallback(async () => {
        try {
            setIsLoading(true);
            setError(null);

            const token = localStorage.getItem("token") || "";
            const data = await fetchKnowledgeGraph(
                token,
                nodeLimit,
                doorTypeId
            );




            setDbGraphData(data);
        } catch (err) {
            console.error("Error fetching knowledge graph:", err);
            setError("Failed to load knowledge graph data");
        } finally {
            setIsLoading(false);
        }
    }, [nodeLimit, doorTypeId]);

    // Fetch data on initial load and when parameters change
    useEffect(() => {
        if (!showExtractedOnly && dbGraphData) {
            console.log(
                "Node Types:",
                dbGraphData.nodes.map((n) => ({
                    id: n.id,
                    name: n.name,
                    type: n.type,
                }))
            );
            console.log(
                "Links:",
                dbGraphData.links.map((l) => ({
                    source: l.source,
                    target: l.target,
                    type: l.type,
                }))
            );
        }
    }, [dbGraphData, showExtractedOnly]);

    // Function to wrap text within nodes
    const wrapText = (
        selection: d3.Selection<any, any, any, any>,
        width: number
    ) => {
        selection.each(function (d: any) {
            const text = d3.select(this);
            const words = d.name.split(/\s+/);
            let line = "";
            let lineNumber = 0;
            const lineHeight = 1.1; // ems
            const y = 0;
            const dy = 0;

            // Clear existing content
            text.text(null);

            let tspan = text
                .append("tspan")
                .attr("x", 0)
                .attr("y", y)
                .attr("dy", dy + "em");

            for (let i = 0; i < words.length; i++) {
                let word = words[i];
                if (word.length > 10) {
                    // Truncate long words
                    word = word.substring(0, 9) + "...";
                }

                let tempLine = line + word + " ";
                if (tempLine.length * 6 > width) {
                    if (lineNumber >= 2) {
                        // Truncate at 3 lines
                        tspan.text(line.trim() + "...");
                        break;
                    }

                    if (i > 0) {
                        tspan.text(line.trim());
                        line = word + " ";
                        tspan = text
                            .append("tspan")
                            .attr("x", 0)
                            .attr("y", y)
                            .attr("dy", ++lineNumber * lineHeight + dy + "em")
                            .text(line);
                    }
                } else {
                    line = tempLine;
                }

                if (i === words.length - 1) {
                    tspan.text(line.trim());
                }
            }
        });
    };

    // Visualize the graph
    useEffect(() => {
        if (!svgRef.current) return;

        // Clear previous graph
        d3.select(svgRef.current).selectAll("*").remove();

        // Determine which data to use
        let graphData: GraphData | null = null;

        if (showExtractedOnly && extractedKnowledge) {
            // Use only extracted knowledge
            const nodes = extractedKnowledge.nodes.map((node: any) => ({
                id: node.id,
                name: node.name,
                type: node.type,
                description: node.description,
                properties: node.properties || {},
            }));

            const links = extractedKnowledge.relationships.map((rel: any) => ({
                source: rel.source_id,
                target: rel.target_id,
                type: rel.type,
                trust_score: rel.trust_score || 0.5,
            }));

            graphData = { nodes, links };
        } else if (!showExtractedOnly && dbGraphData) {
            // Use database graph data
            graphData = dbGraphData;
        } else if (showExtractedOnly && !extractedKnowledge) {
            // No extracted knowledge available
            return;
        } else if (!showExtractedOnly && !dbGraphData) {
            // No database knowledge available
            return;
        }

        if (!graphData || graphData.nodes.length === 0) {
            return;
        }

        // Set up D3 force simulation
        const width = svgRef.current.clientWidth || 600;
        const height = 500;
        const nodeRadius = 30; // Increased node size

        const svg = d3
            .select(svgRef.current)
            .attr("width", width)
            .attr("height", height)
            .attr("viewBox", [0, 0, width, height])
            .attr(
                "style",
                "background-color: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;"
            );

        // Add subtle grid pattern for better visual orientation
        const defs = svg.append("defs");

        // Add a subtle grid pattern
        const pattern = defs
            .append("pattern")
            .attr("id", "grid")
            .attr("width", 50)
            .attr("height", 50)
            .attr("patternUnits", "userSpaceOnUse");

        pattern
            .append("path")
            .attr("d", "M 50 0 L 0 0 0 50")
            .attr("fill", "none")
            .attr("stroke", "#e2e8f0")
            .attr("stroke-width", 0.5);

        // Add grid background
        svg.append("rect")
            .attr("width", width)
            .attr("height", height)
            .attr("fill", "url(#grid)");

        // Add improved arrow marker definition
        defs.append("marker")
            .attr("id", "arrow")
            .attr("viewBox", "0 -3 6 6") // Smaller viewBox
            .attr("refX", nodeRadius + 8) // Adjusted to account for larger nodes
            .attr("refY", 0)
            .attr("markerWidth", 3) // Smaller arrows
            .attr("markerHeight", 3)
            .attr("orient", "auto")
            .append("path")
            .attr("fill", "#64748b") // More subtle color
            .attr("d", "M0,-3L6,0L0,3");

        // Create a container for zoom
        const container = svg.append("g");

        // Add zoom behavior with smoother transitions
        const zoom = d3
            .zoom()
            .scaleExtent([0.2, 4])
            .on("zoom", (event) => {
                container.attr("transform", event.transform);
                setZoomLevel(event.transform.k);
            });

        svg.call(zoom as any);

        // Initial zoom to fit content
        svg.call(
            (zoom as any).transform,
            d3.zoomIdentity.translate(width / 2, height / 2).scale(0.8)
        );

        // Create simulation with nodes and links
        const simulation = d3
            .forceSimulation(graphData.nodes as d3.SimulationNodeDatum[])
            .force(
                "link",
                d3
                    .forceLink(graphData.links)
                    .id((d: any) => d.id)
                    .distance(150) // Increased from 100 to 150 for less cluttering
            )
            .force("charge", d3.forceManyBody().strength(-300)) // Stronger repulsion
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collide", d3.forceCollide().radius(nodeRadius * 1.5)); // Prevent node overlap

        // Create a group for links
        const linkGroup = container.append("g").attr("class", "links");

        // Add links with arrows and curved paths for better visualization
        const link = linkGroup
            .selectAll("path")
            .data(graphData.links)
            .join("path")
            .attr("stroke", "#64748b") // More subtle color
            .attr("stroke-opacity", 0.6)
            .attr("stroke-width", (d: any) =>
                Math.max(1, (d.trust_score || 0.5) * 2.5)
            )
            .attr("fill", "none")
            .attr("marker-end", "url(#arrow)");

        // Create a group for nodes
        const nodeGroup = container.append("g").attr("class", "nodes");

        // Add nodes
        const node = nodeGroup
            .selectAll("g")
            .data(graphData.nodes)
            .join("g")
            .attr("class", "node")
            .call(
                d3
                    .drag<any, any>()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended)
            );

        // Add node circles with improved styling
        node.append("circle")
            .attr("r", nodeRadius)
            .attr(
                "fill",
                (d: any) =>
                    NODE_COLORS[d.type as keyof typeof NODE_COLORS] ||
                    "#78716c"
            )
            .attr("stroke", "#ffffff")
            .attr("stroke-width", 2)
            .attr("opacity", 0.9)
            .on("mouseover", function () {
                d3.select(this)
                    .transition()
                    .duration(300)
                    .attr("r", nodeRadius + 3)
                    .attr("stroke-width", 3);
            })
            .on("mouseout", function () {
                d3.select(this)
                    .transition()
                    .duration(300)
                    .attr("r", nodeRadius)
                    .attr("stroke-width", 2);
            });

        // Add node labels inside the circles
        const nodeText = node
            .append("text")
            .attr("text-anchor", "middle")
            .attr("dy", "0.3em")
            .attr("font-size", 10)
            .attr("font-weight", "500")
            .attr("pointer-events", "none")
            .attr("fill", (d: any) => {
                const color =
                    NODE_COLORS[d.type as keyof typeof NODE_COLORS] ||
                    "#78716c";
                return getTextColor(color);
            });

        // Apply text wrapping
        wrapText(nodeText, nodeRadius * 1.8);

        // Add tooltips for more node information
        node.append("title").text(
            (d: any) => `${d.type}: ${d.name}\n${d.description || ""}`
        );

        // Calculate curved paths for links
        const linkArc = (d: any) => {
            const dx = d.target.x - d.source.x;
            const dy = d.target.y - d.source.y;
            const dr = Math.sqrt(dx * dx + dy * dy) * 1.2;
            return `M${d.source.x},${d.source.y}A${dr},${dr} 0 0,1 ${d.target.x},${d.target.y}`;
        };

        // Update positions on each tick of the simulation
        simulation.on("tick", () => {
            // Update link paths
            link.attr("d", linkArc);

            // Update node positions
            node.attr("transform", (d: any) => `translate(${d.x},${d.y})`);
        });

        // Drag functions
        function dragstarted(event: any, d: any) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event: any, d: any) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event: any, d: any) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // Gentle initial animation to settle the graph
        simulation.alpha(0.8).restart();
    }, [extractedKnowledge, dbGraphData, showExtractedOnly]);

    return (
        <Box>
            <Box
                sx={{
                    mb: 3,
                    display: "flex",
                    flexWrap: "wrap",
                    gap: 2,
                    alignItems: "center",
                    p: 2,
                    borderRadius: 1,
                    backgroundColor: "#f1f5f9",
                }}
            >
                <Tooltip title="Toggle between database knowledge and recently extracted knowledge">
                    <FormControlLabel
                        control={
                            <Switch
                                checked={showExtractedOnly}
                                onChange={(e) =>
                                    setShowExtractedOnly(e.target.checked)
                                }
                                color="primary"
                            />
                        }
                        label="Show extracted knowledge only"
                    />
                </Tooltip>

                {!showExtractedOnly && (
                    <>
                        <FormControl
                            variant="outlined"
                            size="small"
                            sx={{ minWidth: 180 }}
                        >
                            <InputLabel>Door Type</InputLabel>
                            <Select
                                value={doorTypeId}
                                onChange={(e) =>
                                    setDoorTypeId(e.target.value as string)
                                }
                                label="Door Type"
                            >
                                <MenuItem value="">All Door Types</MenuItem>
                                {doorTypes.map((door) => (
                                    <MenuItem
                                        key={door.id}
                                        value={door.id}
                                    >
                                        {door.model}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>

                        <FormControl
                            variant="outlined"
                            size="small"
                            sx={{ minWidth: 120 }}
                        >
                            <InputLabel>Node Limit</InputLabel>
                            <Select
                                value={nodeLimit}
                                onChange={(e) =>
                                    setNodeLimit(Number(e.target.value))
                                }
                                label="Node Limit"
                            >
                                <MenuItem value={20}>20 nodes</MenuItem>
                                <MenuItem value={50}>50 nodes</MenuItem>
                                <MenuItem value={100}>100 nodes</MenuItem>
                                <MenuItem value={200}>200 nodes</MenuItem>
                            </Select>
                        </FormControl>

                        <button
                            onClick={loadGraphData}
                            style={{
                                padding: "8px 16px",
                                backgroundColor: "#3b82f6",
                                color: "white",
                                border: "none",
                                borderRadius: "6px",
                                cursor: "pointer",
                                fontWeight: 500,
                                boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                                transition: "background-color 0.2s ease",
                            }}
                            onMouseOver={(e) => {
                                e.currentTarget.style.backgroundColor =
                                    "#2563eb";
                            }}
                            onMouseOut={(e) => {
                                e.currentTarget.style.backgroundColor =
                                    "#3b82f6";
                            }}
                        >
                            Refresh Graph
                        </button>
                    </>
                )}

                <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ ml: "auto" }}
                >
                    Zoom: {Math.round(zoomLevel * 100)}%
                </Typography>
            </Box>

            {isLoading && (
                <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
                    <CircularProgress />
                </Box>
            )}

            {error && (
                <Typography
                    color="error"
                    sx={{ p: 2 }}
                >
                    {error}
                </Typography>
            )}

            {!isLoading &&
                !error &&
                ((!showExtractedOnly &&
                    (!dbGraphData || dbGraphData.nodes.length === 0)) ||
                    (showExtractedOnly &&
                        (!extractedKnowledge ||
                            extractedKnowledge.nodes.length === 0))) && (
                    <Typography
                        variant="body2"
                        color="text.secondary"
                        align="center"
                        sx={{ py: 4 }}
                    >
                        {showExtractedOnly
                            ? "No extracted knowledge data to visualize yet. Record a description to see the knowledge graph."
                            : "No knowledge data available in the database."}
                    </Typography>
                )}

            <Box sx={{ position: "relative" }}>
                <svg
                    ref={svgRef}
                    width="100%"
                    height="500"
                ></svg>
            </Box>

            <Box
                sx={{
                    display: "flex",
                    flexWrap: "wrap",
                    justifyContent: "center",
                    mt: 3,
                    gap: 1.5,
                    p: 2,
                    borderRadius: 1,
                    backgroundColor: "#f8fafc",
                }}
            >
                {Object.entries(NODE_COLORS).map(([type, color]) => (
                    <Box
                        key={type}
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: 1,
                            mx: 1,
                        }}
                    >
                        <Box
                            sx={{
                                width: 12,
                                height: 12,
                                borderRadius: "50%",
                                backgroundColor: color,
                                mr: 1,
                                border: "1px solid #e2e8f0",
                            }}
                        ></Box>
                        <Typography variant="caption">
                            {type.replace("_", " ")}
                        </Typography>
                    </Box>
                ))}
            </Box>

            <Typography
                variant="caption"
                sx={{
                    display: "block",
                    textAlign: "center",
                    mt: 2,
                    color: "text.secondary",
                }}
            >
                Tip: Drag nodes to rearrange. Scroll to zoom in/out. Click and
                drag background to pan.
            </Typography>
        </Box>
    );
};

export default KnowledgeGraph;
