// components/ProtectedRoute.tsx
import React, { useEffect, useRef } from "react";
import { useRouter } from "next/router";
import { Box, CircularProgress } from "@mui/material";
import { useAuth } from "../contexts/AuthContext";

interface ProtectedRouteProps {
    children: React.ReactNode;
    requireAuth: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
    children,
    requireAuth,
}) => {
    const { user, loading, isDevMode } = useAuth();
    const router = useRouter();
    const redirectingRef = useRef(false);

    useEffect(() => {
        // Don't redirect during loading
        if (loading) return;

        // Don't redirect if router is not ready
        if (!router.isReady) return;

        // Prevent multiple redirects
        if (redirectingRef.current) return;

        const isAuthenticated = !!(user || isDevMode);

        console.log("ProtectedRoute check:", {
            requireAuth,
            user: !!user,
            isDevMode,
            isAuthenticated,
            pathname: router.pathname,
            redirecting: redirectingRef.current,
        });

        if (requireAuth) {
            // Page requires authentication
            if (!isAuthenticated) {
                // Not authenticated, redirect to login
                console.log("Redirecting to login from protected route");
                redirectingRef.current = true;
                router.replace("/login").finally(() => {
                    redirectingRef.current = false;
                });
                return;
            }
        } else {
            // Page doesn't require authentication (like login page)
            if (isAuthenticated) {
                // Already authenticated, redirect to dashboard
                console.log("Redirecting to dashboard from public route");
                redirectingRef.current = true;
                router.replace("/").finally(() => {
                    redirectingRef.current = false;
                });
                return;
            }
        }
    }, [
        user,
        loading,
        isDevMode,
        requireAuth,
        router.isReady,
        router.pathname,
    ]);

    // Show loading spinner while checking auth or while router is not ready
    if (loading || !router.isReady) {
        return (
            <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                minHeight="100vh"
            >
                <CircularProgress />
            </Box>
        );
    }

    const isAuthenticated = !!(user || isDevMode);

    // For pages that require auth
    if (requireAuth) {
        // If not authenticated, show loading while redirect happens
        if (!isAuthenticated) {
            return (
                <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    minHeight="100vh"
                >
                    <CircularProgress />
                </Box>
            );
        }
    } else {
        // For pages that don't require auth (like login)
        // If authenticated, show loading while redirect happens
        if (isAuthenticated) {
            return (
                <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    minHeight="100vh"
                >
                    <CircularProgress />
                </Box>
            );
        }
    }

    // Render the children
    return <>{children}</>;
};

export default ProtectedRoute;
