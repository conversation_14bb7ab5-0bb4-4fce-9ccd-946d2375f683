// components/UI/ResetButton.tsx
import React, { useState } from "react";
import {
    Button,
    IconButton,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Typography,
    Box,
} from "@mui/material";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import { useKnowledge } from "../../contexts/KnowledgeContext";

interface ResetButtonProps {
    id?: string;
    variant?: "icon" | "text" | "full";
}

const ResetButton: React.FC<ResetButtonProps> = ({ id, variant = "full" }) => {
    const { resetState, isProcessing, isRecording } = useKnowledge();
    const [confirmOpen, setConfirmOpen] = useState(false);

    const handleResetClick = () => {
        if (variant === "full") {
            setConfirmOpen(true);
        } else {
            resetState();
        }
    };

    const handleConfirmReset = () => {
        resetState();
        setConfirmOpen(false);
    };

    const handleCancelReset = () => {
        setConfirmOpen(false);
    };

    if (variant === "icon") {
        return (
            <Tooltip title="Reset the demo to initial state">
                <span>
                    <IconButton
                        color="primary"
                        onClick={handleResetClick}
                        disabled={isProcessing || isRecording}
                        id={id}
                        size="small"
                        sx={{
                            backgroundColor: "rgba(0, 132, 61, 0.08)",
                            "&:hover": {
                                backgroundColor: "rgba(0, 132, 61, 0.12)",
                            },
                        }}
                    >
                        <RestartAltIcon fontSize="small" />
                    </IconButton>
                </span>
            </Tooltip>
        );
    }

    if (variant === "text") {
        return (
            <Button
                variant="text"
                color="primary"
                startIcon={<RestartAltIcon />}
                onClick={handleResetClick}
                disabled={isProcessing || isRecording}
                id={id}
                size="small"
            >
                Reset
            </Button>
        );
    }

    return (
        <>
            <Button
                variant="outlined"
                color="primary"
                startIcon={<RestartAltIcon />}
                onClick={handleResetClick}
                disabled={isProcessing || isRecording}
                id={id}
                size="small"
                sx={{
                    borderColor: "rgba(0, 132, 61, 0.5)",
                    "&:hover": {
                        backgroundColor: "rgba(0, 132, 61, 0.04)",
                        borderColor: "primary.main",
                    },
                }}
            >
                Reset Demo
            </Button>

            <Dialog
                open={confirmOpen}
                onClose={handleCancelReset}
                maxWidth="xs"
                fullWidth
            >
                <DialogTitle sx={{ pb: 1 }}>Reset Confirmation</DialogTitle>

                <DialogContent>
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "flex-start",
                            mb: 2,
                        }}
                    >
                        <WarningAmberIcon
                            color="warning"
                            sx={{ mr: 2, mt: 0.5 }}
                        />
                        <Typography variant="body2">
                            This will clear all extracted knowledge and reset
                            the application to its initial state. This action
                            cannot be undone.
                        </Typography>
                    </Box>
                </DialogContent>

                <DialogActions sx={{ p: 2, pt: 0 }}>
                    <Button
                        onClick={handleCancelReset}
                        color="inherit"
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmReset}
                        variant="contained"
                        color="primary"
                        startIcon={<RestartAltIcon />}
                    >
                        Reset Demo
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default ResetButton;
