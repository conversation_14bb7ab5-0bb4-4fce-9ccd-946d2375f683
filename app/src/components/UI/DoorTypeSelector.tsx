// components/UI/DoorTypeSelector.tsx
import React, { useEffect } from "react";
import {
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    SelectChangeEvent,
    Box,
    Chip,
    Typography,
    ListItemIcon,
    ListItemText,
    CircularProgress,
} from "@mui/material";
import MeetingRoomIcon from "@mui/icons-material/MeetingRoom";
import DevicesOtherIcon from "@mui/icons-material/DevicesOther";
import { useKnowledge } from "../../contexts/KnowledgeContext";

const DoorTypeSelector: React.FC = () => {
    const {
        isProcessing,
        isRecording,
        doorModel,
        setDoorModel,
        loadKnowledgeGraph,
        doorTypes,
        loadDoorTypes,
    } = useKnowledge();

    // Load door types when the component mounts
    useEffect(() => {
        console.log("Loading door types...");
        loadDoorTypes();
    }, [loadDoorTypes]);

    // Debug logging to monitor doorTypes
    useEffect(() => {
        console.log("Door types updated:", doorTypes);
    }, [doorTypes]);

    const handleChange = (event: SelectChangeEvent) => {
        const newDoorModelId = event.target.value;

        if (newDoorModelId === "") {
            // Handle "Any Model" selection
            setDoorModel(null, null);
        } else {
            // Find the selected model to get its name/label
            const selectedModel = mappedDoorTypes.find(
                (model) => model.id === newDoorModelId
            );

            if (selectedModel) {
                // Pass both ID and model name to context
                setDoorModel(newDoorModelId, selectedModel.label);
                console.log(
                    `Selected door model: ${selectedModel.label} (ID: ${newDoorModelId})`
                );
            } else {
                // Fallback if model not found
                setDoorModel(newDoorModelId, null);
                console.log(
                    `Selected door model ID: ${newDoorModelId} (name not found)`
                );
            }
        }

        // Reload knowledge graph with new filter
        loadKnowledgeGraph();
    };

    // Map between API response format and what we need for display
    const mappedDoorTypes = doorTypes.map((type) => ({
        id: type.id,
        // Use the model property from API instead of name
        label: type.model,
    }));

    const isLoading = doorTypes.length === 0 && !isProcessing;

    return (
        <FormControl
            fullWidth
            size="small"
            sx={{
                "& .MuiOutlinedInput-root": {
                    "&:hover fieldset": {
                        borderColor: "primary.main",
                    },
                    "&.Mui-focused fieldset": {
                        borderColor: "primary.main",
                    },
                },
            }}
        >
            <InputLabel id="door-type-label">Door Model</InputLabel>
            <Select
                labelId="door-type-label"
                id="door-type-select"
                value={doorModel || ""}
                label="Door Model"
                onChange={handleChange}
                disabled={isProcessing || isRecording}
                renderValue={(selected) => {
                    if (!selected) {
                        return (
                            <Box
                                sx={{ display: "flex", alignItems: "center" }}
                            >
                                <DevicesOtherIcon
                                    fontSize="small"
                                    sx={{ mr: 1, color: "text.secondary" }}
                                />
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                >
                                    Any Model
                                </Typography>
                            </Box>
                        );
                    }

                    const selectedModel = mappedDoorTypes.find(
                        (model) => model.id === selected
                    );
                    return (
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Chip
                                size="small"
                                label={selectedModel?.label || selected}
                                sx={{
                                    backgroundColor: "primary.main",
                                    color: "white",
                                    fontWeight: 500,
                                    "& .MuiChip-label": { px: 1 },
                                }}
                            />
                        </Box>
                    );
                }}
            >
                <MenuItem value="">
                    <ListItemIcon>
                        <DevicesOtherIcon
                            fontSize="small"
                            sx={{ color: "text.secondary" }}
                        />
                    </ListItemIcon>
                    <ListItemText
                        primary="Any Model"
                        secondary="All revolving door types"
                    />
                </MenuItem>

                {isLoading ? (
                    <MenuItem disabled>
                        <CircularProgress
                            size={24}
                            sx={{ mr: 2 }}
                        />
                        <Typography>Loading door models...</Typography>
                    </MenuItem>
                ) : (
                    mappedDoorTypes.map((model) => (
                        <MenuItem
                            key={model.id}
                            value={model.id}
                        >
                            <ListItemIcon>
                                <MeetingRoomIcon
                                    fontSize="small"
                                    sx={{ color: "primary.main" }}
                                />
                            </ListItemIcon>
                            <ListItemText primary={model.label} />
                        </MenuItem>
                    ))
                )}
            </Select>
        </FormControl>
    );
};

export default DoorTypeSelector;
