// components/Layout/Layout.tsx
import React, { useEffect } from "react";
import {
    Box,
    AppBar,
    Toolbar,
    Typography,
    IconButton,
    Drawer,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Divider,
    Avatar,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import DashboardIcon from "@mui/icons-material/Dashboard";
import SettingsIcon from "@mui/icons-material/Settings";
import ExitToAppIcon from "@mui/icons-material/ExitToApp";
import Link from "next/link";
import { useRouter } from "next/router";
import { useAuth } from "../../contexts/AuthContext";

// Helper to check if we're running in a browser
const isBrowser = typeof window !== "undefined";

interface LayoutProps {
    children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
    const [drawerOpen, setDrawerOpen] = React.useState(false);
    const { user, logout } = useAuth();

    // Always call useRouter unconditionally
    const router = useRouter();

    useEffect(() => {
        // Only run this on the client side
        if (isBrowser && !user) {
            router.push("/login");
        }
    }, [user, router]);

    const toggleDrawer = () => {
        setDrawerOpen(!drawerOpen);
    };

    return (
        <Box sx={{ display: "flex" }}>
            <AppBar
                position="fixed"
                sx={{
                    backgroundColor: "white",
                    color: "text.primary",
                }}
                elevation={0}
            >
                <Toolbar
                    sx={{ borderBottom: "1px solid rgba(0, 0, 0, 0.06)" }}
                >
                    <IconButton
                        color="inherit"
                        aria-label="open drawer"
                        edge="start"
                        onClick={toggleDrawer}
                        sx={{ mr: 2 }}
                    >
                        <MenuIcon />
                    </IconButton>
                    <Box
                        component="img"
                        src="/images/boon_logo.jpg"
                        alt="Boon Edam Logo"
                        sx={{
                            height: 40,
                            mr: 2,
                            display: { xs: "none", sm: "block" },
                        }}
                    />
                    <Typography
                        variant="subtitle1"
                        noWrap
                        component="div"
                        sx={{
                            flexGrow: 1,
                            fontWeight: 500,
                        }}
                    >
                        Boon Brain
                    </Typography>
                    {user && (
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Typography
                                variant="body2"
                                sx={{
                                    mr: 2,
                                    display: { xs: "none", sm: "block" },
                                }}
                            >
                                {user.name}
                            </Typography>
                            <Avatar
                                sx={{
                                    bgcolor: "primary.main",
                                    width: 32,
                                    height: 32,
                                    fontSize: "0.875rem",
                                }}
                            >
                                {user.name
                                    ? user.name.charAt(0).toUpperCase()
                                    : "U"}
                            </Avatar>
                        </Box>
                    )}
                </Toolbar>
            </AppBar>

            <Drawer
                anchor="left"
                open={drawerOpen}
                onClose={toggleDrawer}
                sx={{
                    "& .MuiDrawer-paper": {
                        boxSizing: "border-box",
                        width: 280,
                    },
                }}
            >
                <Box
                    sx={{
                        width: 280,
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                    }}
                    role="presentation"
                >
                    <Box
                        sx={{
                            p: 2,
                            display: "flex",
                            alignItems: "center",
                            borderBottom: "1px solid rgba(0, 0, 0, 0.06)",
                            height: 64,
                        }}
                    >
                        <Box
                            component="img"
                            src="/images/logo.png"
                            alt="Boon Edam Logo"
                            sx={{
                                height: 32,
                                mr: 2,
                            }}
                        />
                    </Box>

                    <List sx={{ flexGrow: 1, pt: 2 }}>
                        <Link
                            href="/dashboard"
                            passHref
                        >
                            <ListItem
                                button
                                component="a"
                                sx={{
                                    borderRadius: 0,
                                    mb: 1,
                                    "&:hover": {
                                        backgroundColor:
                                            "rgba(0, 132, 61, 0.04)",
                                    },
                                    ...(router.pathname === "/dashboard" && {
                                        backgroundColor:
                                            "rgba(0, 132, 61, 0.08)",
                                        borderLeft: "4px solid #00843D",
                                    }),
                                }}
                            >
                                <ListItemIcon
                                    sx={{
                                        color:
                                            router.pathname === "/dashboard"
                                                ? "primary.main"
                                                : "inherit",
                                    }}
                                >
                                    <DashboardIcon />
                                </ListItemIcon>
                                <ListItemText
                                    primary="Dashboard"
                                    primaryTypographyProps={{
                                        fontWeight:
                                            router.pathname === "/dashboard"
                                                ? 500
                                                : 400,
                                    }}
                                />
                            </ListItem>
                        </Link>
                        <Link
                            href="/settings"
                            passHref
                        >
                            <ListItem
                                button
                                component="a"
                                sx={{
                                    borderRadius: 0,
                                    mb: 1,
                                    "&:hover": {
                                        backgroundColor:
                                            "rgba(0, 132, 61, 0.04)",
                                    },
                                    ...(router.pathname === "/settings" && {
                                        backgroundColor:
                                            "rgba(0, 132, 61, 0.08)",
                                        borderLeft: "4px solid #00843D",
                                    }),
                                }}
                            >
                                <ListItemIcon
                                    sx={{
                                        color:
                                            router.pathname === "/settings"
                                                ? "primary.main"
                                                : "inherit",
                                    }}
                                >
                                    <SettingsIcon />
                                </ListItemIcon>
                                <ListItemText
                                    primary="Settings"
                                    primaryTypographyProps={{
                                        fontWeight:
                                            router.pathname === "/settings"
                                                ? 500
                                                : 400,
                                    }}
                                />
                            </ListItem>
                        </Link>
                    </List>

                    <Box sx={{ mt: "auto" }}>
                        <Divider />
                        <List>
                            <ListItem
                                button
                                onClick={logout}
                                sx={{
                                    py: 1.5,
                                    "&:hover": {
                                        backgroundColor: "rgba(0, 0, 0, 0.04)",
                                    },
                                }}
                            >
                                <ListItemIcon>
                                    <ExitToAppIcon />
                                </ListItemIcon>
                                <ListItemText primary="Logout" />
                            </ListItem>
                        </List>
                    </Box>
                </Box>
            </Drawer>

            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    pt: 8,
                    backgroundColor: "#f8f9fa",
                    minHeight: "100vh",
                }}
            >
                {children}
            </Box>
        </Box>
    );
};

export default Layout;
