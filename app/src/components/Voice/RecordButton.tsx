// components/Voice/RecordButton.tsx
import React from "react";
import {
    Box,
    IconButton,
    Typography,
    Tooltip,
    CircularProgress,
} from "@mui/material";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import { useKnowledge } from "../../contexts/KnowledgeContext";

const RecordButton: React.FC = () => {
    const { isRecording, startRecording, stopRecording, isProcessing } =
        useKnowledge();

    const handleClick = () => {
        if (isRecording) {
            stopRecording();
        } else {
            startRecording();
        }
    };

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
            }}
        >
            <Tooltip
                title={isRecording ? "Stop recording" : "Start recording"}
            >
                <Box
                    sx={{
                        position: "relative",
                        display: "inline-flex",
                        mb: 1,
                    }}
                >
                    <IconButton
                        color={isRecording ? "error" : "primary"}
                        onClick={handleClick}
                        disabled={isProcessing}
                        size="large"
                        sx={{
                            width: 64,
                            height: 64,
                            backgroundColor: isRecording
                                ? "rgba(211, 47, 47, 0.1)"
                                : "rgba(0, 132, 61, 0.1)",
                            border: isRecording
                                ? "2px solid rgba(211, 47, 47, 0.5)"
                                : "2px solid rgba(0, 132, 61, 0.5)",
                            transition: "all 0.2s ease-in-out",
                            "&:hover": {
                                backgroundColor: isRecording
                                    ? "rgba(211, 47, 47, 0.15)"
                                    : "rgba(0, 132, 61, 0.15)",
                            },
                            ...(isRecording && {
                                animation: "pulse 1.5s infinite ease-in-out",
                                "@keyframes pulse": {
                                    "0%": {
                                        boxShadow:
                                            "0 0 0 0 rgba(211, 47, 47, 0.4)",
                                    },
                                    "70%": {
                                        boxShadow:
                                            "0 0 0 10px rgba(211, 47, 47, 0)",
                                    },
                                    "100%": {
                                        boxShadow:
                                            "0 0 0 0 rgba(211, 47, 47, 0)",
                                    },
                                },
                            }),
                        }}
                    >
                        {isRecording ? (
                            <StopIcon sx={{ fontSize: 28 }} />
                        ) : (
                            <MicIcon sx={{ fontSize: 28 }} />
                        )}
                    </IconButton>
                    {isProcessing && (
                        <CircularProgress
                            size={72}
                            sx={{
                                position: "absolute",
                                top: -4,
                                left: -4,
                                color: "primary.main",
                            }}
                        />
                    )}
                </Box>
            </Tooltip>
            <Typography
                variant="caption"
                color={
                    isRecording
                        ? "error.main"
                        : isProcessing
                        ? "primary.main"
                        : "text.secondary"
                }
                sx={{ fontWeight: isRecording || isProcessing ? 500 : 400 }}
            >
                {isProcessing
                    ? "Processing..."
                    : isRecording
                    ? "Recording..."
                    : "Click to record"}
            </Typography>
        </Box>
    );
};

export default RecordButton;
