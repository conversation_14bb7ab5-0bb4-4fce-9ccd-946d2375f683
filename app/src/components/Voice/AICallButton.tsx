// components/Voice/AICallButton.tsx
import React, { useState, useEffect } from "react";
import {
    Box,
    IconButton,
    Typography,
    Tooltip,
    CircularProgress,
} from "@mui/material";
import CallIcon from "@mui/icons-material/Call";
import CallEndIcon from "@mui/icons-material/CallEnd";
import MicIcon from "@mui/icons-material/Mic";
import StopIcon from "@mui/icons-material/Stop";
import PauseIcon from '@mui/icons-material/Pause';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { useKnowledge } from "../../contexts/KnowledgeContext";
import audioSessionManager from "../../services/audioSessionManager";

interface AICallButtonProps {
    aiModeEnabled: boolean;
    onCallStart?: () => void;
    onCallEnd?: () => void;
}

const AICallButton: React.FC<AICallButtonProps> = ({ 
    aiModeEnabled, 
    onCallStart,
    onCallEnd
}) => {
    const { isRecording, startRecording, stopRecording, isProcessing } = useKnowledge();
    const [sessionState, setSessionState] = useState(audioSessionManager.getState());
    const [isConnecting, setIsConnecting] = useState(false);

    // Subscribe to session state changes
    useEffect(() => {
        const updateState = () => {
            setSessionState(audioSessionManager.getState());
        };

        // Update state periodically (since we don't have event emitters on the state itself)
        const interval = setInterval(updateState, 100);

        return () => {
            clearInterval(interval);
        };
    }, []);

    const handleClick = async () => {
        if (aiModeEnabled) {
            // AI Call Mode - delegate to audioSessionManager
            if (sessionState.isActive) {
                // End the session
                if (onCallEnd) {
                    onCallEnd();
                }
            } else {
                // Start the session
                setIsConnecting(true);
                
                try {
                    await audioSessionManager.startSession(localStorage.getItem("token"));
                    
                    if (onCallStart) {
                        onCallStart();
                    }
                } catch (error) {
                    console.error("Error starting AI session:", error);
                } finally {
                    setIsConnecting(false);
                }
            }
        } else {
            // Standard recording logic - delegate to KnowledgeContext
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        }
    };

    const handlePause = async () => {
        if (sessionState.isPaused) {
            // Resume the session
            setIsConnecting(true);
            try {
                await audioSessionManager.resumeSession();
            } catch (error) {
                console.error("Error resuming session:", error);
            } finally {
                setIsConnecting(false);
            }
        } else {
            // Pause the session
            try {
                await audioSessionManager.pauseSession();
            } catch (error) {
                console.error("Error pausing session:", error);
            }
        }
    };

    if (aiModeEnabled) {
        // Render Call button for AI mode
        return (
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                }}
            >
                <Box sx={{ display: "flex", gap: 1 }}>
                    <Tooltip
                        title={sessionState.isActive ? "End AI call" : "Start AI call"}
                    >
                        <Box
                            sx={{
                                position: "relative",
                                display: "inline-flex",
                                mb: 1,
                            }}
                        >
                            <IconButton
                                color={sessionState.isActive ? "error" : "primary"}
                                onClick={handleClick}
                                disabled={isConnecting}
                                size="large"
                                sx={{
                                    width: 64,
                                    height: 64,
                                    backgroundColor: sessionState.isActive
                                        ? "rgba(211, 47, 47, 0.1)"
                                        : "rgba(0, 132, 61, 0.1)",
                                    border: sessionState.isActive
                                        ? "2px solid rgba(211, 47, 47, 0.5)"
                                        : "2px solid rgba(0, 132, 61, 0.5)",
                                    transition: "all 0.2s ease-in-out",
                                    "&:hover": {
                                        backgroundColor: sessionState.isActive
                                            ? "rgba(211, 47, 47, 0.15)"
                                            : "rgba(0, 132, 61, 0.15)",
                                    },
                                    ...(sessionState.isActive && !sessionState.isPaused && {
                                        animation: "pulse 1.5s infinite ease-in-out",
                                        "@keyframes pulse": {
                                            "0%": {
                                                boxShadow:
                                                    "0 0 0 0 rgba(211, 47, 47, 0.4)",
                                            },
                                            "70%": {
                                                boxShadow:
                                                    "0 0 0 10px rgba(211, 47, 47, 0)",
                                            },
                                            "100%": {
                                                boxShadow:
                                                    "0 0 0 0 rgba(211, 47, 47, 0)",
                                            },
                                        },
                                    }),
                                }}
                            >
                                {sessionState.isActive ? (
                                    <CallEndIcon sx={{ fontSize: 28 }} />
                                ) : (
                                    <CallIcon sx={{ fontSize: 28 }} />
                                )}
                            </IconButton>
                            {isConnecting && (
                                <CircularProgress
                                    size={72}
                                    sx={{
                                        position: "absolute",
                                        top: -4,
                                        left: -4,
                                        color: "primary.main",
                                    }}
                                />
                            )}
                        </Box>
                    </Tooltip>
                    
                    {sessionState.isActive && (
                        <Tooltip
                            title={sessionState.isPaused ? "Resume conversation" : "Pause conversation"}
                        >
                            <Box
                                sx={{
                                    position: "relative",
                                    display: "inline-flex",
                                    mb: 1,
                                }}
                            >
                                <IconButton
                                    color="primary"
                                    onClick={handlePause}
                                    disabled={isConnecting}
                                    size="large"
                                    sx={{
                                        width: 64,
                                        height: 64,
                                        backgroundColor: "rgba(0, 132, 61, 0.1)",
                                        border: "2px solid rgba(0, 132, 61, 0.5)",
                                        transition: "all 0.2s ease-in-out",
                                        "&:hover": {
                                            backgroundColor: "rgba(0, 132, 61, 0.15)",
                                        },
                                    }}
                                >
                                    {sessionState.isPaused ? (
                                        <PlayArrowIcon sx={{ fontSize: 28 }} />
                                    ) : (
                                        <PauseIcon sx={{ fontSize: 28 }} />
                                    )}
                                </IconButton>
                                {isConnecting && (
                                    <CircularProgress
                                        size={72}
                                        sx={{
                                            position: "absolute",
                                            top: -4,
                                            left: -4,
                                            color: "primary.main",
                                        }}
                                    />
                                )}
                            </Box>
                        </Tooltip>
                    )}
                </Box>
                
                <Typography
                    variant="caption"
                    color={
                        sessionState.isActive
                            ? sessionState.isPaused
                                ? "warning.main"
                                : "error.main"
                            : isConnecting
                            ? "primary.main"
                            : "text.secondary"
                    }
                    sx={{ fontWeight: sessionState.isActive || isConnecting ? 500 : 400 }}
                >
                    {isConnecting
                        ? "Connecting..."
                        : sessionState.isActive
                        ? sessionState.isPaused
                            ? "Conversation paused"
                            : "In call with AI..."
                        : "Start AI call"}
                </Typography>
            </Box>
        );
    } else {
        // Standard Record button (unchanged)
        return (
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                }}
            >
                <Tooltip
                    title={isRecording ? "Stop recording" : "Start recording"}
                >
                    <Box
                        sx={{
                            position: "relative",
                            display: "inline-flex",
                            mb: 1,
                        }}
                    >
                        <IconButton
                            color={isRecording ? "error" : "primary"}
                            onClick={handleClick}
                            disabled={isProcessing}
                            size="large"
                            sx={{
                                width: 64,
                                height: 64,
                                backgroundColor: isRecording
                                    ? "rgba(211, 47, 47, 0.1)"
                                    : "rgba(0, 132, 61, 0.1)",
                                border: isRecording
                                    ? "2px solid rgba(211, 47, 47, 0.5)"
                                    : "2px solid rgba(0, 132, 61, 0.5)",
                                transition: "all 0.2s ease-in-out",
                                "&:hover": {
                                    backgroundColor: isRecording
                                        ? "rgba(211, 47, 47, 0.15)"
                                        : "rgba(0, 132, 61, 0.15)",
                                },
                                ...(isRecording && {
                                    animation: "pulse 1.5s infinite ease-in-out",
                                    "@keyframes pulse": {
                                        "0%": {
                                            boxShadow:
                                                "0 0 0 0 rgba(211, 47, 47, 0.4)",
                                        },
                                        "70%": {
                                            boxShadow:
                                                "0 0 0 10px rgba(211, 47, 47, 0)",
                                        },
                                        "100%": {
                                            boxShadow:
                                                "0 0 0 0 rgba(211, 47, 47, 0)",
                                        },
                                    },
                                }),
                            }}
                        >
                            {isRecording ? (
                                <StopIcon sx={{ fontSize: 28 }} />
                            ) : (
                                <MicIcon sx={{ fontSize: 28 }} />
                            )}
                        </IconButton>
                        {isProcessing && (
                            <CircularProgress
                                size={72}
                                sx={{
                                    position: "absolute",
                                    top: -4,
                                    left: -4,
                                    color: "primary.main",
                                }}
                            />
                        )}
                    </Box>
                </Tooltip>
                <Typography
                    variant="caption"
                    color={
                        isRecording
                            ? "error.main"
                            : isProcessing
                            ? "primary.main"
                            : "text.secondary"
                    }
                    sx={{ fontWeight: isRecording || isProcessing ? 500 : 400 }}
                >
                    {isProcessing
                        ? "Processing..."
                        : isRecording
                        ? "Recording..."
                        : "Click to record"}
                </Typography>
            </Box>
        );
    }
};

export default AICallButton;