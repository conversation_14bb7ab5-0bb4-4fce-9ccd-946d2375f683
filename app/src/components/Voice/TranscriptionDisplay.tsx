// components/Voice/TranscriptionDisplay.tsx
import React, { useState, useEffect } from "react";
import { Typography, Box, CircularProgress, Fade } from "@mui/material";
import MicNoneIcon from "@mui/icons-material/MicNone";
import TextFormatIcon from "@mui/icons-material/TextFormat";
import { useKnowledge } from "../../contexts/KnowledgeContext";

interface TranscriptionDisplayProps {
    aiModeEnabled: boolean;
    isCallActive: boolean;
    callCompleted: boolean;
}

const TranscriptionDisplay: React.FC<TranscriptionDisplayProps> = ({
    aiModeEnabled,
    isCallActive,
    callCompleted
}) => {
    const { transcription, isProcessing, isRecording } = useKnowledge();
    
    // Audio visualization heights for the waveform
    const [waveHeights, setWaveHeights] = useState<number[]>([]);
    
    // Generate random wave heights for the audio visualization
    useEffect(() => {
        if (isCallActive && aiModeEnabled) {
            const interval = setInterval(() => {
                // Generate 30 random heights between 5 and 25
                const heights = Array.from({ length: 30 }, () => 
                    Math.floor(Math.random() * 20) + 5
                );
                setWaveHeights(heights);
            }, 100);
            
            return () => clearInterval(interval);
        }
    }, [isCallActive, aiModeEnabled]);

    // Standard mode or completed call - show regular transcription
    if (!aiModeEnabled || callCompleted) {
        return (
            <Box
                sx={{
                    border: "1px solid rgba(0, 0, 0, 0.08)",
                    borderRadius: 1,
                    p: 1.5,
                    minHeight: 70,
                    maxHeight: 120,
                    overflowY: "auto",
                    backgroundColor: "white",
                    position: "relative",
                    transition: "all 0.2s ease-in-out",
                    ...(isRecording && {
                        borderColor: "primary.main",
                        boxShadow: "0 0 0 1px rgba(0, 132, 61, 0.2)",
                    }),
                    ...(isProcessing && {
                        backgroundColor: "#F5F7FA",
                    }),
                }}
            >
                {/* Header */}
                {(isRecording || isProcessing) && (
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            mb: 0.5,
                        }}
                    >
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                            {isRecording ? (
                                <MicNoneIcon
                                    sx={{
                                        color: "primary.main",
                                        mr: 0.5,
                                        fontSize: 16,
                                    }}
                                />
                            ) : (
                                <TextFormatIcon
                                    sx={{
                                        color: "text.secondary",
                                        mr: 0.5,
                                        fontSize: 16,
                                    }}
                                />
                            )}
                            <Typography
                                variant="caption"
                                color={
                                    isRecording ? "primary.main" : "text.secondary"
                                }
                                sx={{ fontWeight: 500 }}
                            >
                                {isRecording ? "Recording..." : "Processing..."}
                            </Typography>
                        </Box>

                        {isProcessing && (
                            <CircularProgress
                                size={14}
                                color="primary"
                            />
                        )}
                    </Box>
                )}

                {/* Content */}
                <Box
                    sx={{
                        minHeight: 40,
                        display: "flex",
                        alignItems: transcription ? "flex-start" : "center",
                        justifyContent: transcription ? "flex-start" : "center",
                    }}
                >
                    {!transcription && !isRecording && !isProcessing && (
                        <Typography
                            variant="body2"
                            color="text.disabled"
                            align="center"
                        >
                            {callCompleted 
                                ? "Call completed, but no transcription available" 
                                : "No transcription available"}
                        </Typography>
                    )}

                    {!transcription && isRecording && (
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                width: "100%",
                            }}
                        >
                            <Box
                                sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: "50%",
                                    backgroundColor: "primary.main",
                                    animation: "pulse 1.5s infinite ease-in-out",
                                    "@keyframes pulse": {
                                        "0%": {
                                            transform: "scale(0.95)",
                                            boxShadow:
                                                "0 0 0 0 rgba(0, 132, 61, 0.5)",
                                        },
                                        "70%": {
                                            transform: "scale(1)",
                                            boxShadow:
                                                "0 0 0 4px rgba(0, 132, 61, 0)",
                                        },
                                        "100%": {
                                            transform: "scale(0.95)",
                                            boxShadow:
                                                "0 0 0 0 rgba(0, 132, 61, 0)",
                                        },
                                    },
                                }}
                            />
                            <Typography
                                variant="body2"
                                color="primary"
                                sx={{ ml: 1, fontWeight: 500 }}
                            >
                                Listening...
                            </Typography>
                        </Box>
                    )}

                    {transcription && (
                        <Typography
                            variant="body2"
                            color="text.primary"
                            sx={{
                                backgroundColor: isProcessing
                                    ? "rgba(0, 0, 0, 0.02)"
                                    : "transparent",
                                borderRadius: 1,
                                width: "100%",
                                fontStyle: "italic",
                            }}
                        >
                            "{transcription}"
                        </Typography>
                    )}
                </Box>
            </Box>
        );
    }

    // Active AI call mode - show waveform animation
    return (
        <Box
            sx={{
                border: "1px solid",
                borderColor: isCallActive ? "primary.main" : "rgba(0, 0, 0, 0.08)",
                borderRadius: 1,
                p: 1.5,
                minHeight: 80,
                maxHeight: 120,
                backgroundColor: "white",
                position: "relative",
                transition: "all 0.2s ease-in-out",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                ...(isCallActive && {
                    boxShadow: "0 0 0 1px rgba(0, 132, 61, 0.2)",
                }),
            }}
        >
            {isCallActive ? (
                // Audio waveform visualization
                <Fade in={true}>
                    <Box sx={{ width: "100%", height: 60, position: "relative" }}>
                        <Box
                            sx={{
                                height: "100%",
                                width: "100%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                gap: 0.5,
                            }}
                        >
                            {waveHeights.map((height, index) => (
                                <Box
                                    key={index}
                                    sx={{
                                        width: 3,
                                        height: `${height}px`,
                                        backgroundColor: "primary.main",
                                        borderRadius: 4,
                                        opacity: height / 25, // Taller bars are more opaque
                                        transition: "height 0.1s ease-in-out",
                                    }}
                                />
                            ))}
                        </Box>
                        <Typography
                            variant="caption"
                            color="primary"
                            align="center"
                            sx={{ 
                                fontWeight: 500, 
                                position: "absolute",
                                bottom: -5,
                                left: 0,
                                right: 0,
                                textAlign: "center"
                            }}
                        >
                            Call in progress...
                        </Typography>
                    </Box>
                </Fade>
            ) : (
                // Ready to start call
                <Box
                    sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                        opacity: 0.7,
                    }}
                >
                    <Typography
                        variant="body2"
                        color="primary"
                        align="center"
                    >
                        Ready for AI conversation
                    </Typography>
                    <Typography
                        variant="caption"
                        color="text.secondary"
                        align="center"
                        sx={{ mt: 0.5 }}
                    >
                        Press the call button to start
                    </Typography>
                </Box>
            )}
        </Box>
    );
};

export default TranscriptionDisplay;