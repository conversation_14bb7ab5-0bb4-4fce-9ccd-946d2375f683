// components/Voice/QueryProcessor.tsx
import React, { useState } from "react";
import {
    Button,
    TextField,
    Box,
    Typography,
    Paper,
    CircularProgress,
    Chip,
    IconButton,
    InputAdornment,
    Fade,
    Tab,
    Tabs,
    Tooltip,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import SendIcon from "@mui/icons-material/Send";
import ClearIcon from "@mui/icons-material/Clear";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import RouteIcon from "@mui/icons-material/Route";
import LightbulbIcon from "@mui/icons-material/Lightbulb";
import AccountTreeIcon from "@mui/icons-material/AccountTree";
import { useKnowledge } from "../../contexts/KnowledgeContext";
import PathVisualizer from "../Knowledge/PathVisualizer";

const QueryProcessor: React.FC = () => {
    const [queryText, setQueryText] = useState("");
    const [activeTab, setActiveTab] = useState(0);
    const { processAsQuery, isProcessing, queryResult, error } =
        useKnowledge();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (queryText.trim()) {
            setActiveTab(0); // Reset to answer tab when submitting new query
            await processAsQuery(queryText.trim());
        }
    };

    const handleClear = () => {
        setQueryText("");
    };

    const handleChangeTab = (
        event: React.SyntheticEvent,
        newValue: number
    ) => {
        setActiveTab(newValue);
    };

    return (
        <Box>
            <form onSubmit={handleSubmit}>
                <TextField
                    fullWidth
                    variant="outlined"
                    placeholder="Ask a question about revolving doors..."
                    value={queryText}
                    onChange={(e) => setQueryText(e.target.value)}
                    disabled={isProcessing}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon color="action" />
                            </InputAdornment>
                        ),
                        endAdornment: queryText && (
                            <InputAdornment position="end">
                                <IconButton
                                    edge="end"
                                    onClick={handleClear}
                                    disabled={isProcessing}
                                    size="small"
                                >
                                    <ClearIcon fontSize="small" />
                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                    sx={{
                        mb: 2,
                        "& .MuiOutlinedInput-root": {
                            "&:hover fieldset": {
                                borderColor: "primary.main",
                            },
                            "&.Mui-focused fieldset": {
                                borderColor: "primary.main",
                            },
                        },
                    }}
                />

                <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    fullWidth
                    disabled={isProcessing || !queryText.trim()}
                    startIcon={
                        isProcessing ? (
                            <CircularProgress
                                size={16}
                                color="inherit"
                            />
                        ) : (
                            <SendIcon />
                        )
                    }
                    sx={{
                        py: 1,
                        fontWeight: 500,
                    }}
                >
                    {isProcessing ? "Processing..." : "Search Knowledge Base"}
                </Button>
            </form>

            {error && (
                <Fade in={true}>
                    <Paper
                        sx={{
                            mt: 3,
                            p: 2,
                            backgroundColor: "#fff4f4",
                            border: "1px solid #ffcdd2",
                            borderRadius: 1,
                        }}
                        elevation={0}
                    >
                        <Typography
                            color="error"
                            variant="body2"
                        >
                            {error}
                        </Typography>
                    </Paper>
                </Fade>
            )}

            {queryResult && (
                <Fade in={true}>
                    <Paper
                        sx={{
                            mt: 3,
                            overflow: "hidden",
                            border: "1px solid rgba(0, 132, 61, 0.2)",
                            borderRadius: 1,
                        }}
                        elevation={0}
                    >
                        <Box
                            sx={{
                                p: 2,
                                backgroundColor: "rgba(0, 132, 61, 0.05)",
                                borderBottom:
                                    "1px solid rgba(0, 132, 61, 0.1)",
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                            }}
                        >
                            <Box>
                                <Typography
                                    variant="subtitle2"
                                    sx={{
                                        fontWeight: 500,
                                        color: "primary.main",
                                    }}
                                >
                                    Query Result
                                </Typography>

                                <Chip
                                    size="small"
                                    icon={
                                        <CheckCircleOutlineIcon fontSize="small" />
                                    }
                                    label="Answer found"
                                    sx={{
                                        mt: 1,
                                        backgroundColor:
                                            "rgba(0, 132, 61, 0.1)",
                                        color: "primary.main",
                                        fontWeight: 500,
                                        fontSize: "0.75rem",
                                    }}
                                />
                            </Box>

                            {queryResult.paths &&
                                queryResult.paths.length > 0 && (
                                    <Tooltip
                                        title={`${queryResult.paths.length} knowledge paths found`}
                                    >
                                        <Chip
                                            size="small"
                                            icon={
                                                <RouteIcon fontSize="small" />
                                            }
                                            label={`${queryResult.paths.length} paths`}
                                            sx={{
                                                backgroundColor:
                                                    "rgba(59, 130, 246, 0.1)",
                                                color: "#3b82f6",
                                                fontWeight: 500,
                                                fontSize: "0.75rem",
                                            }}
                                        />
                                    </Tooltip>
                                )}
                        </Box>

                        <Tabs
                            value={activeTab}
                            onChange={handleChangeTab}
                            sx={{
                                backgroundColor: "white",
                                borderBottom: "1px solid rgba(0, 0, 0, 0.1)",
                                "& .MuiTab-root": {
                                    minHeight: "48px",
                                    fontSize: "0.875rem",
                                },
                            }}
                            variant="fullWidth"
                        >
                            <Tab
                                icon={<LightbulbIcon fontSize="small" />}
                                iconPosition="start"
                                label="Answer"
                            />
                            {queryResult.paths &&
                                queryResult.paths.length > 0 && (
                                    <Tab
                                        icon={
                                            <AccountTreeIcon fontSize="small" />
                                        }
                                        iconPosition="start"
                                        label="Knowledge Paths"
                                    />
                                )}
                        </Tabs>

                        <TabPanel
                            value={activeTab}
                            index={0}
                        >
                            <Typography
                                variant="body1"
                                sx={{ whiteSpace: "pre-line" }}
                            >
                                {queryResult.answer}
                            </Typography>
                        </TabPanel>

                        {queryResult.paths && queryResult.paths.length > 0 && (
                            <TabPanel
                                value={activeTab}
                                index={1}
                            >
                                <PathVisualizer
                                    paths={queryResult.paths}
                                    maxPathsToShow={3}
                                />
                            </TabPanel>
                        )}
                    </Paper>
                </Fade>
            )}
        </Box>
    );
};

// TabPanel component to handle tab content
interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function TabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`tabpanel-${index}`}
            aria-labelledby={`tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3, backgroundColor: "white" }}>{children}</Box>
            )}
        </div>
    );
}

export default QueryProcessor;
