// components/Voice/AICallMode.tsx
import React, { useState, useEffect } from "react";
import {
    Box,
    Switch,
    Typography,
    Tooltip,
    alpha,
    IconButton
} from "@mui/material";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import CallIcon from "@mui/icons-material/Call";
import CallEndIcon from "@mui/icons-material/CallEnd";
import { useKnowledge } from "../../contexts/KnowledgeContext";

interface AICallModeProps {
    onToggle?: (enabled: boolean) => void;
}

const AICallMode: React.FC<AICallModeProps> = ({ onToggle }) => {
    const [aiModeEnabled, setAiModeEnabled] = useState(false);
    const { isRecording, isProcessing } = useKnowledge();

    const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newState = event.target.checked;
        setAiModeEnabled(newState);
        
        if (onToggle) {
            onToggle(newState);
        }
    };

    return (
        <Box 
            sx={{ 
                border: "1px solid", 
                borderColor: aiModeEnabled 
                    ? 'primary.main'
                    : 'divider',
                borderRadius: 1,
                px: 2,
                py: 1.5,
                display: "flex", 
                alignItems: "center", 
                justifyContent: "space-between",
                backgroundColor: aiModeEnabled 
                    ? alpha('#00843D', 0.04)
                    : 'transparent',
                transition: 'all 0.2s ease-in-out'
            }}
        >
            <Box sx={{ display: "flex", alignItems: "center" }}>
                <SmartToyIcon 
                    sx={{ 
                        fontSize: 20, 
                        mr: 1.5, 
                        color: aiModeEnabled ? "primary.main" : "text.disabled"
                    }} 
                />
                <Box>
                    <Typography 
                        variant="body2" 
                        fontWeight={500}
                        color={aiModeEnabled ? "primary.main" : "text.primary"}
                    >
                        AI Call Mode
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                        {aiModeEnabled 
                            ? "Ready to start AI conversation" 
                            : "Enable to have a conversation with AI"}
                    </Typography>
                </Box>
            </Box>
            
            <Switch
                checked={aiModeEnabled}
                onChange={handleToggle}
                color="primary"
                disabled={isProcessing || isRecording}
            />
        </Box>
    );
};

export default AICallMode;