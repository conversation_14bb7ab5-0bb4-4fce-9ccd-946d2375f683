import React, { useState } from "react";
import {
    Button,
    TextField,
    Box,
    Typography,
    Paper,
    CircularProgress,
    Alert,
    Snackbar,
    Fade,
    Tooltip,
    IconButton,
} from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import SendIcon from "@mui/icons-material/Send";
import HelpOutlineIcon from "@mui/icons-material/HelpOutline";
import { useKnowledge } from "../../contexts/KnowledgeContext";
import DoorTypeSelector from "../UI/DoorTypeSelector";

interface KnowledgeExtractorProps {
    compact?: boolean;
}

const KnowledgeExtractor: React.FC<KnowledgeExtractorProps> = ({
    compact = false,
}) => {
    const [knowledgeText, setKnowledgeText] = useState("");
    const [success, setSuccess] = useState(false);
    const { extractFromText, isProcessing, error, doorModel } = useKnowledge();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!knowledgeText.trim()) return;

        try {
            // Pass the selected door model
            await extractFromText(
                knowledgeText.trim(),
                doorModel || undefined
            );
            setSuccess(true);
            setKnowledgeText(""); // Clear the input after successful submission
        } catch (err) {
            // The error handling is already done in the context
            console.error("Knowledge extraction error:", err);
        }
    };

    const handleCloseSnackbar = () => {
        setSuccess(false);
    };

    // If compact mode, render a simpler version
    if (compact) {
        return (
            <>
                <form onSubmit={handleSubmit}>
                    <TextField
                        fullWidth
                        multiline
                        rows={3}
                        variant="outlined"
                        placeholder="Enter technical information about revolving doors..."
                        value={knowledgeText}
                        onChange={(e) => setKnowledgeText(e.target.value)}
                        disabled={isProcessing}
                        sx={{
                            mb: 2,
                            backgroundColor: "white",
                            "& .MuiOutlinedInput-root": {
                                "&:hover fieldset": {
                                    borderColor: "primary.main",
                                },
                                "&.Mui-focused fieldset": {
                                    borderColor: "primary.main",
                                },
                            },
                        }}
                    />

                    {error && (
                        <Alert
                            severity="error"
                            sx={{ mb: 2 }}
                        >
                            {error}
                        </Alert>
                    )}

                    <Button
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={isProcessing || !knowledgeText.trim()}
                        fullWidth
                        startIcon={
                            isProcessing ? (
                                <CircularProgress
                                    size={16}
                                    color="inherit"
                                />
                            ) : (
                                <SendIcon />
                            )
                        }
                        sx={{
                            py: 1,
                            fontWeight: 500,
                        }}
                    >
                        {isProcessing ? "Processing..." : "Extract Knowledge"}
                    </Button>
                </form>

                <Snackbar
                    open={success}
                    autoHideDuration={6000}
                    onClose={handleCloseSnackbar}
                    anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
                >
                    <Alert
                        onClose={handleCloseSnackbar}
                        severity="success"
                        sx={{
                            backgroundColor: "primary.main",
                            color: "white",
                            "& .MuiAlert-icon": {
                                color: "white",
                            },
                        }}
                    >
                        Knowledge successfully extracted and added to the
                        database!
                    </Alert>
                </Snackbar>
            </>
        );
    }

    // Regular full view
    return (
        <Box sx={{ mt: 3, mb: 3 }}>
            <Paper
                sx={{
                    border: "1px solid rgba(0, 0, 0, 0.06)",
                    overflow: "hidden",
                }}
                elevation={0}
            >
                <Box
                    sx={{
                        p: 2,
                        borderBottom: "1px solid rgba(0, 0, 0, 0.06)",
                        backgroundColor: "rgba(0, 132, 61, 0.03)",
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                    }}
                >
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <AddCircleOutlineIcon
                            sx={{
                                color: "primary.main",
                                mr: 1.5,
                                fontSize: 20,
                            }}
                        />
                        <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: 500 }}
                        >
                            Add Knowledge to Database
                        </Typography>
                    </Box>

                    <Tooltip title="Enter technical details, maintenance reports, or troubleshooting information about revolving doors. The system will automatically extract structured knowledge.">
                        <IconButton size="small">
                            <HelpOutlineIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                </Box>

                <Box sx={{ p: 3 }}>
                    <Box sx={{ mb: 3 }}>
                        <Typography
                            variant="body2"
                            sx={{ mb: 1 }}
                        >
                            Select Door Model
                        </Typography>
                        <DoorTypeSelector />
                    </Box>

                    <form onSubmit={handleSubmit}>
                        <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ mb: 2 }}
                        >
                            Enter technical information, maintenance reports,
                            or other relevant knowledge about revolving doors.
                        </Typography>

                        <TextField
                            fullWidth
                            multiline
                            rows={5}
                            variant="outlined"
                            placeholder="Enter knowledge to be extracted (e.g., maintenance reports, technical specifications, troubleshooting steps)..."
                            value={knowledgeText}
                            onChange={(e) => setKnowledgeText(e.target.value)}
                            disabled={isProcessing}
                            sx={{
                                mb: 2,
                                backgroundColor: "white",
                                "& .MuiOutlinedInput-root": {
                                    "&:hover fieldset": {
                                        borderColor: "primary.main",
                                    },
                                    "&.Mui-focused fieldset": {
                                        borderColor: "primary.main",
                                    },
                                },
                            }}
                        />

                        {error && (
                            <Alert
                                severity="error"
                                sx={{ mb: 2 }}
                            >
                                {error}
                            </Alert>
                        )}

                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "flex-end",
                            }}
                        >
                            <Button
                                type="submit"
                                variant="contained"
                                color="primary"
                                disabled={
                                    isProcessing || !knowledgeText.trim()
                                }
                                startIcon={
                                    isProcessing ? (
                                        <CircularProgress
                                            size={20}
                                            color="inherit"
                                        />
                                    ) : (
                                        <SendIcon />
                                    )
                                }
                                sx={{
                                    py: 1.2,
                                    px: 3,
                                    fontWeight: 500,
                                }}
                            >
                                {isProcessing
                                    ? "Processing..."
                                    : "Extract Knowledge"}
                            </Button>
                        </Box>
                    </form>

                    {success && (
                        <Fade in={success}>
                            <Alert
                                severity="success"
                                sx={{
                                    mt: 2,
                                    backgroundColor: "rgba(0, 132, 61, 0.1)",
                                    color: "primary.main",
                                    "& .MuiAlert-icon": {
                                        color: "primary.main",
                                    },
                                }}
                            >
                                Knowledge successfully extracted!
                            </Alert>
                        </Fade>
                    )}
                </Box>
            </Paper>

            <Snackbar
                open={success}
                autoHideDuration={6000}
                onClose={handleCloseSnackbar}
                anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
            >
                <Alert
                    onClose={handleCloseSnackbar}
                    severity="success"
                    sx={{
                        backgroundColor: "primary.main",
                        color: "white",
                        "& .MuiAlert-icon": {
                            color: "white",
                        },
                    }}
                >
                    Knowledge successfully extracted!
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default KnowledgeExtractor;
