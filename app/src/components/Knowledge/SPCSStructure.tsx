// components/Knowledge/SPCSStructure.tsx
import React, { useState } from "react";
import {
    Box,
    Typography,
    Grid,
    Paper,
    Tabs,
    Tab,
    Badge,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
} from "@mui/material";
import { useKnowledge } from "../../contexts/KnowledgeContext";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import BuildIcon from "@mui/icons-material/Build";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";

const SPCSStructure: React.FC = () => {
    const { extractedKnowledge } = useKnowledge();
    const [tabValue, setTabValue] = useState(0);
    const [expandedNodes, setExpandedNodes] = useState<{
        [key: string]: boolean;
    }>({});

    // Function to filter nodes by type
    const getNodesByType = (type: string): any[] => {
        if (!extractedKnowledge || !extractedKnowledge.nodes) return [];
        return extractedKnowledge.nodes.filter(
            (node: any) => node.type === type
        );
    };

    const symptoms = getNodesByType("SYMPTOM");
    const problems = getNodesByType("PROBLEM");
    const causes = getNodesByType("CAUSE");
    const solutions = getNodesByType("SOLUTION");

    const categories = [
        {
            type: "SYMPTOM",
            title: "Symptoms",
            color: "#3b82f6",
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            icon: <ErrorOutlineIcon />,
            nodes: symptoms,
            description: "Observable effects or indicators of a problem",
        },
        {
            type: "PROBLEM",
            title: "Problems",
            color: "#8b5cf6",
            backgroundColor: "rgba(139, 92, 246, 0.1)",
            icon: <WarningAmberIcon />,
            nodes: problems,
            description: "Identified issues that need to be resolved",
        },
        {
            type: "CAUSE",
            title: "Causes",
            color: "#f97316",
            backgroundColor: "rgba(249, 115, 22, 0.1)",
            icon: <BuildIcon />,
            nodes: causes,
            description: "Root factors that lead to problems",
        },
        {
            type: "SOLUTION",
            title: "Solutions",
            color: "#00843D",
            backgroundColor: "rgba(0, 132, 61, 0.1)",
            icon: <CheckCircleOutlineIcon />,
            nodes: solutions,
            description: "Recommended actions to resolve issues",
        },
    ];

    const handleTabChange = (
        event: React.SyntheticEvent,
        newValue: number
    ) => {
        setTabValue(newValue);
    };

    const toggleNodeExpand = (nodeId: string) => {
        setExpandedNodes((prev) => ({
            ...prev,
            [nodeId]: !prev[nodeId],
        }));
    };

    // Calculate total knowledge items
    const totalItems = extractedKnowledge
        ? extractedKnowledge.nodes.length
        : 0;

    // Get active category
    const activeCategory = categories[tabValue];

    return (
        <Box>
            {/* Mobile view: Tab interface */}
            <Box sx={{ display: { xs: "block", md: "none" } }}>
                <Tabs
                    value={tabValue}
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    sx={{
                        mb: 2,
                        "& .MuiTabs-indicator": {
                            backgroundColor: activeCategory.color,
                        },
                        "& .MuiTab-root": {
                            minWidth: "auto",
                            px: 2,
                            "&.Mui-selected": {
                                color: activeCategory.color,
                                fontWeight: 500,
                            },
                        },
                    }}
                >
                    {categories.map((category, index) => (
                        <Tab
                            key={index}
                            label={
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <Box sx={{ mr: 0.5 }}>{category.icon}</Box>
                                    <Box>{category.title}</Box>
                                    <Badge
                                        badgeContent={category.nodes.length}
                                        color="primary"
                                        sx={{
                                            "& .MuiBadge-badge": {
                                                backgroundColor:
                                                    category.color,
                                                minWidth: 18,
                                                height: 18,
                                                fontSize: "0.7rem",
                                                ml: 1,
                                            },
                                        }}
                                    />
                                </Box>
                            }
                            sx={{
                                fontSize: "0.875rem",
                                textTransform: "none",
                                py: 1,
                            }}
                        />
                    ))}
                </Tabs>

                <Box sx={{ mb: 2, px: 1 }}>
                    <Typography
                        variant="body2"
                        color="text.secondary"
                    >
                        {activeCategory.description}
                    </Typography>
                </Box>

                <Box>
                    {activeCategory.nodes.length === 0 ? (
                        <Box
                            sx={{
                                textAlign: "center",
                                py: 6,
                                backgroundColor: "#f8fafc",
                                borderRadius: 1,
                            }}
                        >
                            <Box sx={{ color: activeCategory.color, mb: 1 }}>
                                {activeCategory.icon}
                            </Box>
                            <Typography
                                variant="body1"
                                sx={{
                                    fontWeight: 500,
                                    color: activeCategory.color,
                                }}
                            >
                                No {activeCategory.title.toLowerCase()}{" "}
                                detected
                            </Typography>
                            <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ mt: 1 }}
                            >
                                Record or enter information to extract{" "}
                                {activeCategory.title.toLowerCase()}
                            </Typography>
                        </Box>
                    ) : (
                        activeCategory.nodes.map((node: any) => (
                            <Paper
                                key={node.id}
                                sx={{
                                    p: 0,
                                    mb: 2,
                                    backgroundColor: "white",
                                    border: `1px solid ${activeCategory.backgroundColor}`,
                                    borderLeft: `4px solid ${activeCategory.color}`,
                                    transition: "all 0.2s ease-in-out",
                                    overflow: "hidden",
                                    "&:hover": {
                                        boxShadow:
                                            "0px 2px 8px rgba(0, 0, 0, 0.08)",
                                    },
                                }}
                                elevation={0}
                            >
                                <Box
                                    sx={{
                                        p: 2,
                                        cursor: "pointer",
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "space-between",
                                    }}
                                    onClick={() => toggleNodeExpand(node.id)}
                                >
                                    <Typography
                                        variant="body1"
                                        sx={{
                                            fontWeight: 500,
                                            flex: 1,
                                            overflow: "hidden",
                                            textOverflow: "ellipsis",
                                            whiteSpace: "nowrap",
                                        }}
                                    >
                                        {node.name}
                                    </Typography>
                                    {expandedNodes[node.id] ? (
                                        <ExpandLessIcon fontSize="small" />
                                    ) : (
                                        <ExpandMoreIcon fontSize="small" />
                                    )}
                                </Box>

                                <Collapse in={expandedNodes[node.id]}>
                                    <Box
                                        sx={{
                                            p: 2,
                                            pt: 0,
                                            backgroundColor:
                                                activeCategory.backgroundColor +
                                                "40",
                                        }}
                                    >
                                        <Typography
                                            variant="body2"
                                            color="text.secondary"
                                        >
                                            {node.description ||
                                                "No description available"}
                                        </Typography>
                                    </Box>
                                </Collapse>
                            </Paper>
                        ))
                    )}
                </Box>
            </Box>

            {/* Desktop view: Grid layout */}
            <Box sx={{ display: { xs: "none", md: "block" } }}>
                {totalItems === 0 ? (
                    <Box
                        sx={{
                            textAlign: "center",
                            py: 6,
                            px: 3,
                            backgroundColor: "#f8fafc",
                            borderRadius: 1,
                        }}
                    >
                        <Typography
                            variant="h6"
                            sx={{ fontWeight: 500, mb: 1 }}
                        >
                            No knowledge has been extracted yet
                        </Typography>
                        <Typography
                            variant="body1"
                            color="text.secondary"
                            sx={{ mb: 3 }}
                        >
                            Use the voice recording or manual text entry to
                            extract knowledge about revolving doors
                        </Typography>
                        <Button
                            variant="outlined"
                            color="primary"
                            onClick={() => {
                                // Scroll to the recording section
                                const recordingSection =
                                    document.getElementById(
                                        "voice-recording-section"
                                    );
                                if (recordingSection) {
                                    recordingSection.scrollIntoView({
                                        behavior: "smooth",
                                    });
                                }
                            }}
                        >
                            Start recording
                        </Button>
                    </Box>
                ) : (
                    <Grid
                        container
                        spacing={3}
                    >
                        {categories.map((category, index) => (
                            <Grid
                                item
                                xs={12}
                                sm={6}
                                key={index}
                            >
                                <Box
                                    sx={{
                                        backgroundColor:
                                            category.backgroundColor,
                                        borderRadius: 1,
                                        height: "100%",
                                        display: "flex",
                                        flexDirection: "column",
                                        border:
                                            "1px solid " +
                                            category.backgroundColor,
                                    }}
                                >
                                    <Box
                                        sx={{
                                            p: 2,
                                            color: category.color,
                                            display: "flex",
                                            justifyContent: "space-between",
                                            alignItems: "center",
                                            borderBottom: `2px solid ${category.color}`,
                                        }}
                                    >
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                            }}
                                        >
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    mr: 1.5,
                                                    p: 1,
                                                    backgroundColor:
                                                        "rgba(255, 255, 255, 0.8)",
                                                    borderRadius: "50%",
                                                    color: category.color,
                                                }}
                                            >
                                                {category.icon}
                                            </Box>
                                            <Box>
                                                <Typography
                                                    variant="subtitle1"
                                                    sx={{ fontWeight: 600 }}
                                                >
                                                    {category.title}
                                                    <Typography
                                                        component="span"
                                                        variant="body2"
                                                        sx={{
                                                            ml: 1,
                                                            fontWeight: 600,
                                                            backgroundColor:
                                                                "white",
                                                            color: category.color,
                                                            px: 1,
                                                            py: 0.5,
                                                            borderRadius: 1,
                                                        }}
                                                    >
                                                        {category.nodes.length}
                                                    </Typography>
                                                </Typography>
                                                <Typography
                                                    variant="caption"
                                                    sx={{
                                                        display: "block",
                                                        mt: 0.5,
                                                    }}
                                                >
                                                    {category.description}
                                                </Typography>
                                            </Box>
                                        </Box>
                                    </Box>

                                    <Box
                                        sx={{
                                            p: 2,
                                            backgroundColor: "white",
                                            borderBottomLeftRadius: 1,
                                            borderBottomRightRadius: 1,
                                            flexGrow: 1,
                                            minHeight: 200,
                                            maxHeight: 300,
                                            overflowY: "auto",
                                        }}
                                    >
                                        {category.nodes.length === 0 ? (
                                            <Box
                                                sx={{
                                                    textAlign: "center",
                                                    py: 4,
                                                    backgroundColor: "#f8fafc",
                                                    borderRadius: 1,
                                                    height: "100%",
                                                    display: "flex",
                                                    flexDirection: "column",
                                                    justifyContent: "center",
                                                    alignItems: "center",
                                                }}
                                            >
                                                <Typography
                                                    variant="body2"
                                                    color="text.secondary"
                                                >
                                                    No{" "}
                                                    {category.title.toLowerCase()}{" "}
                                                    detected
                                                </Typography>
                                            </Box>
                                        ) : (
                                            category.nodes.map((node: any) => (
                                                <Paper
                                                    key={node.id}
                                                    sx={{
                                                        p: 2,
                                                        mb: 1.5,
                                                        backgroundColor:
                                                            "white",
                                                        border: `1px solid ${category.backgroundColor}`,
                                                        borderLeft: `3px solid ${category.color}`,
                                                        transition:
                                                            "all 0.2s ease-in-out",
                                                        "&:hover": {
                                                            transform:
                                                                "translateX(2px)",
                                                            boxShadow:
                                                                "0px 2px 8px rgba(0, 0, 0, 0.08)",
                                                        },
                                                    }}
                                                    elevation={0}
                                                >
                                                    <Typography
                                                        variant="body1"
                                                        sx={{
                                                            fontWeight: 500,
                                                            mb: 0.5,
                                                        }}
                                                    >
                                                        {node.name}
                                                    </Typography>
                                                    <Typography
                                                        variant="body2"
                                                        color="text.secondary"
                                                        sx={{
                                                            display:
                                                                "-webkit-box",
                                                            WebkitBoxOrient:
                                                                "vertical",
                                                            WebkitLineClamp: 2,
                                                            overflow: "hidden",
                                                            textOverflow:
                                                                "ellipsis",
                                                        }}
                                                    >
                                                        {node.description ||
                                                            "No description available"}
                                                    </Typography>
                                                </Paper>
                                            ))
                                        )}
                                    </Box>
                                </Box>
                            </Grid>
                        ))}
                    </Grid>
                )}
            </Box>
        </Box>
    );
};

export default SPCSStructure;
