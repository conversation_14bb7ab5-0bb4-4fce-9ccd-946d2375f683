import React, { useState } from "react";
import {
    Box,
    Typography,
    Collapse,
    Paper,
    List,
    ListItem,
    ListItemText,
    Chip,
    IconButton,
    Divider,
    Badge,
    Toolt<PERSON>,
    Fade,
    <PERSON><PERSON>,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ThumbUpIcon from "@mui/icons-material/ThumbUp";
import ThumbDownIcon from "@mui/icons-material/ThumbDown";
import BuildIcon from "@mui/icons-material/Build";
import BugReportIcon from "@mui/icons-material/BugReport";
import WarningIcon from "@mui/icons-material/Warning";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import InfoIcon from "@mui/icons-material/Info";
import ConstructionIcon from "@mui/icons-material/Construction";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";
import { sendRelationshipFeedback } from "../../services/knowledgeService";

// Define types for our data structure
interface NodeProps {
    id: string;
    name: string;
    description: string;
    type: string;
    properties?: Record<string, string>;
    created_at: string;
    updated_at: string;
    created_by: string;
}

interface RelationshipProps {
    source_id: string;
    target_id: string;
    type: string;
    properties?: Record<string, string>;
    trust_score: number;
    valid_from: string;
    valid_to: string | null;
    is_current: boolean;
    feedback_entries: Array<{
        type: string;
        timestamp: string;
        mechanic_id?: string;
    }>;
    created_at: string;
    updated_at: string;
    created_by: string | null;
}

interface PathProps {
    nodes: NodeProps[];
    relationships: RelationshipProps[];
    trust_score: number;
    id?: string;
    resolved_by_relationships?: Array<{
        source_id: string;
        target_id: string;
    }>;
}

interface PathVisualizerProps {
    paths: PathProps[];
    maxPathsToShow?: number;
}

// Helper function to get node type icon
const getNodeTypeIcon = (type: string) => {
    switch (type.toUpperCase()) {
        case "SYMPTOM":
            return <WarningIcon fontSize="small" />;
        case "PROBLEM":
            return <BugReportIcon fontSize="small" />;
        case "CAUSE":
            return <ErrorOutlineIcon fontSize="small" />;
        case "SOLUTION":
            return <CheckCircleIcon fontSize="small" />;
        case "TECHNIQUE":
            return <ConstructionIcon fontSize="small" />;
        case "GENERAL":
            return <InfoIcon fontSize="small" />;
        default:
            return <InfoIcon fontSize="small" />;
    }
};

// Helper to get node type color
const getNodeTypeColor = (type: string) => {
    switch (type.toUpperCase()) {
        case "SYMPTOM":
            return "#f97316"; // Orange
        case "PROBLEM":
            return "#ef4444"; // Red
        case "CAUSE":
            return "#f59e0b"; // Amber
        case "SOLUTION":
            return "#10b981"; // Green
        case "TECHNIQUE":
            return "#3b82f6"; // Blue
        case "GENERAL":
            return "#6366f1"; // Indigo
        default:
            return "#64748b"; // Slate
    }
};

// Helper function to get relationship type icon and label
const getRelationshipTypeInfo = (type: string) => {
    switch (type.toUpperCase()) {
        case "INDICATES":
            return {
                icon: <ArrowRightAltIcon fontSize="small" />,
                label: "Indicates",
                color: "#3b82f6", // Blue
            };
        case "CAUSED_BY":
            return {
                icon: <ArrowRightAltIcon fontSize="small" />,
                label: "Caused by",
                color: "#f97316", // Orange
            };
        case "RESOLVED_BY":
            return {
                icon: <ArrowRightAltIcon fontSize="small" />,
                label: "Resolved by",
                color: "#10b981", // Green
            };
        default:
            return {
                icon: <ArrowRightAltIcon fontSize="small" />,
                label: type.replace(/_/g, " "),
                color: "#64748b", // Slate
            };
    }
};

// Helper to format date
const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
    });
};

const PathVisualizer: React.FC<PathVisualizerProps> = ({
    paths,
    maxPathsToShow = 3,
}) => {
    const [expandedPath, setExpandedPath] = useState<number | null>(null);
    const [showAllPaths, setShowAllPaths] = useState(false);
    const [feedback, setFeedback] = useState<Record<number, "positive" | "negative" | null>>({});

    // Sort paths by trust score
    const sortedPaths = [...paths].sort(
        (a, b) => (b.trust_score || 0) - (a.trust_score || 0)
    );

    const handleExpandPath = (index: number) => {
        setExpandedPath(expandedPath === index ? null : index);
    };

    const handleFeedback = async (pathIndex: number, feedbackType: "positive" | "negative") => {
        const currentFeedback = feedback[pathIndex];
        const token = localStorage.getItem("token") || "";
        const path = sortedPaths[pathIndex];

        // Find RESOLVED_BY relationships in this path
        const resolvedByRelationships = path.relationships
            .filter(rel => rel.type.toUpperCase() === "RESOLVED_BY")
            .map(rel => ({
                source_id: rel.source_id,
                target_id: rel.target_id,
            }));

        // Also check if path has resolved_by_relationships property from backend
        const pathResolvedByRels = path.resolved_by_relationships || [];
        const allResolvedByRels = [...resolvedByRelationships, ...pathResolvedByRels];

        if (allResolvedByRels.length === 0) {
            console.warn("No RESOLVED_BY relationships found in this path");
            return;
        }

        console.log("Sending feedback for RESOLVED_BY relationships:", {
            feedbackType,
            relationships: allResolvedByRels,
            relationshipCount: allResolvedByRels.length,
        });

        try {
            // If clicking the same feedback type, remove feedback (toggle off)
            if (currentFeedback === feedbackType) {
                // For now, we don't implement feedback removal
                // You could implement this by sending opposite feedback or a delete endpoint
                setFeedback((prev) => ({
                    ...prev,
                    [pathIndex]: null,
                }));
                console.log("Feedback toggled off");
                return;
            }

            // Send feedback to backend
            const response = await sendRelationshipFeedback(
                allResolvedByRels,
                feedbackType,
                token
            );

            setFeedback((prev) => ({
                ...prev,
                [pathIndex]: feedbackType,
            }));

            console.log("Feedback sent successfully:", {
                updatedRelationships: response.updated_relationships?.length || 0,
                feedbackType,
            });

        } catch (error) {
            console.error("Failed to send feedback:", error);
            // Revert on error
            setFeedback((prev) => ({
                ...prev,
                [pathIndex]: currentFeedback || null,
            }));
        }
    };

    // Determine which paths to show
    const pathsToDisplay = showAllPaths
        ? sortedPaths
        : sortedPaths.slice(0, maxPathsToShow);

    return (
        <Box sx={{ mt: 2 }}>
            <Typography
                variant="subtitle2"
                gutterBottom
                sx={{
                    color: "text.secondary",
                    display: "flex",
                    alignItems: "center",
                    mb: 2,
                }}
            >
                <BuildIcon fontSize="small" sx={{ mr: 1 }} />
                Knowledge Paths
                <Chip
                    size="small"
                    label={`${paths.length} found`}
                    sx={{
                        ml: 1,
                        fontSize: "0.7rem",
                        height: 20,
                        backgroundColor: "rgba(0, 132, 61, 0.1)",
                        color: "primary.main",
                    }}
                />
            </Typography>

            {pathsToDisplay.map((path, pathIndex) => (
                <Paper
                    key={pathIndex}
                    elevation={0}
                    sx={{
                        mb: 2,
                        overflow: "hidden",
                        border:
                            expandedPath === pathIndex
                                ? "2px solid rgba(0, 132, 61, 0.3)"
                                : "1px solid rgba(0, 0, 0, 0.1)",
                        borderRadius: 1,
                        transition: "all 0.2s ease",
                    }}
                >
                    {/* Path header */}
                    <Box
                        sx={{
                            p: 1.5,
                            backgroundColor:
                                expandedPath === pathIndex
                                    ? "rgba(0, 132, 61, 0.05)"
                                    : "rgba(0, 0, 0, 0.02)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "space-between",
                            cursor: "pointer",
                            "&:hover": {
                                backgroundColor: "rgba(0, 132, 61, 0.1)",
                            },
                            transition: "background-color 0.2s ease",
                        }}
                        onClick={() => handleExpandPath(pathIndex)}
                    >
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Chip
                                size="small"
                                label={pathIndex + 1}
                                sx={{
                                    mr: 2,
                                    fontWeight: 600,
                                    backgroundColor: "primary.main",
                                    color: "white",
                                }}
                            />
                            <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
                                {path.nodes[0]?.name || `Path ${pathIndex + 1}`}
                            </Typography>
                        </Box>

                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                            {/* Feedback buttons for RESOLVED_BY relationships */}
                            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                                <Tooltip title="Mark as helpful">
                                    <IconButton
                                        size="small"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleFeedback(pathIndex, "positive");
                                        }}
                                    >
                                        <ThumbUpIcon
                                            fontSize="small"
                                            sx={{
                                                color:
                                                    feedback[pathIndex] === "positive"
                                                        ? "#10b981"
                                                        : "#cbd5e1",
                                            }}
                                        />
                                    </IconButton>
                                </Tooltip>

                                <Tooltip title="Mark as not helpful">
                                    <IconButton
                                        size="small"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleFeedback(pathIndex, "negative");
                                        }}
                                    >
                                        <ThumbDownIcon
                                            fontSize="small"
                                            sx={{
                                                color:
                                                    feedback[pathIndex] === "negative"
                                                        ? "#ef4444"
                                                        : "#cbd5e1",
                                            }}
                                        />
                                    </IconButton>
                                </Tooltip>
                            </Box>

                            <Tooltip
                                title={`Trust score: ${Math.round(
                                    (path.trust_score || 0) * 100
                                )}%`}
                            >
                                <Chip
                                    size="small"
                                    label={`${Math.round((path.trust_score || 0) * 100)}%`}
                                    sx={{
                                        fontSize: "0.7rem",
                                        height: 20,
                                        mr: 1,
                                        backgroundColor:
                                            (path.trust_score || 0) > 0.7
                                                ? "rgba(16, 185, 129, 0.1)"
                                                : (path.trust_score || 0) > 0.4
                                                ? "rgba(245, 158, 11, 0.1)"
                                                : "rgba(239, 68, 68, 0.1)",
                                        color:
                                            (path.trust_score || 0) > 0.7
                                                ? "#10b981"
                                                : (path.trust_score || 0) > 0.4
                                                ? "#f59e0b"
                                                : "#ef4444",
                                    }}
                                />
                            </Tooltip>

                            <Tooltip title={`${path.nodes.length} nodes`}>
                                <Badge
                                    badgeContent={path.nodes.length}
                                    color="primary"
                                    sx={{
                                        "& .MuiBadge-badge": {
                                            fontSize: "0.6rem",
                                            height: 16,
                                            minWidth: 16,
                                        },
                                        mr: 1,
                                    }}
                                >
                                    <InfoIcon fontSize="small" sx={{ color: "text.secondary" }} />
                                </Badge>
                            </Tooltip>

                            <IconButton
                                size="small"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleExpandPath(pathIndex);
                                }}
                            >
                                {expandedPath === pathIndex ? (
                                    <ExpandLessIcon fontSize="small" />
                                ) : (
                                    <ExpandMoreIcon fontSize="small" />
                                )}
                            </IconButton>
                        </Box>
                    </Box>

                    {/* Path details */}
                    <Collapse in={expandedPath === pathIndex}>
                        <Box sx={{ p: 0, borderRadius: 0, backgroundColor: "#f8fafc" }}>
                            <Box sx={{ p: 2 }}>
                                <List disablePadding>
                                    {path.nodes.map((node, nodeIndex) => (
                                        <React.Fragment key={node.id}>
                                            <ListItem
                                                alignItems="flex-start"
                                                disableGutters
                                                sx={{
                                                    py: 1.5,
                                                    borderLeft: `3px solid ${getNodeTypeColor(node.type)}`,
                                                    pl: 2,
                                                    backgroundColor: "white",
                                                    borderRadius: "4px",
                                                    mb: 1,
                                                }}
                                            >
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        flexDirection: "column",
                                                        width: "100%",
                                                    }}
                                                >
                                                    <Box
                                                        sx={{
                                                            display: "flex",
                                                            alignItems: "center",
                                                            mb: 0.5,
                                                        }}
                                                    >
                                                        <Tooltip title={node.type}>
                                                            <Box
                                                                sx={{
                                                                    display: "flex",
                                                                    alignItems: "center",
                                                                    color: getNodeTypeColor(node.type),
                                                                    mr: 1,
                                                                }}
                                                            >
                                                                {getNodeTypeIcon(node.type)}
                                                            </Box>
                                                        </Tooltip>
                                                        <Typography
                                                            variant="body2"
                                                            sx={{ fontWeight: 600 }}
                                                        >
                                                            {node.name}
                                                        </Typography>
                                                    </Box>

                                                    {node.description && node.description !== node.name && (
                                                        <Typography
                                                            variant="body2"
                                                            color="text.secondary"
                                                            sx={{ ml: 3, mb: 1 }}
                                                        >
                                                            {node.description}
                                                        </Typography>
                                                    )}

                                                    {node.properties &&
                                                        Object.keys(node.properties).length > 0 && (
                                                            <Box
                                                                sx={{
                                                                    mt: 1,
                                                                    ml: 3,
                                                                    p: 1,
                                                                    backgroundColor: "rgba(0, 0, 0, 0.02)",
                                                                    borderRadius: 1,
                                                                }}
                                                            >
                                                                {Object.entries(node.properties).map(
                                                                    ([key, value]) =>
                                                                        value && (
                                                                            <Box
                                                                                key={key}
                                                                                sx={{
                                                                                    display: "flex",
                                                                                    mb: 0.5,
                                                                                }}
                                                                            >
                                                                                <Typography
                                                                                    variant="caption"
                                                                                    sx={{
                                                                                        fontWeight: 600,
                                                                                        color: "text.secondary",
                                                                                        mr: 1,
                                                                                        minWidth: "100px",
                                                                                    }}
                                                                                >
                                                                                    {key.replace(/_/g, " ")}:
                                                                                </Typography>
                                                                                <Typography variant="caption">
                                                                                    {value}
                                                                                </Typography>
                                                                            </Box>
                                                                        )
                                                                )}
                                                            </Box>
                                                        )}
                                                </Box>
                                            </ListItem>

                                            {/* Show relationship arrow if not the last node */}
                                            {nodeIndex < path.nodes.length - 1 && path.relationships[nodeIndex] && (
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        my: 0.5,
                                                        mx: 2,
                                                    }}
                                                >
                                                    <Fade in={true}>
                                                        <Chip
                                                            icon={
                                                                getRelationshipTypeInfo(
                                                                    path.relationships[nodeIndex].type
                                                                ).icon
                                                            }
                                                            label={
                                                                getRelationshipTypeInfo(
                                                                    path.relationships[nodeIndex].type
                                                                ).label
                                                            }
                                                            size="small"
                                                            sx={{
                                                                backgroundColor: `${
                                                                    getRelationshipTypeInfo(
                                                                        path.relationships[nodeIndex].type
                                                                    ).color
                                                                }20`,
                                                                color: getRelationshipTypeInfo(
                                                                    path.relationships[nodeIndex].type
                                                                ).color,
                                                                fontWeight: 500,
                                                                border: `1px solid ${
                                                                    getRelationshipTypeInfo(
                                                                        path.relationships[nodeIndex].type
                                                                    ).color
                                                                }40`,
                                                            }}
                                                        />
                                                    </Fade>
                                                </Box>
                                            )}
                                        </React.Fragment>
                                    ))}
                                </List>
                            </Box>
                        </Box>
                    </Collapse>
                </Paper>
            ))}

            {/* Show button to reveal more paths if available */}
            {paths.length > maxPathsToShow && (
                <Box sx={{ display: "flex", justifyContent: "center", mt: 1, mb: 3 }}>
                    <Button
                        size="small"
                        variant="outlined"
                        onClick={() => setShowAllPaths(!showAllPaths)}
                        sx={{ textTransform: "none", px: 2 }}
                    >
                        {showAllPaths
                            ? `Show top ${maxPathsToShow} paths`
                            : `Show all ${paths.length} paths`}
                    </Button>
                </Box>
            )}
        </Box>
    );
};

export default PathVisualizer;