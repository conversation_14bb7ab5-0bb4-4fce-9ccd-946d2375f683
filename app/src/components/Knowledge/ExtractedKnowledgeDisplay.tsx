import React, { useState } from "react";
import {
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    CardContent,
    <PERSON>,
    <PERSON>con<PERSON>utton,
    <PERSON><PERSON><PERSON>,
    <PERSON>ge,
    <PERSON>ack,
    Button,
    Tooltip,
    Al<PERSON>,
    S<PERSON><PERSON>bar,
} from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import AccountTreeIcon from "@mui/icons-material/AccountTree";
import VisibilityIcon from "@mui/icons-material/Visibility";
import BuildIcon from "@mui/icons-material/Build";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import DescriptionIcon from "@mui/icons-material/Description";
import InfoIcon from "@mui/icons-material/Info";
import SavedIcon from "@mui/icons-material/Save";
import { useKnowledge } from "../../contexts/KnowledgeContext";

// Types for our data structure
interface KnowledgeNode {
    id: string;
    name: string;
    description: string;
    type: string;
    properties?: Record<string, any>;
    created_at?: string;
    updated_at?: string;
    created_by?: string;
}

interface KnowledgeRelationship {
    source_id: string;
    target_id: string;
    type: string;
    trust_score: number;
    properties?: Record<string, any>;
}

interface KnowledgePath {
    nodes: KnowledgeNode[];
    relationships: KnowledgeRelationship[];
    trust_score: number;
}

interface ExtractionResponse {
    nodes: any[]; // The new ExtractedNode format
    paths?: KnowledgePath[]; // Transformed paths
    raw_extracted_data?: string;
    admin?: string;
    summary?: {
        observation?: number;
        cause?: number;
        solution?: number;
        tacit_knowledge?: number;
    };
}

// Compact node component
const CompactNode: React.FC<{ node: KnowledgeNode }> = ({ node }) => {
    const getNodeColor = (type: string) => {
        switch (type.toUpperCase()) {
            case "OBSERVATION":
                return "#3b82f6";
            case "CAUSE":
                return "#f59e0b";
            case "SOLUTION":
                return "#10b981";
            case "GENERAL":
                return "#6366f1";
            default:
                return "#64748b";
        }
    };

    const getNodeIcon = (type: string) => {
        switch (type.toUpperCase()) {
            case "OBSERVATION":
                return <VisibilityIcon sx={{ fontSize: 14 }} />;
            case "CAUSE":
                return <BuildIcon sx={{ fontSize: 14 }} />;
            case "SOLUTION":
                return <CheckCircleIcon sx={{ fontSize: 14 }} />;
            default:
                return <InfoIcon sx={{ fontSize: 14 }} />;
        }
    };

    const color = getNodeColor(node.type);
    const icon = getNodeIcon(node.type);

    // Helper function to render attribute if it exists
    const renderAttribute = (label: string, value: any, isArray = false) => {
        if (!value || (isArray && (!Array.isArray(value) || value.length === 0))) {
            return null;
        }

        return (
            <Box sx={{ mb: 1 }}>
                <Typography
                    variant="caption"
                    sx={{
                        fontWeight: 600,
                        fontSize: "0.7rem",
                        color: "text.secondary",
                        textTransform: "uppercase",
                        display: "block",
                        mb: 0.5,
                    }}
                >
                    {label}:
                </Typography>
                <Typography
                    variant="body2"
                    sx={{
                        fontSize: "0.8rem",
                        lineHeight: 1.3,
                        color: "text.primary",
                    }}
                >
                    {value.toString().replace(",", ", ")} 
                </Typography>
            </Box>
        );
    };

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                p: 2,
                backgroundColor: `${color}05`,
                border: `1px solid ${color}20`,
                borderLeft: `3px solid ${color}`,
                borderRadius: 1,
            }}
        >
            {/* Header with icon and type */}
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    mb: 1.5,
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: 24,
                        height: 24,
                        borderRadius: "50%",
                        backgroundColor: `${color}20`,
                        color,
                        mr: 1.5,
                    }}
                >
                    {icon}
                </Box>
                <Typography
                    variant="caption"
                    sx={{
                        color: "text.secondary",
                        fontSize: "0.7rem",
                        textTransform: "uppercase",
                        fontWeight: 500,
                        opacity: 0.8,
                    }}
                >
                    {node.type}
                </Typography>
            </Box>

            {/* Main name */}
            <Typography
                variant="body1"
                sx={{
                    fontWeight: 600,
                    fontSize: "0.9rem",
                    lineHeight: 1.2,
                    mb: 1.5,
                    color: "text.primary",
                }}
            >
                {node.name}
            </Typography>

            {/* All attributes from node.properties */}
            {node.properties && (
                <Box>
                    {renderAttribute("Description", node.properties.description)}
                    {renderAttribute("Visual Observation", node.properties.visual_observation)}
                    {renderAttribute("Audio Observation", node.properties.auditory_observation)}
                    {renderAttribute("Position", node.properties.positional_observation)}
                    {renderAttribute("Error Code", node.properties.error_codes, true)}
                    {renderAttribute("Related Parts", node.properties.related_parts, true)}
                    {renderAttribute("Parts Involved", node.properties.parts_involved, true)}
                    {renderAttribute("Part Numbers", node.properties.part_numbers, true)}
                </Box>
            )}
        </Box>
    );
};

// Relationship arrow component
const RelationshipArrow: React.FC<{ relationship: KnowledgeRelationship }> = ({
    relationship,
}) => {
    const getRelationshipColor = (type: string) => {
        switch (type.toUpperCase()) {
            case "OBSERVED_WITH":
                return "#3b82f6";
            case "RESOLVED_BY":
                return "#10b981";
            case "CAUSED_BY":
                return "#f97316";
            case "LINKED_TO":
                return "#8b5cf6";
            default:
                return "#64748b";
        }
    };

    const color = getRelationshipColor(relationship.type);

    return (
        <Box
            sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                py: 0.5,
            }}
        >
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 0.5,
                }}
            >
                <ArrowDownwardIcon
                    sx={{
                        fontSize: 16,
                        color,
                    }}
                />
                <Typography
                    variant="caption"
                    sx={{
                        fontSize: "0.65rem",
                        color,
                        fontWeight: 500,
                    }}
                >
                    {relationship.type.replace(/_/g, " ")}
                </Typography>
                {relationship.trust_score > 0 && (
                    <Typography
                        variant="caption"
                        sx={{
                            fontSize: "0.65rem",
                            color: "text.secondary",
                        }}
                    >
                        ({Math.round(relationship.trust_score * 100)}%)
                    </Typography>
                )}
            </Box>
        </Box>
    );
};

// Main component
export const ExtractedKnowledgeDisplay: React.FC = () => {
    const { extractedKnowledge, storeKnowledge, isProcessing } = useKnowledge();
    const [copiedSnackbar, setCopiedSnackbar] = useState(false);

    // New state for tracking storage status and notifications
    const [isStored, setIsStored] = useState(false);
    const [showSuccessSnackbar, setShowSuccessSnackbar] = useState(false);
    const [showErrorSnackbar, setShowErrorSnackbar] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");

    // Extract data from the extractedKnowledge
    const knowledgeData: ExtractionResponse | null = extractedKnowledge;

    // Generate admin description if not provided
    const generateAdminDescription = (paths: KnowledgePath[]): string => {
        if (!paths || paths.length === 0) {
            return "No knowledge paths extracted.";
        }

        let description = "## Maintenance Report\n\n";

        paths.forEach((path, index) => {
            description += `### Issue ${index + 1}\n\n`;

            path.nodes.forEach((node, nodeIndex) => {
                const typeLabel = node.type.charAt(0).toUpperCase() + node.type.slice(1).toLowerCase();
                description += `**${typeLabel}**: ${node.name}\n`;
                if (node.description && node.description !== node.name) {
                    description += `*Details*: ${node.description}\n`;
                }
                description += "\n";
            });
            
            if (path.trust_score > 0) {
                description += `*Trust Level*: ${Math.round(path.trust_score * 100)}%\n`;
            }
            description += "\n---\n\n";
        });

        const summary = knowledgeData?.summary;
        if (summary) {
            description += "### Summary\n";
            if (summary.observation) description += `- Observations: ${summary.observation}\n`;
            if (summary.cause) description += `- Root Causes: ${summary.cause}\n`;
            if (summary.solution) description += `- Solutions: ${summary.solution}\n`;
            if (summary.tacit_knowledge) description += `- Technical Notes: ${summary.tacit_knowledge}\n`;
        }

        return description;
    };

    const adminContent = knowledgeData?.admin ||
        (knowledgeData?.paths ? generateAdminDescription(knowledgeData.paths) : "");

    const handleCopyAdmin = () => {
        navigator.clipboard.writeText(adminContent);
        setCopiedSnackbar(true);
    };

    const handleCloseSnackbar = () => {
        setShowSuccessSnackbar(false);
        setShowErrorSnackbar(false);
        setCopiedSnackbar(false);
    };

    const handleStoreKnowledge = async () => {
        try {
            if (knowledgeData?.raw_extracted_data) {
                await storeKnowledge(knowledgeData.raw_extracted_data);
                console.log("Knowledge stored successfully");
                setIsStored(true);
                setShowSuccessSnackbar(true);
            }
        } catch (error) {
            console.error("Failed to store knowledge:", error);
            setErrorMessage(error instanceof Error ? error.message : "Data could not be added to the database");
            setShowErrorSnackbar(true);
        }
    };

    if (!knowledgeData || !knowledgeData.paths || knowledgeData.paths.length === 0) {
        return (
            <Alert severity="info" sx={{ mb: 3 }}>
                No knowledge has been extracted yet. Use voice recording or text input to extract knowledge.
            </Alert>
        );
    }

    return (
        <>
            <Paper
                sx={{
                    p: 0,
                    mb: 3,
                    overflow: "hidden",
                    border: "1px solid rgba(0, 0, 0, 0.06)",
                }}
                elevation={0}
            >
                <Box
                    sx={{
                        p: 2,
                        borderBottom: "1px solid rgba(0, 0, 0, 0.06)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        backgroundColor: isStored ? "rgba(16, 185, 129, 0.03)" : "rgba(0, 132, 61, 0.03)",
                    }}
                >
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                        <AccountTreeIcon
                            sx={{ color: "primary.main", mr: 1.5 }}
                        />
                        <Typography
                            variant="subtitle1"
                            sx={{ fontWeight: 500 }}
                        >
                            Extracted Knowledge Analysis
                        </Typography>
                        <Badge
                            badgeContent={knowledgeData.paths.length}
                            color="primary"
                            sx={{ ml: 2 }}
                        >
                            <Chip
                                label="paths"
                                size="small"
                                sx={{
                                    fontSize: "0.7rem",
                                    height: 20,
                                }}
                            />
                        </Badge>
                    </Box>
                    {/* Right side with approve button */}
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Chip
                            label={isStored ? "Stored" : "Pending Review"}
                            size="small"
                            sx={{
                                fontSize: "0.7rem",
                                height: 24,
                                backgroundColor: isStored ? "rgba(16, 185, 129, 0.1)" : "rgba(245, 158, 11, 0.1)",
                                color: isStored ? "#10b981" : "#f59e0b",
                                border: isStored ? "1px solid rgba(16, 185, 129, 0.2)" : "1px solid rgba(245, 158, 11, 0.2)",
                            }}
                        />
                        <Button
                            variant="contained"
                            size="small"
                            startIcon={isStored ? <SavedIcon /> : <CheckIcon />}
                            disabled={isProcessing || isStored}
                            sx={{
                                backgroundColor: isStored ? "#6b7280" : "#10b981",
                                color: "white",
                                fontSize: "0.8rem",
                                fontWeight: 600,
                                px: 2,
                                py: 0.5,
                                borderRadius: 1.5,
                                textTransform: "none",
                                boxShadow: isStored ? "none" : "0 2px 4px rgba(16, 185, 129, 0.2)",
                                "&:hover": {
                                    backgroundColor: isStored ? "#6b7280" : "#059669",
                                    boxShadow: isStored ? "none" : "0 4px 8px rgba(16, 185, 129, 0.3)",
                                    transform: isStored ? "none" : "translateY(-1px)",
                                },
                                "&:active": {
                                    transform: "translateY(0)",
                                },
                                "&:disabled": {
                                    backgroundColor: "#6b7280",
                                    color: "white",
                                },
                                transition: "all 0.2s ease-in-out",
                            }}
                            onClick={handleStoreKnowledge}
                        >
                            {isProcessing ? "Storing..." : isStored ? "Saved" : "Approve & Store"}
                        </Button>
                    </Box>
                </Box>

                <Box sx={{ p: 2 }}>
                    <Grid container spacing={3}>
                        {/* OCS Paths Column */}
                        <Grid item xs={12} md={6}>
                            <Box
                                sx={{
                                    p: 2,
                                    backgroundColor: "rgba(16, 185, 129, 0.03)",
                                    borderRadius: 1,
                                    border: "1px solid rgba(16, 185, 129, 0.1)",
                                    height: "100%",
                                }}
                            >
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        mb: 1.5,
                                    }}
                                >
                                    <AccountTreeIcon
                                        sx={{ mr: 1, color: "#10b981", fontSize: 18 }}
                                    />
                                    <Typography
                                        variant="subtitle2"
                                        sx={{ fontWeight: 600 }}
                                    >
                                        OCS Knowledge Paths
                                    </Typography>
                                    <Chip
                                        label="Mechanics Database"
                                        size="small"
                                        sx={{
                                            ml: "auto",
                                            fontSize: "0.7rem",
                                            height: 18,
                                            backgroundColor: "rgba(16, 185, 129, 0.2)",
                                            color: "#10b981",
                                        }}
                                    />
                                </Box>
                                <Typography
                                    variant="caption"
                                    color="text.secondary"
                                    sx={{ display: "block", mb: 2 }}
                                >
                                    Observation → Cause → Solution
                                </Typography>
                                <Stack spacing={1.5}>
                                    {knowledgeData.paths.map((path, index) => (
                                        <Box
                                            key={index}
                                            sx={{
                                                p: 1.5,
                                                backgroundColor: "rgba(0, 0, 0, 0.02)",
                                                borderRadius: 1,
                                                position: "relative",
                                            }}
                                        >
                                            <Box
                                                sx={{
                                                    position: "absolute",
                                                    top: 8,
                                                    left: 8,
                                                    display: "flex",
                                                    alignItems: "center",
                                                    gap: 0.5,
                                                }}
                                            >
                                                <Chip
                                                    label={`${index + 1}`}
                                                    size="small"
                                                    sx={{
                                                        fontSize: "0.7rem",
                                                        height: 20,
                                                        minWidth: 20,
                                                        "& .MuiChip-label": {
                                                            px: 0.5,
                                                        },
                                                    }}
                                                />
                                                {path.trust_score > 0 && (
                                                    <Typography
                                                        variant="caption"
                                                        sx={{
                                                            fontSize: "0.7rem",
                                                            color: "text.secondary",
                                                        }}
                                                    >
                                                        {Math.round(path.trust_score * 100)}%
                                                    </Typography>
                                                )}
                                            </Box>
                                            <Box sx={{ mt: 3 }}>
                                                {path.nodes.map((node, nodeIndex) => (
                                                    <React.Fragment key={node.id}>
                                                        <Box sx={{ mb: nodeIndex < path.nodes.length - 1 ? 2 : 0 }}>
                                                            <CompactNode node={node} />
                                                        </Box>
                                                        {nodeIndex < path.nodes.length - 1 &&
                                                            nodeIndex < path.relationships.length && (
                                                                <RelationshipArrow
                                                                    relationship={path.relationships[nodeIndex]}
                                                                />
                                                            )}
                                                    </React.Fragment>
                                                ))}
                                            </Box>
                                        </Box>
                                    ))}
                                </Stack>
                            </Box>
                        </Grid>

                        {/* Admin Report Column */}
                        <Grid item xs={12} md={6}>
                            <Box
                                sx={{
                                    p: 2,
                                    backgroundColor: "rgba(59, 130, 246, 0.03)",
                                    borderRadius: 1,
                                    border: "1px solid rgba(59, 130, 246, 0.1)",
                                    height: "100%",
                                    display: "flex",
                                    flexDirection: "column",
                                }}
                            >
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        mb: 1.5,
                                    }}
                                >
                                    <DescriptionIcon
                                        sx={{ mr: 1, color: "#3b82f6", fontSize: 18 }}
                                    />
                                    <Typography
                                        variant="subtitle2"
                                        sx={{ fontWeight: 600 }}
                                    >
                                        Admin Report
                                    </Typography>
                                    <Chip
                                        label="Client Documentation"
                                        size="small"
                                        sx={{
                                            ml: "auto",
                                            fontSize: "0.7rem",
                                            height: 18,
                                            backgroundColor: "rgba(59, 130, 246, 0.2)",
                                            color: "#3b82f6",
                                        }}
                                    />
                                </Box>
                                <Typography
                                    variant="caption"
                                    color="text.secondary"
                                    sx={{ display: "block", mb: 2 }}
                                >
                                    Comprehensive maintenance report for client records
                                </Typography>

                                <Paper
                                    elevation={0}
                                    sx={{
                                        p: 2,
                                        backgroundColor: "white",
                                        border: "1px solid rgba(0, 0, 0, 0.1)",
                                        maxHeight: 400,
                                        overflowY: "auto",
                                        flexGrow: 1,
                                        fontFamily: "monospace",
                                    }}
                                >
                                    <pre style={{
                                        margin: 0,
                                        whiteSpace: "pre-wrap",
                                        wordBreak: "break-word",
                                        fontSize: "0.8rem",
                                        lineHeight: 1.5,
                                    }}>
                                        {adminContent}
                                    </pre>
                                </Paper>

                                <Button
                                    variant="contained"
                                    size="small"
                                    startIcon={<ContentCopyIcon />}
                                    onClick={handleCopyAdmin}
                                    sx={{
                                        mt: 2,
                                        backgroundColor: "#3b82f6",
                                        "&:hover": {
                                            backgroundColor: "#2563eb",
                                        },
                                    }}
                                >
                                    Copy Report
                                </Button>
                            </Box>
                        </Grid>
                    </Grid>
                </Box>
            </Paper>

            {/* Success Snackbar */}
            <Snackbar
                open={showSuccessSnackbar}
                autoHideDuration={6000}
                onClose={handleCloseSnackbar}
                anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
            >
                <Alert
                    onClose={handleCloseSnackbar}
                    severity="success"
                    sx={{
                        backgroundColor: "primary.main",
                        color: "white",
                        "& .MuiAlert-icon": {
                            color: "white",
                        },
                    }}
                >
                    Knowledge successfully stored in database!
                </Alert>
            </Snackbar>

            {/* Error Snackbar */}
            <Snackbar
                open={showErrorSnackbar}
                autoHideDuration={6000}
                onClose={handleCloseSnackbar}
                anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
            >
                <Alert
                    onClose={handleCloseSnackbar}
                    severity="error"
                    sx={{ width: "100%" }}
                >
                    Error: {errorMessage || "Data could not be added to the database"}
                </Alert>
            </Snackbar>

            {/* Copy Success Snackbar */}
            <Snackbar
                open={copiedSnackbar}
                autoHideDuration={3000}
                onClose={() => setCopiedSnackbar(false)}
                anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
            >
                <Alert
                    onClose={() => setCopiedSnackbar(false)}
                    severity="success"
                    sx={{ width: "100%" }}
                >
                    Admin report copied to clipboard!
                </Alert>
            </Snackbar>
        </>
    );
};

export default ExtractedKnowledgeDisplay;