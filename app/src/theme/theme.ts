// theme/theme.ts
import { createTheme } from "@mui/material/styles";

const theme = createTheme({
    palette: {
        primary: {
            main: "#00843D", // Boon <PERSON>am green
            light: "#2a9959",
            dark: "#006d32",
            contrastText: "#FFFFFF",
        },
        secondary: {
            main: "#64748b", // Slate gray for secondary elements
            light: "#7b8a9d",
            dark: "#546174",
        },
        background: {
            default: "#FFFFFF",
            paper: "#FFFFFF",
        },
        text: {
            primary: "#333333",
            secondary: "#64748b",
        },
        error: {
            main: "#e53935",
        },
        warning: {
            main: "#f57c00",
        },
        info: {
            main: "#0288d1",
        },
        success: {
            main: "#00843D", // Using brand green for success too
        },
    },
    typography: {
        fontFamily: ["'Segoe UI'", "Roboto", "Arial", "sans-serif"].join(","),
        h4: {
            fontWeight: 600,
            fontSize: "1.75rem",
        },
        h5: {
            fontWeight: 600,
            fontSize: "1.25rem",
        },
        h6: {
            fontWeight: 600,
            fontSize: "1.125rem",
        },
        subtitle1: {
            fontWeight: 500,
        },
        body1: {
            fontSize: "0.9375rem",
        },
        body2: {
            fontSize: "0.875rem",
        },
        button: {
            textTransform: "none",
            fontWeight: 500,
        },
    },
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    borderRadius: 4,
                    boxShadow: "none",
                    "&:hover": {
                        boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
                    },
                },
                contained: {
                    "&:hover": {
                        backgroundColor: "#006d32", // Darker green on hover
                    },
                },
                outlined: {
                    borderColor: "#00843D",
                    "&:hover": {
                        backgroundColor: "rgba(0, 132, 61, 0.04)",
                        borderColor: "#00843D",
                    },
                },
            },
        },
        MuiPaper: {
            styleOverrides: {
                root: {
                    borderRadius: 8,
                    boxShadow: "0px 2px 8px rgba(0, 0, 0, 0.05)",
                },
                elevation1: {
                    boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                },
            },
        },
        MuiAppBar: {
            styleOverrides: {
                root: {
                    boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.1)",
                },
            },
        },
        MuiTab: {
            styleOverrides: {
                root: {
                    textTransform: "none",
                    fontWeight: 500,
                    fontSize: "0.9375rem",
                },
            },
        },
        MuiTextField: {
            styleOverrides: {
                root: {
                    "& .MuiOutlinedInput-root": {
                        "&:hover fieldset": {
                            borderColor: "#00843D",
                        },
                    },
                },
            },
        },
        MuiOutlinedInput: {
            styleOverrides: {
                root: {
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#00843D",
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#00843D",
                    },
                },
            },
        },
        MuiSwitch: {
            styleOverrides: {
                switchBase: {
                    "&.Mui-checked": {
                        color: "#00843D",
                        "& + .MuiSwitch-track": {
                            backgroundColor: "#00843D",
                        },
                    },
                },
            },
        },
        MuiDivider: {
            styleOverrides: {
                root: {
                    borderColor: "rgba(0, 0, 0, 0.06)",
                },
            },
        },
    },
});

export default theme;
