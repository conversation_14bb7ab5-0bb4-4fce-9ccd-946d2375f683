// services/doorService.ts
import axios from "./apiClient";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "/api";

export interface DoorType {
    id: string;
    model: string;
}

// Cache to prevent multiple simultaneous requests
let doorTypesCache: DoorType[] | null = null;
let doorTypesPromise: Promise<DoorType[]> | null = null;

/**
 * Fetches all door types from the API
 * Uses caching to prevent multiple simultaneous requests
 * @returns Array of door types
 */
export const fetchDoorTypes = async (): Promise<DoorType[]> => {
    // Return cached data if available
    if (doorTypesCache) {
        console.log("Returning cached door types");
        return doorTypesCache;
    }

    // If there's already a request in progress, return that promise
    if (doorTypesPromise) {
        console.log("Door types request already in progress, waiting...");
        return doorTypesPromise;
    }

    // Create new request
    console.log("Fetching door types from API...");

    doorTypesPromise = (async () => {
        try {
            const response = await axios.get(
                `${API_URL}/api/knowledge/door-types`,
                {
                    headers: {
                        // Note: Don't set Authorization here since apiClient handles it automatically
                        "Content-Type": "application/json",
                    },
                }
            );

            // Verify response structure
            const data = response.data;
            console.log("Door types API response:", data);

            if (!Array.isArray(data)) {
                console.error("API response is not an array:", data);
                return [];
            }

            // Validate each item has the expected structure
            const validatedData = data.filter((item: any) => {
                if (!item || typeof item !== "object") {
                    console.error("Invalid door type item:", item);
                    return false;
                }

                if (!item.id || !item.model) {
                    console.error("Door type missing required fields:", item);
                    return false;
                }

                return true;
            });

            console.log(
                `Successfully fetched ${validatedData.length} door types`
            );

            // Cache the result
            doorTypesCache = validatedData;

            return validatedData;
        } catch (error) {
            console.error("Error fetching door types:", error);
            throw error;
        } finally {
            // Clear the promise so future calls can make new requests
            doorTypesPromise = null;
        }
    })();

    return doorTypesPromise;
};

/**
 * Clear the door types cache (useful for testing or when data needs to be refreshed)
 */
export const clearDoorTypesCache = (): void => {
    doorTypesCache = null;
    doorTypesPromise = null;
    console.log("Door types cache cleared");
};

/**
 * Get cached door types without making a network request
 * @returns Cached door types or null if not cached
 */
export const getCachedDoorTypes = (): DoorType[] | null => {
    return doorTypesCache;
};
