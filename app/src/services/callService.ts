// services/callService.ts (modified)
import axios from "./apiClient";
import { RealtimeClient } from "@openai/realtime-api-beta";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "/api";

export let realtimeClient: RealtimeClient;
let connected = false;
let currentToken: string | null = null;
let currentConversationHistory: string | null = null;

export const connectRealtimeClient = async (
    authToken?: string,
    conversation_history?: string
): Promise<void> => {
    // If already connected with same token and no new conversation history is provided, return
    if (connected && currentToken === authToken && !conversation_history)
        return;

    try {
        // Disconnect if switching tokens or conversation history changed
        if (
            connected &&
            (currentToken !== authToken ||
                currentConversationHistory !== conversation_history)
        ) {
            await disconnectRealtimeClient();
        }

        let config: {
            client_secret: string;
            instructions: string;
            voice?: string;
            turn_detection?: { type: string };
            input_audio_transcription?: { model: string };
            modalities?: string[];
        };

        // Get configuration based on auth token
        // TEMPORARY, set authToken to anonymous for testing without login
        authToken = "anonymous";

        if (authToken) {
            // Fetch ephemeral token from your API
            const response = await axios.post(
                `${API_URL}/api/mechanics/ephemeral-token`,
                {},
                {
                    headers: {
                        Authorization: `Bearer ${authToken}`,
                        "Content-Type": "application/json",
                    },
                }
            );

            config = response.data;
            currentToken = authToken;
            currentConversationHistory = conversation_history || null;
        } else {
            throw new Error("No ephemeral token provided");
        }

        // Create realtime client with configuration
        realtimeClient = new RealtimeClient({
            apiKey: config.client_secret,
            dangerouslyAllowAPIKeyInBrowser: true,
        });

        let instructions = config.instructions;
        if (conversation_history) {
            instructions +=
                "\n You are continuing a conversation that was paused. Here is the current conversation context history: " +
                conversation_history +
                "\n Please continue the conversation from this point.";
        }

        console.log("Full instructions:", instructions);

        // Update session with configuration
        realtimeClient.updateSession({
            instructions: instructions,
            voice: config.voice || "alloy",
            turn_detection: {
                type: config.turn_detection?.type || "server_vad",
            },
            input_audio_transcription: {
                model:
                    config.input_audio_transcription?.model ||
                    "gpt-4o-transcribe",
                language: "nl",
            },
            modalities: config.modalities || ["text", "audio"],
        });

        console.log("Realtime client created with config:", config);

        // Connect to the service
        await realtimeClient.connect();
        connected = true;
        console.log("Connected to realtime service");
    } catch (error) {
        console.error("Failed to connect realtime client:", error);
        throw error;
    }
};

export const disconnectRealtimeClient = async (): Promise<void> => {
    if (!connected || !realtimeClient) return;

    try {
        await realtimeClient.disconnect();
        connected = false;
        currentToken = null;
        currentConversationHistory = null;
        console.log("Disconnected from realtime service");
    } catch (error) {
        console.error("Error disconnecting realtime client:", error);
    }
};

export const isConnected = (): boolean => connected;
