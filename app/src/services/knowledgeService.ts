import axios from "./apiClient";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "/api";
const CORE_NODE_TYPES = ["observation", "cause", "solution"];

export const uploadAudio = async (audioFile: File, token: string) => {
    console.log(
        "Uploading audio file:",
        audioFile.name,
        "Type:",
        audioFile.type
    );

    const formData = new FormData();
    formData.append("audio_file", audioFile);

    const response = await axios.post(
        `${API_URL}/api/knowledge/upload-audio`,
        formData,
        {
            headers: {
                // Note: Don't set Authorization here since apiClient handles it automatically
                "Content-Type": "multipart/form-data",
            },
        }
    );

    return response.data;
};

// Updated to accept door_model parameter and process paths
export const extractKnowledge = async (
    text: string,
    doorModelName: string | null,
    token: string
) => {
    const payload: any = { text };

    if (doorModelName) {
        payload.door_model = doorModelName;
        payload.service_order_number = "12345test"; // Temporary, replace with actual SO number
    }

    const response = await axios.post(
        `${API_URL}/api/knowledge/extract`,
        payload,
        {
            headers: {
                // Note: Don't set Authorization here since apiClient handles it automatically
                "Content-Type": "application/json",
            },
        }
    );

    const data = response.data;

    // Transform the new format to the old format that the frontend expects
    if (data.nodes && !data.paths) {
        data.paths = transformNodesToPaths(data.nodes);
    }

    // Add RESOLVED_BY relationship identifiers to each path for feedback
    if (data.paths) {
        data.paths = addResolvedByRelationships(data.paths);
    }

    return data;
};

// Helper function to add RESOLVED_BY relationship identifiers to paths
function addResolvedByRelationships(paths: any[]) {
    return paths.map((path) => {
        const resolvedByRelationships =
            path.relationships
                ?.filter((rel) => rel.type?.toUpperCase() === "RESOLVED_BY")
                .map((rel) => ({
                    source_id: rel.source_id,
                    target_id: rel.target_id,
                })) || [];

        return {
            ...path,
            resolved_by_relationships: resolvedByRelationships,
        };
    });
}

// Helper function to transform new format to old format
function transformNodesToPaths(nodes: any[]) {
    // Group nodes by path_id
    const pathGroups: Record<string, any[]> = {};

    nodes.forEach((node) => {
        // Only process observation, cause, and solution nodes
        if (CORE_NODE_TYPES.includes(node.type.toLowerCase())) {
            if (node.path_id !== null && node.path_id !== undefined) {
                const pathId = node.path_id.toString();
                if (!pathGroups[pathId]) {
                    pathGroups[pathId] = [];
                }
                pathGroups[pathId].push(transformNodeToOldFormat(node));
            }
        }
        // Skip tacit_knowledge nodes completely
    });

    // Convert path groups to paths array
    const paths = Object.values(pathGroups).map((pathNodes) => ({
        nodes: pathNodes,
        relationships: [], // No relationships in the new format
        confidence_score: 0.8, // Default confidence
    }));

    return paths;
}

// Transform ExtractedNode to old KnowledgeNode format
function transformNodeToOldFormat(extractedNode: any) {
    return {
        id: `${extractedNode.type}_${Math.random().toString(36).substr(2, 9)}`, // Generate random ID
        name: extractedNode.data.name || "",
        description: extractedNode.data.description || "",
        type: extractedNode.type.toUpperCase(),
        properties: extractedNode.data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: "system",
    };
}

// Helper function to generate paths on the frontend if needed
function generatePathsFromNodesAndRelationships(
    nodes: any[],
    relationships: any[]
) {
    // Group nodes by type
    const nodesByType: Record<string, any[]> = {
        SYMPTOM: [],
        PROBLEM: [],
        CAUSE: [],
        SOLUTION: [],
    };

    // Create a lookup map for nodes by ID
    const nodeMap: Record<string, any> = {};
    for (const node of nodes) {
        nodeMap[node.id] = node;
        const type = node.type.toUpperCase();
        if (type in nodesByType) {
            nodesByType[type].push(node);
        }
    }

    // Build a relationship map for easy lookup
    const relationshipMap: Record<string, Record<string, any>> = {};
    for (const rel of relationships) {
        if (!relationshipMap[rel.source_id]) {
            relationshipMap[rel.source_id] = {};
        }
        relationshipMap[rel.source_id][rel.target_id] = rel;
    }

    // Start building paths
    const paths = [];
    const startNodes =
        nodesByType["SYMPTOM"].length > 0
            ? nodesByType["SYMPTOM"]
            : nodesByType["PROBLEM"].length > 0
            ? nodesByType["PROBLEM"]
            : nodesByType["CAUSE"].length > 0
            ? nodesByType["CAUSE"]
            : nodesByType["SOLUTION"];

    const processedNodes = new Set();

    // Create paths starting with each potential start node
    for (const startNode of startNodes) {
        if (processedNodes.has(startNode.id)) continue;

        const pathNodes = [startNode];
        const pathRelationships = [];
        let currentNode = startNode;
        processedNodes.add(currentNode.id);

        // Try to build a path by following relationships
        for (let i = 0; i < 3; i++) {
            // Maximum path length of 4 nodes
            if (relationshipMap[currentNode.id]) {
                // Find all potential next nodes
                const nextTargets = Object.keys(
                    relationshipMap[currentNode.id]
                );
                let bestNextNode = null;
                let bestRel = null;
                let bestScore = -1;

                // Find the next node with highest trust score
                for (const targetId of nextTargets) {
                    const rel = relationshipMap[currentNode.id][targetId];
                    if (
                        nodeMap[targetId] &&
                        !processedNodes.has(targetId) &&
                        rel.trust_score > bestScore
                    ) {
                        bestNextNode = nodeMap[targetId];
                        bestRel = rel;
                        bestScore = rel.trust_score;
                    }
                }

                if (bestNextNode && bestRel) {
                    pathNodes.push(bestNextNode);
                    pathRelationships.push(bestRel);
                    processedNodes.add(bestNextNode.id);
                    currentNode = bestNextNode;
                } else {
                    break;
                }
            } else {
                break;
            }
        }

        // Add the path if it has at least 2 nodes
        if (pathNodes.length >= 2) {
            // Calculate average trust score
            const avgTrust =
                pathRelationships.length > 0
                    ? pathRelationships.reduce(
                          (sum, rel) => sum + rel.trust_score,
                          0
                      ) / pathRelationships.length
                    : 0.8;

            paths.push({
                nodes: pathNodes,
                relationships: pathRelationships,
                trust_score: avgTrust,
            });
        }
    }

    // If no multi-node paths were found, create single-type paths
    if (paths.length === 0) {
        for (const type in nodesByType) {
            if (nodesByType[type].length > 0) {
                paths.push({
                    nodes: nodesByType[type],
                    relationships: [],
                    trust_score: 0.7, // Default trust
                });
            }
        }
    }

    // If still no paths, create a catch-all path
    if (paths.length === 0 && nodes.length > 0) {
        paths.push({
            nodes: nodes.slice(0, 5), // First 5 nodes
            relationships: [],
            trust_score: 0.6,
        });
    }

    return paths;
}

export const storeExtractedKnowledge = async (
    rawExtractedData: string,
    doorModelName: string | null,
    token: string
) => {
    try {
        const payload: any = {
            text: rawExtractedData,
        };

        if (doorModelName) {
            payload.door_model = doorModelName;
        }

        const response = await axios.post(
            `${API_URL}/api/knowledge/evaluate`,
            payload,
            {
                headers: {
                    // Note: Don't set Authorization here since apiClient handles it automatically
                    "Content-Type": "application/json",
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error("Error storing extracted knowledge:", error);
        throw error;
    }
};

// Updated to match the curl example with door_model instead of door_type_id
export const extractKnowledgeWithFullText = async (
    fullText: string,
    doorModel: string | null,
    token: string
) => {
    try {
        const payload = doorModel
            ? { text: fullText, door_model: doorModel }
            : { text: fullText };

        const response = await axios.post(
            `${API_URL}/api/knowledge/extract`,
            payload,
            {
                headers: {
                    // Note: Don't set Authorization here since apiClient handles it automatically
                    "Content-Type": "application/json",
                },
            }
        );

        // Check if paths need to be generated on the frontend
        const data = response.data;
        if (!data.paths && data.nodes && data.relationships) {
            data.paths = generatePathsFromNodesAndRelationships(
                data.nodes,
                data.relationships
            );
        }

        // Add RESOLVED_BY relationship identifiers
        if (data.paths) {
            data.paths = addResolvedByRelationships(data.paths);
        }

        return data;
    } catch (error) {
        console.error("Error extracting knowledge:", error);
        throw error;
    }
};

// Updated to use door_model instead of door_type_id
export const processQuery = async (
    query: { text: string; door_model?: string | null; door_type_id?: string },
    token: string,
    serviceOrderNumber = "12345test"
) => {
    const response = await axios.post(
        `${API_URL}/api/knowledge/query`,
        query, // query.door_model now contains the model name
        serviceOrderNumber,
        {
            headers: {
                // Note: Don't set Authorization here since apiClient handles it automatically
                "Content-Type": "application/json",
            },
        }
    );

    const data = response.data;

    // Add RESOLVED_BY relationship identifiers to paths
    if (data.paths) {
        data.paths = addResolvedByRelationships(data.paths);
    }

    return data;
};

// Updated to use door_model instead of doorTypeId
export const fetchKnowledgeGraph = async (
    token: string,
    limit: number = 50,
    doorModelName?: string // Now uses model name
) => {
    try {
        let url = `${API_URL}/api/knowledge/graph?limit=${limit}`;
        if (doorModelName) {
            url += `&door_model=${encodeURIComponent(doorModelName)}`; // URL encode the model name
        }

        const response = await axios.get(url, {
            headers: {
                // Note: Don't set Authorization here since apiClient handles it automatically
                "Content-Type": "application/json",
            },
        });

        return response.data;
    } catch (error) {
        console.error("Error fetching knowledge graph:", error);
        throw error;
    }
};

// Updated to remove token parameter since apiClient handles authentication
export const fetchDoorTypes = async (token?: string) => {
    try {
        console.log("Fetching door types from knowledge service...");

        const response = await axios.get(
            `${API_URL}/api/knowledge/door-types`,
            {
                headers: {
                    // Note: Don't set Authorization here since apiClient handles it automatically
                    "Content-Type": "application/json",
                },
            }
        );

        console.log("Door types API response:", response.data);

        if (!Array.isArray(response.data)) {
            console.error("API response is not an array:", response.data);
            return [];
        }

        // Validate each item has the expected structure
        const validatedData = response.data.filter((item: any) => {
            if (!item || typeof item !== "object") {
                console.error("Invalid door type item:", item);
                return false;
            }

            if (!item.id || !item.model) {
                console.error("Door type missing required fields:", item);
                return false;
            }

            return true;
        });

        console.log(`Successfully fetched ${validatedData.length} door types`);
        return validatedData;
    } catch (error) {
        console.error("Error fetching door types:", error);
        throw error;
    }
};

// ========================= NEW SIMPLIFIED FEEDBACK API =========================

// Main relationship feedback function (replaces old complex feedback)
export const sendRelationshipFeedback = async (
    relationships: Array<{
        source_id: string;
        target_id: string;
    }>,
    feedbackType: "positive" | "negative",
    token: string
) => {
    try {
        console.log("Sending relationship feedback:", {
            relationships,
            feedbackType,
            relationshipCount: relationships.length,
        });

        const response = await axios.post(
            `${API_URL}/api/feedback/relationship-feedback`,
            {
                relationships: relationships,
                feedback_type: feedbackType,
            },
            {
                headers: {
                    // Note: Don't set Authorization here since apiClient handles it automatically
                    "Content-Type": "application/json",
                },
            }
        );

        console.log("Feedback response:", response.data);
        return response.data;
    } catch (error) {
        console.error("Error sending relationship feedback:", error);
        throw error;
    }
};

// Record successful path (simplified version)
export const recordPathSuccess = async (
    resolvedByRelationships: Array<{
        source_id: string;
        target_id: string;
    }>,
    token: string
) => {
    try {
        console.log("Recording path success:", {
            relationships: resolvedByRelationships,
            count: resolvedByRelationships.length,
        });

        const response = await axios.post(
            `${API_URL}/api/feedback/path-success`,
            {
                resolved_by_relationships: resolvedByRelationships,
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
            }
        );

        console.log("Path success recorded:", response.data);
        return response.data;
    } catch (error) {
        console.error("Error recording path success:", error);
        throw error;
    }
};

// Record failed path (simplified version)
export const recordPathFailure = async (
    resolvedByRelationships: Array<{
        source_id: string;
        target_id: string;
    }>,
    token: string
) => {
    try {
        console.log("Recording path failure:", {
            relationships: resolvedByRelationships,
            count: resolvedByRelationships.length,
        });

        const response = await axios.post(
            `${API_URL}/api/feedback/path-failure`,
            {
                resolved_by_relationships: resolvedByRelationships,
            },
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    "Content-Type": "application/json",
                },
            }
        );

        console.log("Path failure recorded:", response.data);
        return response.data;
    } catch (error) {
        console.error("Error recording path failure:", error);
        throw error;
    }
};

// Get feedback for a specific relationship
export const getRelationshipFeedback = async (
    sourceId: string,
    targetId: string,
    token: string
) => {
    try {
        const response = await axios.get(
            `${API_URL}/api/feedback/relationship-feedback/${sourceId}/${targetId}`,
            {
                headers: {
                    // Note: Don't set Authorization here since apiClient handles it automatically
                    "Content-Type": "application/json",
                },
            }
        );

        return response.data;
    } catch (error) {
        console.error("Error getting relationship feedback:", error);
        throw error;
    }
};

// ========================= LEGACY COMPATIBILITY =========================
// These functions are kept for backward compatibility but are deprecated
// fix
export const sendPathFeedback = async (
    pathId: string,
    feedbackType: "positive" | "negative",
    pathDetails: {
        nodeIds: string[];
        relationships: Array<{
            source_id: string;
            target_id: string;
        }>;
    },
    token: string
) => {
    console.warn(
        "sendPathFeedback is deprecated. Use sendRelationshipFeedback instead."
    );

    // Extract RESOLVED_BY relationships and forward to new function
    const resolvedByRels = pathDetails.relationships.filter(
        (rel) => rel.source_id && rel.target_id
    );

    if (resolvedByRels.length === 0) {
        throw new Error("No valid relationships found for feedback");
    }

    return sendRelationshipFeedback(resolvedByRels, feedbackType, token);
};
