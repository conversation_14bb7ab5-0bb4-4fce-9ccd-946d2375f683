// services/audioSessionManager.ts
import {
    realtimeClient,
    connectRealtimeClient,
    disconnectRealtimeClient,
} from "./callService";
import conversationManager from "./conversationManager";
import realtimeAudioHandler from "./realtimeAudioHandler";

export interface SessionState {
    isActive: boolean;
    isPaused: boolean;
    isRecording: boolean;
    aiModeEnabled: boolean;
    authToken: string | null;
    recordingDuration: number;
}

export interface SessionCallbacks {
    onStart?: () => void;
    onPause?: () => void;
    onResume?: () => void;
    onStop?: () => void;
    onError?: (error: string) => void;
    onRecordingStart?: () => void;
    onRecordingStop?: () => void;
}

class audioSessionManager {
    private state: SessionState = {
        isActive: false,
        isPaused: false,
        isRecording: false,
        aiModeEnabled: false,
        authToken: null,
        recordingDuration: 0,
    };

    private callbacks: SessionCallbacks = {};
    private durationTimer: NodeJS.Timeout | null = null;

    // Recording chunk management for pause/resume cycles
    private recordingChunks: Blob[] = [];
    private currentSessionStartTime: number = 0;

    // Event subscription
    public setCallbacks(callbacks: SessionCallbacks): void {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    // State getters
    public getState(): SessionState {
        return { ...this.state };
    }

    public isSessionActive(): boolean {
        return this.state.isActive;
    }

    public isSessionPaused(): boolean {
        return this.state.isPaused;
    }

    public isRecordingActive(): boolean {
        return this.state.isRecording;
    }

    public getRecordingDuration(): number {
        return this.state.recordingDuration;
    }

    // Set AI mode before starting session
    public setAiMode(enabled: boolean): void {
        this.state.aiModeEnabled = enabled;
        console.log(`AI Mode ${enabled ? "enabled" : "disabled"}`);
    }

    // Start a new session
    public async startSession(authToken?: string): Promise<boolean> {
        if (this.state.isActive && !this.state.isPaused) {
            console.log("Session already active");
            return true;
        }

        try {
            this.state.authToken = authToken || localStorage.getItem("token");

            console.log(
                `Starting ${
                    this.state.aiModeEnabled ? "AI" : "standard"
                } session`
            );

            if (this.state.aiModeEnabled) {
                // AI Call Mode
                if (this.state.isPaused) {
                    // Resume existing conversation
                    return await this.resumeSession();
                } else {
                    // Start new AI conversation
                    conversationManager.setAuthToken(
                        this.state.authToken || ""
                    );
                    conversationManager.startNewConversation();

                    // Clear any previous recording chunks for new session
                    this.recordingChunks = [];
                    this.currentSessionStartTime = Date.now();

                    const success = await realtimeAudioHandler.startCall(
                        this.state.authToken
                    );

                    if (success) {
                        this.state.isActive = true;
                        this.state.isPaused = false;

                        // Start recording
                        await this.startRecording();

                        this.callbacks.onStart?.();
                        console.log("AI session started successfully");
                        return true;
                    } else {
                        throw new Error("Failed to start AI call");
                    }
                }
            } else {
                // Standard recording mode
                this.state.isActive = true;
                this.state.isPaused = false;

                // Start recording
                await this.startRecording();

                this.callbacks.onStart?.();
                console.log("Standard recording session started");
                return true;
            }
        } catch (error) {
            console.error("Error starting session:", error);
            this.state.isActive = false;
            this.state.isPaused = false;
            this.callbacks.onError?.(`Failed to start session: ${error}`);
            return false;
        }
    }

    // Pause the current session
    public async pauseSession(): Promise<void> {
        if (!this.state.isActive || this.state.isPaused) {
            console.log("Session not active or already paused");
            return;
        }

        try {
            console.log("Pausing session...");

            // Stop recording timer
            this.stopDurationTimer();

            if (this.state.aiModeEnabled) {
                // AI Call Mode - save current recording chunk and pause conversation
                await this.saveCurrentRecordingChunk();

                // Pause the conversation (flushes messages and disconnects)
                await conversationManager.pauseConversation();

                // End the audio call
                await realtimeAudioHandler.endCall();
            } else {
                // Standard mode - just pause recording
                if (this.state.isRecording) {
                    // For standard mode, we can't really pause mid-recording
                    // So we stop it and user needs to start new recording
                    await this.stopRecording();
                }
            }

            this.state.isPaused = true;
            this.state.isRecording = false;
            this.callbacks.onPause?.();
            console.log("Session paused successfully");
        } catch (error) {
            console.error("Error pausing session:", error);
            this.callbacks.onError?.(`Failed to pause session: ${error}`);
        }
    }

    // Resume a paused session
    public async resumeSession(): Promise<void> {
        if (!this.state.isActive || !this.state.isPaused) {
            console.log("Session not active or not paused");
            return;
        }

        try {
            console.log("Resuming session...");

            if (this.state.aiModeEnabled) {
                // AI Call Mode - start fresh call with conversation history
                // Get conversation history
                const conversationHistory =
                    conversationManager.getConversationHistoryAsString();

                // Start a completely new call but with the history
                const success = await realtimeAudioHandler.startCall(
                    this.state.authToken,
                    false, // Don't skip connect
                    conversationHistory // Pass the history
                );

                if (success) {
                    // Restart recording (will create a new chunk)
                    await this.startRecording();

                    this.state.isPaused = false;
                    this.callbacks.onResume?.();
                    console.log(
                        "AI session resumed successfully with history"
                    );
                } else {
                    throw new Error("Failed to resume AI call");
                }
            } else {
                // Standard mode - start new recording
                await this.startRecording();
                this.state.isPaused = false;
                this.callbacks.onResume?.();
                console.log("Standard session resumed");
            }
        } catch (error) {
            console.error("Error resuming session:", error);
            this.callbacks.onError?.(`Failed to resume session: ${error}`);
        }
    }

    // Stop the current session
    public async stopSession(): Promise<void> {
        if (!this.state.isActive) {
            console.log("Session not active");
            return;
        }

        try {
            console.log("Stopping session...");

            // Stop duration timer
            this.stopDurationTimer();

            if (this.state.aiModeEnabled) {
                // AI Call Mode - save final recording chunk
                await this.saveCurrentRecordingChunk();

                // Flush conversation and end call
                conversationManager.flushPendingMessages();
                await realtimeAudioHandler.endCall();
            } else {
                // Standard mode - stop recording
                if (this.state.isRecording) {
                    await this.stopRecording();
                }
            }

            // Reset state
            this.state.isActive = false;
            this.state.isPaused = false;
            this.state.isRecording = false;
            this.state.recordingDuration = 0;

            this.callbacks.onStop?.();
            console.log("Session stopped successfully");
        } catch (error) {
            console.error("Error stopping session:", error);
            this.callbacks.onError?.(`Failed to stop session: ${error}`);
        }
    }

    // Save current recording chunk (for pause/resume)
    private async saveCurrentRecordingChunk(): Promise<void> {
        if (!this.state.isRecording) {
            console.log("No active recording to save");
            return;
        }

        try {
            // Wait for any remaining AI audio to finish before stopping recording
            console.log("Waiting for AI audio to finish...");
            await new Promise((resolve) => setTimeout(resolve, 3000)); // 3 second delay

            // Stop current recording
            realtimeAudioHandler.stopRecording();

            // Get the current recording blob
            const currentBlob = realtimeAudioHandler.getCurrentRecordingBlob();

            if (currentBlob && currentBlob.size > 0) {
                this.recordingChunks.push(currentBlob);
                console.log(
                    `Saved recording chunk ${this.recordingChunks.length}: ${currentBlob.size} bytes`
                );
            } else {
                console.log("No recording data to save");
            }

            this.state.isRecording = false;
            this.callbacks.onRecordingStop?.();
        } catch (error) {
            console.error("Error saving recording chunk:", error);
        }
    }

    // Start recording within the session
    private async startRecording(): Promise<void> {
        if (this.state.isRecording) {
            console.log("Recording already active");
            return;
        }

        try {
            if (this.state.aiModeEnabled) {
                // AI mode - start mixed recording
                realtimeAudioHandler.startRecording();
                realtimeAudioHandler.adjustRecordingBalance(1.0, 0.7);
            }
            // Note: Standard recording is handled by KnowledgeContext separately

            this.state.isRecording = true;
            this.startDurationTimer();
            this.callbacks.onRecordingStart?.();
            console.log("Recording started");
        } catch (error) {
            console.error("Error starting recording:", error);
            this.callbacks.onError?.(`Failed to start recording: ${error}`);
        }
    }

    // Stop recording within the session
    private async stopRecording(): Promise<void> {
        if (!this.state.isRecording) {
            console.log("Recording not active");
            return;
        }

        try {
            if (this.state.aiModeEnabled) {
                // AI mode - stop mixed recording
                realtimeAudioHandler.stopRecording();
            }

            this.state.isRecording = false;
            this.stopDurationTimer();
            this.callbacks.onRecordingStop?.();
            console.log("Recording stopped");
        } catch (error) {
            console.error("Error stopping recording:", error);
            this.callbacks.onError?.(`Failed to stop recording: ${error}`);
        }
    }

    // Duration timer management
    private startDurationTimer(): void {
        this.stopDurationTimer(); // Clear any existing timer
        this.state.recordingDuration = 0;

        this.durationTimer = setInterval(() => {
            if (this.state.isRecording) {
                this.state.recordingDuration += 1;
            }
        }, 1000);
    }

    private stopDurationTimer(): void {
        if (this.durationTimer) {
            clearInterval(this.durationTimer);
            this.durationTimer = null;
        }
    }

    // Get concatenated recording blob (combines all chunks with silence gaps)
    public async getConcatenatedRecordingBlob(): Promise<Blob | null> {
        console.log(
            `Getting concatenated blob from ${this.recordingChunks.length} chunks`
        );

        if (this.recordingChunks.length === 0) {
            console.log("No recording chunks available");
            // No chunks, get current recording and add it as a chunk for conversion
            const currentBlob = realtimeAudioHandler.getCurrentRecordingBlob();
            if (currentBlob) {
                this.recordingChunks.push(currentBlob);
                // Now fall through to the conversion logic below
            } else {
                return null;
            }
        }

        // if (this.recordingChunks.length === 1) {
        //     // Only one chunk, return it directly
        //     return this.recordingChunks[0];
        // }

        try {
            // Create audio context for concatenation
            const audioContext = new AudioContext();
            const silenceBuffer = audioContext.createBuffer(
                1,
                audioContext.sampleRate * 1,
                audioContext.sampleRate
            ); // 1 second silence

            // Decode all chunks
            const audioBuffers: AudioBuffer[] = [];

            for (const chunk of this.recordingChunks) {
                try {
                    const arrayBuffer = await chunk.arrayBuffer();
                    const audioBuffer = await audioContext.decodeAudioData(
                        arrayBuffer
                    );
                    audioBuffers.push(audioBuffer);
                } catch (error) {
                    console.error("Error decoding chunk:", error);
                }
            }

            if (audioBuffers.length === 0) {
                console.log("No valid audio buffers to concatenate");
                return null;
            }

            // Calculate total length (audio + silence gaps)
            const totalSamples =
                audioBuffers.reduce(
                    (total, buffer) => total + buffer.length,
                    0
                ) +
                (audioBuffers.length - 1) * silenceBuffer.length;

            // Create combined buffer
            const combinedBuffer = audioContext.createBuffer(
                1,
                totalSamples,
                audioContext.sampleRate
            );
            const combinedData = combinedBuffer.getChannelData(0);

            // Copy data with silence gaps
            let offset = 0;
            for (let i = 0; i < audioBuffers.length; i++) {
                const buffer = audioBuffers[i];
                const bufferData = buffer.getChannelData(0);

                // Copy audio data
                combinedData.set(bufferData, offset);
                offset += buffer.length;

                // Add silence gap (except after last chunk)
                if (i < audioBuffers.length - 1) {
                    // Silence is already zeros, so we just skip ahead
                    offset += silenceBuffer.length;
                }
            }

            // Convert back to blob
            const offlineContext = new OfflineAudioContext(
                1,
                combinedBuffer.length,
                audioContext.sampleRate
            );
            const source = offlineContext.createBufferSource();
            source.buffer = combinedBuffer;
            source.connect(offlineContext.destination);
            source.start();

            const renderedBuffer = await offlineContext.startRendering();

            // Create blob from rendered buffer
            const interleaved = this.interleaveChannels([
                renderedBuffer.getChannelData(0),
            ]);
            const dataView = this.encodeWAV(
                interleaved,
                audioContext.sampleRate
            );

            await audioContext.close();

            return new Blob([dataView], { type: "audio/wav" });
        } catch (error) {
            console.error("Error concatenating recording chunks:", error);
            // Fallback: return the first chunk if concatenation fails
            return this.recordingChunks[0] || null;
        }
    }

    // Helper methods for WAV encoding
    private interleaveChannels(channels: Float32Array[]): Float32Array {
        const length = channels[0].length;
        const result = new Float32Array(length);

        for (let i = 0; i < length; i++) {
            result[i] = channels[0][i];
        }

        return result;
    }

    private encodeWAV(samples: Float32Array, sampleRate: number): DataView {
        const buffer = new ArrayBuffer(44 + samples.length * 2);
        const view = new DataView(buffer);

        // WAV header
        const writeString = (offset: number, string: string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        writeString(0, "RIFF");
        view.setUint32(4, 36 + samples.length * 2, true);
        writeString(8, "WAVE");
        writeString(12, "fmt ");
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, 1, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, "data");
        view.setUint32(40, samples.length * 2, true);

        // Convert samples to 16-bit PCM
        let offset = 44;
        for (let i = 0; i < samples.length; i++) {
            const sample = Math.max(-1, Math.min(1, samples[i]));
            view.setInt16(offset, sample * 0x7fff, true);
            offset += 2;
        }

        return view;
    }

    // Utility methods that now use concatenated blob
    public getCurrentRecordingBlob(): Blob | null {
        // During active session, return current recording
        if (this.state.isActive && this.state.isRecording) {
            return realtimeAudioHandler.getCurrentRecordingBlob();
        }

        // After session ends, return concatenated blob
        return null; // Will be handled by getConcatenatedRecordingBlob()
    }

    public async saveRecording(filename: string): Promise<void> {
        const blob = await this.getConcatenatedRecordingBlob();

        if (!blob) {
            console.error("No recording to save");
            return;
        }

        // Create download link
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${filename}.wav`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log(`Concatenated recording saved as: ${filename}.wav`);
    }

    public formatDuration(seconds: number): string {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, "0")}`;
    }

    // Reset the manager (for demo purposes)
    public reset(): void {
        this.stopDurationTimer();
        this.recordingChunks = [];
        this.currentSessionStartTime = 0;
        this.state = {
            isActive: false,
            isPaused: false,
            isRecording: false,
            aiModeEnabled: false,
            authToken: null,
            recordingDuration: 0,
        };
        console.log("audioSessionManager reset");
    }

    // Cleanup
    public destroy(): void {
        this.stopDurationTimer();
        this.recordingChunks = [];
        this.callbacks = {};
        console.log("audioSessionManager destroyed");
    }
}

// Export singleton instance
const sessionManager = new audioSessionManager();
export default sessionManager;
