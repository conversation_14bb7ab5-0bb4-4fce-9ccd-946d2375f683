import axios from "./apiClient";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "";

export const login = async (credentials: {
    email: string;
    password: string;
}) => {
    const params = new URLSearchParams();
    params.append("username", credentials.email);
    params.append("password", credentials.password);

    const response = await axios.post(
        `${API_URL}/api/mechanics/token`,
        params,
        {
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
        }
    );

    return response.data;
};

export const getCurrentUser = async (token?: string | null) => {
    // Don't make request if no token provided
    if (!token) {
        throw new Error("No authentication token provided");
    }

    const response = await axios.get(`${API_URL}/api/mechanics/me`, {
        headers: token
            ? {
                  Authorization: `Bearer ${token}`,
              }
            : undefined,
    });

    return response.data;
};

// Helper function to check if we have a valid token
export const hasValidToken = (): boolean => {
    if (typeof window === "undefined") return false;

    const token = localStorage.getItem("token");
    return !!token;
};

// Helper function to get stored token
export const getStoredToken = (): string | null => {
    if (typeof window === "undefined") return null;

    return localStorage.getItem("token");
};

// Helper function to clear stored token
export const clearStoredToken = (): void => {
    if (typeof window === "undefined") return;

    localStorage.removeItem("token");
};
