// services/apiClient.ts
// Create this new file to handle automatic token management
import axios from "axios";
import { getStoredToken, clearStoredToken } from "./authService";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "";

// Create axios instance
const apiClient = axios.create({
    baseURL: API_URL,
});

// Helper function to check if we're in dev mode
const isDevMode = (): boolean => {
    if (typeof window === "undefined") return false;
    return localStorage.getItem("devMode") === "true";
};

// Request interceptor to add token to all requests
apiClient.interceptors.request.use(
    (config) => {
        const token = getStoredToken();

        // Add token to headers if it exists
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor to handle auth errors
apiClient.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        // If we get a 401, clear the stored token
        if (error.response?.status === 401) {
            // Don't redirect if we're in dev mode - let the AuthContext handle it
            if (!isDevMode()) {
                clearStoredToken();

                // Redirect to login only if we're not already there
                if (
                    typeof window !== "undefined" &&
                    window.location.pathname !== "/login"
                ) {
                    window.location.href = "/login";
                }
            }
        }

        return Promise.reject(error);
    }
);

export default apiClient;
