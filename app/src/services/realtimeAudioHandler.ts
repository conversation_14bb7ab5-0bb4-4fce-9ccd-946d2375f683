// services/realtimeAudioHandler.ts
import { realtimeClient, connectRealtimeClient, disconnectRealtimeClient } from './callService';
import conversationManager from './conversationManager';

class RealtimeAudioHandler {
    private audioContext: AudioContext | null = null;
    private microphoneStream: MediaStream | null = null;
    private audioProcessor: ScriptProcessorNode | null = null;
    private isCallActive: boolean = false;
    private audioWorkletNode: AudioWorkletNode | null = null;
    
    // Audio buffer for smoother playback
    private combinedAudioBuffers: Float32Array[] = [];
    private lastPlaybackTime: number = 0;
    private scheduledTime: number = 0;
    
    // Track active audio sources for cleanup
    private activeSources: Set<AudioBufferSourceNode> = new Set();
    
    // Recording functionality
    private mediaRecorder: MediaRecorder | null = null;
    private recordedChunks: Blob[] = [];
    private isRecording: boolean = false;
    
    // Mixed recording nodes
    private mixerDestination: MediaStreamDestinationNode | null = null;
    private microphoneGain: GainNode | null = null;
    private aiAudioGain: GainNode | null = null;
    private microphoneSource: MediaStreamAudioSourceNode | null = null;
    
    constructor() {
        // Event listeners will be set up when client connects
    }
    
    private setupEventListeners() {
        // Set up event listener for assistant audio
        realtimeClient.on('conversation.updated', (event) => {
            const { item, delta } = event;
            if (item?.status === 'completed') {
                if (item.role === 'assistant') {
                    console.log("Assistant:", item.formatted.transcript);
                    // Add to conversation history
                    conversationManager.addMessage('assistant', item.formatted.transcript);
                }
                else if (item.role === 'user') {
                    console.log("User:", item.formatted.transcript);
                    // Add to conversation history
                    conversationManager.addMessage('user', item.formatted.transcript);
                }
            }
            if (delta?.audio) {
                // Play audio and record it if recording is active
                this.playAudioWithMixedRecording(delta.audio);
            }
        });
        
        realtimeClient.on('conversation.interrupted', () => {
            console.log("Conversation interrupted - clearing audio buffer and handling recording");
            this.handleInterruption();
        });
    }
    
    // Handle interruptions - clear audio and mark in recording
    private handleInterruption(): void {
        // Clear the playback buffer
        this.clearAudioBuffer();
        
        // If recording, we could add a brief silence or marker
        // The natural cutoff will be reflected in the mixed stream
        if (this.isRecording && this.aiAudioGain) {
            // Briefly mute AI audio to create a clear interruption point
            this.aiAudioGain.gain.setValueAtTime(0, this.audioContext!.currentTime);
            this.aiAudioGain.gain.setValueAtTime(1, this.audioContext!.currentTime + 0.05);
        }
    }
    
    // Convert Int16Array to Float32Array
    private convertToFloat32(int16Data: Int16Array): Float32Array {
        const AUDIO_MAX_VALUE = 32768.0;
        const float32Data = new Float32Array(int16Data.length);
        for (let i = 0; i < int16Data.length; i++) {
            float32Data[i] = int16Data[i] / AUDIO_MAX_VALUE;
        }
        return float32Data;
    }
    
    // Clear audio buffer and stop all scheduled playback
    private clearAudioBuffer(): void {
        // Clear the buffer
        this.combinedAudioBuffers = [];
        
        // Stop all active sources
        this.activeSources.forEach(source => {
            try {
                source.stop();
                source.disconnect();
            } catch (error) {
                console.debug("Source already stopped:", error);
            }
        });
        this.activeSources.clear();
        
        // Reset timing
        if (this.audioContext) {
            this.scheduledTime = this.audioContext.currentTime;
            this.lastPlaybackTime = this.audioContext.currentTime;
        }
    }
    
    // Play audio and route to both speakers and recording mixer
    private playAudioWithMixedRecording(audioData: Int16Array) {
        if (!this.audioContext) return;
        
        // Convert to appropriate format
        const floatArray = this.convertToFloat32(audioData);
        
        // Create buffer and play
        const bufferSize = 24000;
        const buffer = this.audioContext.createBuffer(1, floatArray.length, bufferSize);
        buffer.getChannelData(0).set(floatArray);
        
        const source = this.audioContext.createBufferSource();
        source.buffer = buffer;
        
        // Connect to speakers (original behavior)
        source.connect(this.audioContext.destination);
        
        // If recording, also connect to the AI audio gain node for mixing
        if (this.isRecording && this.aiAudioGain) {
            const sourceForRecording = this.audioContext.createBufferSource();
            sourceForRecording.buffer = buffer;
            sourceForRecording.connect(this.aiAudioGain);
            
            // Track this source too
            this.activeSources.add(sourceForRecording);
            sourceForRecording.onended = () => {
                this.activeSources.delete(sourceForRecording);
            };
            
            // Schedule both sources at the same time
            const now = this.audioContext.currentTime;
            const startTime = Math.max(now, this.scheduledTime);
            
            source.start(startTime);
            sourceForRecording.start(startTime);
            
            // Update scheduled time for next chunk
            this.scheduledTime = startTime + buffer.duration;
        } else {
            // Original behavior when not recording
            // Track this source
            this.activeSources.add(source);
            source.onended = () => {
                this.activeSources.delete(source);
            };
            
            // Schedule as close as possible to the end of the previous chunk
            const now = this.audioContext.currentTime;
            const startTime = Math.max(now, this.scheduledTime);
            source.start(startTime);
            
            // Update scheduled time for next chunk
            this.scheduledTime = startTime + buffer.duration;
        }
    }
    
    // Setup mixed recording audio graph
    private setupMixedRecording(): void {
        if (!this.audioContext || !this.microphoneStream) return;
        
        // Create mixer destination for recording
        this.mixerDestination = this.audioContext.createMediaStreamDestination();
        
        // Create gain nodes for volume control
        this.microphoneGain = this.audioContext.createGain();
        this.aiAudioGain = this.audioContext.createGain();
        
        // Create microphone source
        this.microphoneSource = this.audioContext.createMediaStreamSource(this.microphoneStream);
        
        // Connect microphone to mixer through gain node
        this.microphoneSource.connect(this.microphoneGain);
        this.microphoneGain.connect(this.mixerDestination);
        
        // Connect AI audio gain to mixer (AI audio will be connected in playAudioWithMixedRecording)
        this.aiAudioGain.connect(this.mixerDestination);
        
        // Set initial gain levels (can be adjusted for balance)
        this.microphoneGain.gain.value = 1.0; // Full microphone volume
        this.aiAudioGain.gain.value = 0.8;    // Slightly lower AI volume to avoid dominance
        
        console.log("Mixed recording setup complete");
    }
    
    // Cleanup mixed recording nodes
    private cleanupMixedRecording(): void {
        if (this.microphoneSource) {
            this.microphoneSource.disconnect();
            this.microphoneSource = null;
        }
        
        if (this.microphoneGain) {
            this.microphoneGain.disconnect();
            this.microphoneGain = null;
        }
        
        if (this.aiAudioGain) {
            this.aiAudioGain.disconnect();
            this.aiAudioGain = null;
        }
        
        if (this.mixerDestination) {
            this.mixerDestination.disconnect();
            this.mixerDestination = null;
        }
    }
    
    public async startCall(authToken?: string, skipConnect: boolean = false, conversationHistory?: string): Promise<boolean> {
        if (this.isCallActive) {
            console.log("Call already active, ending previous call first");
            await this.endCall();
        }
        
        try {
            // Connect to OpenAI with auth token and optional conversation history
            if (!skipConnect) {
                await connectRealtimeClient(authToken, conversationHistory);
                console.log(`Connected to realtime service${conversationHistory ? ' with conversation history' : ''}`);
            }
            
            // Set up event listeners after connection
            this.setupEventListeners();
            
            // Reset audio state
            this.scheduledTime = 0;
            this.lastPlaybackTime = 0;
            this.activeSources.clear();

            const SAMPLE_RATE = 24000; // Sample rate for OpenAI, 24kHz
            
            // Create audio context
            this.audioContext = new AudioContext({ sampleRate: SAMPLE_RATE });
            
            // Get microphone stream
            this.microphoneStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: SAMPLE_RATE,
                    channelCount: 1,
                }
            });
            
            // Create audio processor
            this.audioProcessor = this.audioContext.createScriptProcessor(2048, 1, 1);
            
            // Connect audio flow
            const source = this.audioContext.createMediaStreamSource(this.microphoneStream);
            source.connect(this.audioProcessor);
            this.audioProcessor.connect(this.audioContext.destination);
            
            // Process audio
            this.audioProcessor.onaudioprocess = (event) => {
                if (!this.isCallActive) return;
                
                // Get and convert audio data
                const inputData = event.inputBuffer.getChannelData(0);
                const int16Data = new Int16Array(inputData.length);

                const AUDIO_MAX_VALUE = 32767; // Max value for Int16
                
                for (let i = 0; i < inputData.length; i++) {
                    int16Data[i] = Math.max(-1, Math.min(1, inputData[i])) * AUDIO_MAX_VALUE;
                }
                
                // Send to OpenAI
                try {
                    realtimeClient.appendInputAudio(int16Data);
                } catch (error) {
                    console.error("Error sending audio:", error);
                }
            };
            
            this.isCallActive = true;
            console.log("Audio call started successfully");
            return true;
            
        } catch (error) {
            console.error("Failed to start audio call:", error);
            return false;
        }
    }
    
    // Start mixed recording (both microphone and AI voice)
    public startRecording(): void {
        if (!this.microphoneStream || this.isRecording || !this.audioContext) return;
        
        try {
            // Setup mixed recording audio graph
            this.setupMixedRecording();
            
            if (!this.mixerDestination) {
                console.error("Failed to setup mixed recording");
                return;
            }
            
            this.recordedChunks = [];
            
            // Create MediaRecorder from the mixed stream
            this.mediaRecorder = new MediaRecorder(this.mixerDestination.stream, {
                mimeType: 'audio/webm;codecs=opus',
                audioBitsPerSecond: 128000
            });
            
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };
            
            this.mediaRecorder.start(100); // Capture in 100ms chunks
            this.isRecording = true;
            console.log("Mixed recording started (microphone + AI voice)");
        } catch (error) {
            console.error("Error starting mixed recording:", error);
        }
    }
    
    // Stop recording
    public stopRecording(): void {
        if (!this.mediaRecorder || !this.isRecording) return;
        
        this.mediaRecorder.stop();
        this.isRecording = false;
        this.cleanupMixedRecording();
        console.log("Mixed recording stopped");
    }
    
    // Save recording
    public saveRecording(filename: string): void {
        if (this.recordedChunks.length === 0) {
            console.error("No recording to save");
            return;
        }
        
        // Create blob from recorded chunks
        const blob = new Blob(this.recordedChunks, { type: 'audio/webm' });
        
        // Create download link
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${filename}.webm`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log("Mixed recording saved as:", filename);
    }
    
    // Get the current recording as a blob (useful for upload to transcription)
    public getCurrentRecordingBlob(): Blob | null {
        if (this.recordedChunks.length === 0) return null;
        return new Blob(this.recordedChunks, { type: 'audio/webm' });
    }
    
    // End the call and clean up resources
    public async endCall(): Promise<void> {
        if (!this.isCallActive) return;
        
        // Stop recording if active
        if (this.isRecording) {
            this.stopRecording();
        }
        
        // Clear audio buffer and stop all sources
        this.clearAudioBuffer();
        
        // Cleanup mixed recording
        this.cleanupMixedRecording();
        
        // Reset audio state
        this.scheduledTime = 0;
        this.lastPlaybackTime = 0;
        
        // Stop audio processing
        if (this.audioProcessor) {
            this.audioProcessor.disconnect();
            this.audioProcessor = null;
        }
        
        // Stop microphone
        if (this.microphoneStream) {
            this.microphoneStream.getTracks().forEach(track => track.stop());
            this.microphoneStream = null;
        }
        
        // Close audio context
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }
        
        // Disconnect from realtime service
        await disconnectRealtimeClient();
        
        this.isCallActive = false;
        console.log("Audio call ended");
    }
    
    // Check if call is active
    public isActive(): boolean {
        return this.isCallActive;
    }
    
    // Check if recording is active
    public isRecordingActive(): boolean {
        return this.isRecording;
    }
    
    // Get recording duration (approximate)
    public getRecordingDuration(): number {
        if (!this.isRecording || this.recordedChunks.length === 0) return 0;
        // This is an approximation - for exact duration you'd need to analyze the audio data
        return this.recordedChunks.length * 0.1; // Assuming 100ms chunks
    }
    
    // Adjust recording balance (microphone vs AI voice levels)
    public adjustRecordingBalance(micLevel: number, aiLevel: number): void {
        if (this.microphoneGain) {
            this.microphoneGain.gain.value = Math.max(0, Math.min(2, micLevel));
        }
        if (this.aiAudioGain) {
            this.aiAudioGain.gain.value = Math.max(0, Math.min(2, aiLevel));
        }
    }
}

// Export a singleton instance
const realtimeAudioHandler = new RealtimeAudioHandler();
export default realtimeAudioHandler;