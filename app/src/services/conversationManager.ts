// services/conversationManager.ts
import { realtimeClient, connectRealtimeClient, disconnectRealtimeClient } from './callService';

interface ConversationItem {
    role: 'user' | 'assistant';
    content: string;
    timestamp: number;
}

class ConversationManager {
    private conversationHistory: ConversationItem[] = [];
    private pendingUserMessages: string[] = [];
    private isUserMessagePending: boolean = false;
    private isPaused: boolean = false;
    private authToken: string | null = null;
    
    constructor() {
        // Initialize
    }
    
    // Add a message to the conversation history
    public addMessage(role: 'user' | 'assistant', content: string): void {
        if (role === 'user') {
            this.handleUserMessage(content);
        } else {
            this.handleAssistantMessage(content);
        }
    }
    
    private handleUserMessage(content: string): void {
        // If we're already tracking user messages, append to the pending array
        if (this.isUserMessagePending) {
            this.pendingUserMessages.push(content);
        } else {
            // Start tracking new user message
            this.isUserMessagePending = true;
            this.pendingUserMessages = [content];
        }
    }
    
    private handleAssistantMessage(content: string): void {
        // If there were pending user messages, combine them first
        this.flushPendingMessages();
        
        // Add the assistant message
        this.conversationHistory.push({
            role: 'assistant',
            content: content,
            timestamp: Date.now()
        });
    }
    
    // Flush any pending user messages (call this when pausing or ending)
    public flushPendingMessages(): void {
        if (this.isUserMessagePending && this.pendingUserMessages.length > 0) {
            const combinedUserMessage = this.pendingUserMessages.join(' ');
            this.conversationHistory.push({
                role: 'user',
                content: combinedUserMessage,
                timestamp: Date.now()
            });
            
            this.pendingUserMessages = [];
            this.isUserMessagePending = false;
        }
    }
    
    // Get conversation history as formatted string
    public getConversationHistoryAsString(): string {
        return this.conversationHistory.map(item => {
            return `${item.role}: ${item.content}`;
        }).join('\n');
    }
    
    // Pause the conversation
    public async pauseConversation(): Promise<boolean> {
        if (this.isPaused) return true;
        
        try {
            // Flush any pending messages
            this.flushPendingMessages();

            try {
                // Disconnect from the realtime client
                await disconnectRealtimeClient();
            } catch (error) {
                console.error("Failed to disconnect realtime client:", error);
                return false;
            }
            
            this.isPaused = true;
            console.log("Conversation paused");
            return true;
        } catch (error) {
            console.error("Error pausing conversation:", error);
            return false;
        }
    }
    
    // Resume the conversation
    public async resumeConversation(authToken?: string): Promise<boolean> {
        if (!this.isPaused) return true;
        
        try {
            // Use provided token or stored token
            const token = authToken || this.authToken;
           
            // Log the conversation history being sent
            console.log("Resuming conversation with history:");
            console.log(JSON.stringify(this.conversationHistory, null, 2));

            // Get conversation history as string
            const conversationHistoryString = this.getConversationHistoryAsString();

            // Reconnect to the realtime client with conversation history
            await connectRealtimeClient(token, conversationHistoryString);
            
            this.isPaused = false;
            console.log("Conversation resumed with history");
            return true;
        } catch (error) {
            console.error("Error resuming conversation:", error);
            return false;
        }
    }
    
    // Start a new conversation (clear history)
    public startNewConversation(): void {
        this.conversationHistory = [];
        this.pendingUserMessages = [];
        this.isUserMessagePending = false;
        this.isPaused = false;
        console.log("Started new conversation");
    }
    
    // Get conversation history
    public getHistory(): ConversationItem[] {
        return [...this.conversationHistory];
    }
    
    // Check if conversation is paused
    public isConversationPaused(): boolean {
        return this.isPaused;
    }
    
    // Set auth token for resuming
    public setAuthToken(token: string): void {
        this.authToken = token;
    }
}

// Export singleton instance
const conversationManager = new ConversationManager();
export default conversationManager;