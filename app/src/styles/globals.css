:root {
  --background: #ffffff;
  --foreground: #333333;
  --primary: #00843D;
  --primary-light: #2a9959;
  --primary-dark: #006d32;
  --grey-100: #f8fafc;
  --grey-200: #e2e8f0;
  --grey-300: #cbd5e1;
  --grey-400: #94a3b8;
  --grey-500: #64748b;
  --grey-600: #475569;
  --grey-700: #334155;
  --grey-800: #1e293b;
  --grey-900: #0f172a;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #121212;
    --foreground: #e5e5e5;
    --primary: #2a9959;
    --primary-light: #3aad69;
    --primary-dark: #00843D;
    --grey-100: #1a1a1a;
    --grey-200: #2c2c2c;
    --grey-300: #3d3d3d;
    --grey-400: #5f5f5f;
    --grey-500: #737373;
    --grey-600: #8a8a8a;
    --grey-700: #a0a0a0;
    --grey-800: #c7c7c7;
    --grey-900: #ededed;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: "Segoe UI", Roboto, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--grey-100);
}

::-webkit-scrollbar-thumb {
  background: var(--grey-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--grey-400);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Smooth transitions */
button, a, input, select, textarea {
  transition: all 0.2s ease-in-out;
}

/* Remove autofill background */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px white inset;
  transition: background-color 5000s ease-in-out 0s;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0px 1000px #1a1a1a inset;
    -webkit-text-fill-color: var(--foreground);
  }
}