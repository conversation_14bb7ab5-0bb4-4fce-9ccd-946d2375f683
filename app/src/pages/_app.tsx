// pages/_app.tsx
import React from "react";
import Head from "next/head";
import { AppProps } from "next/app";
import { CacheProvider, EmotionCache } from "@emotion/react";
import { ThemeProvider } from "@mui/material/styles";
import { CssBaseline } from "@mui/material";
import createEmotionCache from "../utils/createEmotionCache";
import theme from "../theme/theme";
import { AuthProvider } from "../contexts/AuthContext";
import { KnowledgeProvider } from "@/contexts/KnowledgeContext";

// Client-side cache, shared for the whole session of the user in the browser
const clientSideEmotionCache = createEmotionCache();

interface MyAppProps extends AppProps {
    emotionCache?: EmotionCache;
}

const MyApp: React.FC<MyAppProps> = ({
    Component,
    emotionCache = clientSideEmotionCache,
    pageProps,
}) => {
    return (
        <CacheProvider value={emotionCache}>
            <Head>
                <title>Revolving Door Knowledge System</title>
                <meta
                    name="viewport"
                    content="initial-scale=1, width=device-width"
                />
            </Head>
            <ThemeProvider theme={theme}>
                <CssBaseline />
                <AuthProvider>
                    <KnowledgeProvider>
                        <Component {...pageProps} />
                    </KnowledgeProvider>
                </AuthProvider>
            </ThemeProvider>
        </CacheProvider>
    );
};

export default MyApp;
