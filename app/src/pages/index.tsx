// pages/index.tsx
import dynamic from "next/dynamic";
import ProtectedRoute from "@/components/ProtectedRoute";

// Dynamically import the dashboard component with SSR disabled
const DashboardComponent = dynamic(
    () => import("../components/Dashboard/DashboardComponent"),
    { ssr: false }
);

const Dashboard = () => {
    return (
        <ProtectedRoute requireAuth={true}>
            <DashboardComponent />
        </ProtectedRoute>
    );
};

export default Dashboard;
