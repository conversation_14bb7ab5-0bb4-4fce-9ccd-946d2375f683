// pages/login.tsx
import React, { useState } from "react";
import { useRouter } from "next/router";
import {
    Box,
    Card,
    CardContent,
    TextField,
    Button,
    Typography,
    Alert,
    CircularProgress,
    Chip,
    Container,
} from "@mui/material";
import { useAuth } from "../contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";

const LoginPage: React.FC = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { login, error, loading, user, isDevMode, setDevMode } = useAuth();
    const router = useRouter();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!email || !password) return;

        setIsSubmitting(true);
        try {
            await login({ email, password });
            // Navigation is handled in the login function
        } catch (error) {
            // Error is handled in AuthContext
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleSkipLogin = async () => {
        console.log("Skip login clicked, attempting auto-login for dev user.");
        setIsSubmitting(true);
        try {
            await login({ email: "<EMAIL>", password: "dev123" });
            // Navigation to the dashboard is handled by the login function
        } catch (err) {
            // The error state will be set in the AuthContext and displayed
            console.error("Developer auto-login failed:", err);
        } finally {
            setIsSubmitting(false);
        }
    };

    // Show loading while checking existing auth
    if (loading) {
        return (
            <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                minHeight="100vh"
            >
                <CircularProgress />
            </Box>
        );
    }

    return (
        <ProtectedRoute requireAuth={false}>
            <Container maxWidth="sm">
                <Box
                    display="flex"
                    flexDirection="column"
                    justifyContent="center"
                    alignItems="center"
                    minHeight="100vh"
                    py={4}
                >
                    <Card sx={{ width: "100%", maxWidth: 400 }}>
                        <CardContent sx={{ p: 4 }}>
                            <Box
                                textAlign="center"
                                mb={3}
                            >
                                <Typography
                                    variant="h4"
                                    component="h1"
                                    gutterBottom
                                >
                                    Boon Brain
                                </Typography>
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                >
                                    Sign in to access the knowledge system
                                </Typography>
                            </Box>

                            {/* Development Mode Indicator */}
                            {process.env.NODE_ENV === "development" && (
                                <Box mb={2}>
                                    <Alert
                                        severity="info"
                                        sx={{ mb: 2 }}
                                    >
                                        <Typography variant="body2">
                                            <strong>Development Mode:</strong>{" "}
                                            You can access the system without
                                            logging in, or use the demo
                                            credentials below.
                                        </Typography>
                                    </Alert>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            gap: 1,
                                            flexWrap: "wrap",
                                        }}
                                    >
                                        <Chip
                                            label="Email: <EMAIL>"
                                            size="small"
                                            variant="outlined"
                                            onClick={() =>
                                                setEmail("<EMAIL>")
                                            }
                                            sx={{ cursor: "pointer" }}
                                        />
                                        <Chip
                                            label="Password: admin123"
                                            size="small"
                                            variant="outlined"
                                            onClick={() =>
                                                setPassword("admin123")
                                            }
                                            sx={{ cursor: "pointer" }}
                                        />
                                    </Box>
                                </Box>
                            )}

                            {error && (
                                <Alert
                                    severity="error"
                                    sx={{ mb: 2 }}
                                >
                                    {error}
                                </Alert>
                            )}

                            <form onSubmit={handleSubmit}>
                                <TextField
                                    fullWidth
                                    label="Email"
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    margin="normal"
                                    required
                                    disabled={isSubmitting}
                                />
                                <TextField
                                    fullWidth
                                    label="Password"
                                    type="password"
                                    value={password}
                                    onChange={(e) =>
                                        setPassword(e.target.value)
                                    }
                                    margin="normal"
                                    required
                                    disabled={isSubmitting}
                                />
                                <Button
                                    type="submit"
                                    fullWidth
                                    variant="contained"
                                    sx={{ mt: 3, mb: 2 }}
                                    disabled={
                                        isSubmitting || !email || !password
                                    }
                                >
                                    {isSubmitting ? (
                                        <>
                                            <CircularProgress
                                                size={20}
                                                sx={{ mr: 1 }}
                                            />
                                            Signing in...
                                        </>
                                    ) : (
                                        "Sign In"
                                    )}
                                </Button>
                            </form>

                            {/* Skip Login Button for Development */}
                            {process.env.NODE_ENV === "development" && (
                                <Button
                                    fullWidth
                                    variant="outlined"
                                    onClick={handleSkipLogin}
                                    sx={{ mt: 1 }}
                                    disabled={isSubmitting}
                                >
                                    Skip Login (Dev Mode)
                                </Button>
                            )}
                        </CardContent>
                    </Card>
                </Box>
            </Container>
        </ProtectedRoute>
    );
};

export default LoginPage;
