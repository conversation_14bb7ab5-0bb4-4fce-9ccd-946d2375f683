// src/mocks/initialData.ts
import { v4 as uuidv4 } from "uuid";
import {
    DoorComponent,
    TechnicalTerm,
    KnowledgeItem,
    DoorModel,
    Mechanic,
} from "../types/models";

// Technical terms extracted from the example data
export const technicalTerms: TechnicalTerm[] = [
    {
        id: uuidv4(),
        term: "box",
        englishTranslation: "box",
        definition:
            "A device or module in the door system used for diagnostics or control, sometimes removed during incidents.",
        frequency: 2,
    },
    {
        id: uuidv4(),
        term: "sensoren",
        englishTranslation: "sensors",
        definition:
            "Devices that detect presence, movement, or other parameters related to door operation.",
        frequency: 14,
    },
    {
        id: uuidv4(),
        term: "motor",
        englishTranslation: "motor",
        definition:
            "The mechanical drive component that powers door movement.",
        frequency: 2,
    },
    {
        id: uuidv4(),
        term: "resetpunt",
        englishTranslation: "reset point",
        definition:
            "A point or function used to reset the door system or its components.",
        frequency: 2,
    },
    {
        id: uuidv4(),
        term: "rustpositie",
        englishTranslation: "rest position",
        definition:
            "The default or idle position of the door when not in operation.",
        frequency: 9,
    },
];

// Door components
export const doorComponents: DoorComponent[] = [
    {
        id: uuidv4(),
        name: "Sensor System",
        description:
            "The system of sensors that detect presence and control door movement",
        technicalTerms: technicalTerms.filter((term) =>
            ["sensoren", "detectieveld", "flatscan", "EBS"].includes(term.term)
        ),
    },
    {
        id: uuidv4(),
        name: "Motor Assembly",
        description:
            "The motor and related components that drive door movement",
        technicalTerms: technicalTerms.filter((term) =>
            ["motor", "acceleratie", "deceleratie"].includes(term.term)
        ),
    },
    {
        id: uuidv4(),
        name: "Control Box",
        description: "The main control unit that manages door operations",
        technicalTerms: technicalTerms.filter((term) =>
            ["box", "resetpunt", "errorcode"].includes(term.term)
        ),
    },
    {
        id: uuidv4(),
        name: "Position System",
        description: "System that controls and monitors door position",
        technicalTerms: technicalTerms.filter((term) =>
            ["rustpositie", "kopieerpositie", "learning slows"].includes(
                term.term
            )
        ),
    },
];

// Door models
export const doorModels: DoorModel[] = [
    {
        id: uuidv4(),
        name: "SW-750",
        manufacturer: "SwingMaster",
        description: "Heavy-duty swinging door system for commercial use",
        imageUrl: "/door-model-1.jpg",
    },
    {
        id: uuidv4(),
        name: "SL-500",
        manufacturer: "SlideWorks",
        description: "Sliding door system with advanced sensor capabilities",
        imageUrl: "/door-model-2.jpg",
    },
    {
        id: uuidv4(),
        name: "RD-350",
        manufacturer: "RevolDoor",
        description: "Medium-capacity revolving door for office buildings",
        imageUrl: "/door-model-3.jpg",
    },
];

// Sample mechanics
export const mechanics: Mechanic[] = [
    {
        id: uuidv4(),
        name: "Jan Jansen",
        experienceYears: 15,
        specialties: ["Sliding Doors", "Sensor Calibration"],
    },
    {
        id: uuidv4(),
        name: "Pieter de Vries",
        experienceYears: 8,
        specialties: ["Motor Repairs", "Revolving Doors"],
    },
    {
        id: uuidv4(),
        name: "Emma Bakker",
        experienceYears: 3,
        specialties: ["Control Systems", "Software Updates"],
    },
];

// Prepopulated knowledge items based on the extraction report
export const knowledgeItems: KnowledgeItem[] = [
    {
        id: uuidv4(),
        componentId: doorComponents[0].id, // Sensor System
        symptom:
            "Boxes in the service vehicle are broken and not used during incidents.",
        problem:
            "The incident boxes intended for use during accidents or incidents are damaged and thus not utilized.",
        cause: "The boxes have become damaged over time and have not been maintained or replaced.",
        solution:
            "Repair or replace the incident boxes so they can be used properly when needed.",
        trust: 87,
        mechanicId: mechanics[0].id,
        timestamp: new Date(
            Date.now() - 7 * 24 * 60 * 60 * 1000
        ).toISOString(), // 7 days ago
        doorModelId: doorModels[0].id,
    },
    {
        id: uuidv4(),
        componentId: doorComponents[0].id, // Sensor System
        symptom:
            "Difficulty in quickly diagnosing and resolving door malfunctions or sensor issues.",
        problem:
            "Lack of easily accessible, comprehensive, and up-to-date technical knowledge and documentation.",
        cause: "Insufficient or outdated manuals in the vehicle; reliance on physical books instead of an online knowledge base.",
        solution:
            "Develop and maintain an online knowledge base with detailed manuals, sensor information, and troubleshooting guides to enable faster diagnosis and repair.",
        trust: 92,
        mechanicId: mechanics[1].id,
        timestamp: new Date(
            Date.now() - 14 * 24 * 60 * 60 * 1000
        ).toISOString(), // 14 days ago
        doorModelId: doorModels[1].id,
    },
    {
        id: uuidv4(),
        componentId: doorComponents[3].id, // Position System
        symptom:
            "Door does not stop consistently at the designated rest position; sometimes stops too early or too late.",
        problem:
            "The door's rest position learning and stopping behavior is not calibrated correctly.",
        cause: "Incorrect settings for the door's stop position, acceleration, deceleration, and timing parameters.",
        solution:
            "Manually set the rest position and perform the learning cycle where the door stops three times at the same point; adjust acceleration, deceleration, and timing parameters based on experience to ensure consistent stopping.",
        trust: 85,
        mechanicId: mechanics[2].id,
        timestamp: new Date(
            Date.now() - 3 * 24 * 60 * 60 * 1000
        ).toISOString(), // 3 days ago
        doorModelId: doorModels[2].id,
    },
    {
        id: uuidv4(),
        componentId: doorComponents[1].id, // Motor Assembly
        symptom:
            "Door movement is either too slow to reach the rest position or overshoots it.",
        problem:
            "Improper deceleration and timing settings causing inconsistent door positioning.",
        cause: "Deceleration time too long or too short, causing the door to miss the rest position or overshoot.",
        solution:
            "Adjust deceleration and action-relation timing values to optimize door stopping behavior; use experience and door-specific characteristics to fine-tune.",
        trust: 78,
        mechanicId: mechanics[0].id,
        timestamp: new Date(
            Date.now() - 5 * 24 * 60 * 60 * 1000
        ).toISOString(), // 5 days ago
        doorModelId: doorModels[0].id,
    },
];

// Initialize the entire mock database
export const initialDatabase = {
    technicalTerms,
    doorComponents,
    doorModels,
    mechanics,
    knowledgeItems,
};
