// hooks/useDoorTypes.ts
import { useState, useEffect } from "react";
import { useAuth } from "../contexts/AuthContext";
import { fetchDoorTypes, getCachedDoorTypes } from "../services/doorService";

export interface DoorType {
    id: string;
    model: string;
}

export const useDoorTypes = () => {
    const { user, isDevMode } = useAuth();
    const [doorTypes, setDoorTypes] = useState<DoorType[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Check if we should make API calls
    const shouldFetch = () => {
        // In dev mode or with authenticated user
        return isDevMode || !!user;
    };

    useEffect(() => {
        // Don't fetch if not authenticated
        if (!shouldFetch()) {
            console.log("Skipping door types fetch - not authenticated");
            return;
        }

        // Check for cached data first
        const cached = getCachedDoorTypes();
        if (cached) {
            console.log("Using cached door types");
            setDoorTypes(cached);
            return;
        }

        // Fetch door types
        const loadDoorTypes = async () => {
            // Don't set loading if we already have data (prevents flickering)
            if (doorTypes.length === 0) {
                setLoading(true);
            }
            setError(null);

            try {
                console.log("Loading door types via hook...");
                const types = await fetchDoorTypes();
                setDoorTypes(types);
            } catch (err) {
                console.error("Error loading door types:", err);
                setError(
                    err instanceof Error
                        ? err.message
                        : "Failed to load door types"
                );
            } finally {
                setLoading(false);
            }
        };

        loadDoorTypes();
    }, [user, isDevMode]); // Only depend on authentication state

    return {
        doorTypes,
        loading,
        error,
        refetch: async () => {
            if (!shouldFetch()) return;

            setLoading(true);
            setError(null);

            try {
                const types = await fetchDoorTypes();
                setDoorTypes(types);
            } catch (err) {
                setError(
                    err instanceof Error
                        ? err.message
                        : "Failed to load door types"
                );
            } finally {
                setLoading(false);
            }
        },
    };
};
