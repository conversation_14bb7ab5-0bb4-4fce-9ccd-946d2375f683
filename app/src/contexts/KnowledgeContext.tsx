// contexts/KnowledgeContext.tsx
import React, {
    createContext,
    useState,
    useContext,
    useCallback,
} from "react";
import {
    uploadAudio,
    extractKnowledge,
    processQuery,
    fetchKnowledgeGraph,
    fetchDoorTypes,
    storeExtractedKnowledge,
} from "../services/knowledgeService";
import { useAuth } from "./AuthContext";
import realtimeAudioHandler from "../services/realtimeAudioHandler";
import audioSessionManager from "../services/audioSessionManager";

interface KnowledgeContextType {
    transcription: string;
    extractedKnowledge: any | null;
    queryResult: any | null;
    isRecording: boolean;
    isProcessing: boolean;
    error: string | null;
    doorModel: string | null;
    doorModelName: string | null;
    doorTypeId: string | null;
    doorTypes: { id: string; model: string }[];
    knowledgeGraph: any | null;
    setDoorModel: (model: string | null, modelName?: string | null) => void;
    setDoorTypeId: (id: string | null) => void;
    startRecording: () => void;
    stopRecording: () => Promise<void>;
    uploadAudioFile: (file: File) => Promise<void>;
    uploadMixedAudio: () => Promise<void>;
    extractFromText: (text: string, doorModel?: string) => Promise<void>;
    processAsQuery: (text: string, doorModel?: string) => Promise<void>;
    loadKnowledgeGraph: () => Promise<void>;
    loadDoorTypes: () => Promise<void>;
    resetState: () => void;
    storeKnowledge: (rawData: string) => Promise<void>;
}

const KnowledgeContext = createContext<KnowledgeContextType | undefined>(
    undefined
);

export const KnowledgeProvider: React.FC<{ children: React.ReactNode }> = ({
    children,
}) => {
    const { user, isDevMode } = useAuth();
    const [transcription, setTranscription] = useState<string>("");
    const [extractedKnowledge, setExtractedKnowledge] = useState<any | null>(
        null
    );
    const [queryResult, setQueryResult] = useState<any | null>(null);
    const [isRecording, setIsRecording] = useState<boolean>(false);
    const [isProcessing, setIsProcessing] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
        null
    );
    const [audioChunks, setAudioChunks] = useState<Blob[]>([]);
    const [mimeType, setMimeType] = useState<string>("audio/mp3");

    // Keep both state variables synchronized
    const [doorModel, setDoorModelState] = useState<string | null>(null);
    const [doorTypeId, setDoorTypeIdState] = useState<string | null>(null);
    const [doorModelName, setDoorModelNameState] = useState<string | null>(
        null
    );

    // Synchronized setters
    const setDoorModel = (model: string | null, modelName?: string | null) => {
        setDoorModelState(model);
        setDoorTypeIdState(model); // Keep both in sync
        if (modelName !== undefined) {
            setDoorModelNameState(modelName);
        }
    };

    const setDoorTypeId = (id: string | null) => {
        setDoorTypeIdState(id);
        setDoorModelState(id); // Keep both in sync
    };

    const [doorTypes, setDoorTypes] = useState<
        { id: string; model: string }[]
    >([]);
    const [knowledgeGraph, setKnowledgeGraph] = useState<any | null>(null);

    // Helper function to check if we should make API calls
    const shouldMakeApiCalls = () => {
        // In dev mode, we can make API calls (they'll work or fail gracefully)
        if (isDevMode) return true;

        // For normal mode, we need a valid user
        return !!user;
    };

    // Helper function to get auth token
    const getAuthToken = () => {
        if (isDevMode) {
            // In dev mode, return empty string (API client will handle dev mode)
            return "";
        }
        return localStorage.getItem("token") || "";
    };

    // Function to get the best supported MIME type
    const getBestSupportedMimeType = () => {
        // Preferred audio formats in order of preference
        const preferredTypes = [
            "audio/mp3",
            "audio/wav",
            "audio/webm;codecs=opus",
            "audio/ogg;codecs=opus",
            "audio/webm",
            "audio/ogg",
        ];

        // Find the first supported type
        for (const type of preferredTypes) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }
        return "audio/webm"; // Default fallback
    };

    // Helper function to determine audio source priority
    const getAudioSourceInfo = () => {
        const hasMicrophoneRecording = audioChunks.length > 0;

        return {
            hasMicrophoneRecording,
            source: hasMicrophoneRecording ? "microphone" : "none",
        };
    };

    const startRecording = async () => {
        // Check if we're in AI mode - if so, delegate to audioSessionManager
        if (audioSessionManager.getState().aiModeEnabled) {
            console.log(
                "AI mode enabled - recording managed by audioSessionManager"
            );
            return;
        }

        try {
            setError(null);
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: true,
            });

            // Get the best supported audio format for this browser
            const bestMimeType = getBestSupportedMimeType();
            setMimeType(bestMimeType);

            console.log(`Using audio format: ${bestMimeType}`);

            // Clear existing chunks
            setAudioChunks([]);

            // Create recorder with the best supported format
            const recorder = new MediaRecorder(stream, {
                mimeType: bestMimeType,
                audioBitsPerSecond: 128000, // Use a standard bitrate
            });

            // Set up data handling
            recorder.ondataavailable = (e) => {
                console.log(`Microphone data available: ${e.data.size} bytes`);
                if (e.data.size > 0) {
                    setAudioChunks((chunks) => {
                        const newChunks = [...chunks, e.data];
                        console.log(
                            `Total microphone chunks: ${newChunks.length}`
                        );
                        return newChunks;
                    });
                }
            };

            // Handle recording errors
            recorder.onerror = (event) => {
                console.error("Microphone MediaRecorder error:", event);
            };

            // Start recording with timeslice for better data capture
            recorder.start(250); // Get data every 250ms

            setMediaRecorder(recorder);
            setIsRecording(true);

            console.log("Microphone recording started successfully");
        } catch (err) {
            console.error("Recording error:", err);
            setError("Could not access microphone");
        }
    };

    const stopRecording = async () => {
        console.log("Stopping recording...");

        // Check if we're in AI mode - if so, delegate to audioSessionManager
        if (audioSessionManager.getState().aiModeEnabled) {
            console.log(
                "AI mode enabled - recording managed by audioSessionManager"
            );
            return;
        }

        // Check what audio sources are available
        const audioInfo = getAudioSourceInfo();
        console.log("Audio source info:", audioInfo);

        // Process microphone-only recording with automatic transcription
        if (!mediaRecorder) {
            console.log("No recording available to stop");
            return;
        }

        return new Promise<void>((resolve, reject) => {
            // Create a final data handler for the stop event
            mediaRecorder.onstop = async () => {
                // Add a small delay to ensure all chunks are processed
                setTimeout(async () => {
                    try {
                        setIsProcessing(true);

                        // Log the collected chunks to help with debugging
                        console.log(`Have ${audioChunks.length} audio chunks`);
                        let totalSize = 0;
                        audioChunks.forEach((chunk, i) => {
                            console.log(
                                `Chunk ${i} size: ${chunk.size} bytes`
                            );
                            totalSize += chunk.size;
                        });
                        console.log(
                            `Total audio data size: ${totalSize} bytes`
                        );

                        if (totalSize === 0 || audioChunks.length === 0) {
                            setError(
                                "No audio was captured. Please try again and speak into your microphone."
                            );
                            setIsProcessing(false);
                            reject(new Error("No audio data captured"));
                            return;
                        }

                        // Extract the base format for the filename extension
                        const fileExtension = mimeType
                            .split("/")[1]
                            .split(";")[0];

                        // Create blob with all audio chunks
                        const audioBlob = new Blob(audioChunks, {
                            type: mimeType,
                        });

                        console.log(
                            `Created microphone-only blob of size: ${audioBlob.size} bytes`
                        );

                        // Safety check for empty blob
                        if (audioBlob.size === 0) {
                            setError(
                                "Failed to capture audio. Please try again."
                            );
                            setIsProcessing(false);
                            reject(new Error("Empty audio blob"));
                            return;
                        }

                        const audioFile = new File(
                            [audioBlob],
                            `recording.${fileExtension}`,
                            {
                                type: mimeType,
                            }
                        );

                        console.log(
                            `Created ${mimeType} file named recording.${fileExtension} (${audioFile.size} bytes)`
                        );

                        // Upload audio for transcription (automatic)
                        console.log(
                            "Uploading microphone-only audio file for automatic transcription..."
                        );
                        const token = getAuthToken();
                        const { text } = await uploadAudio(audioFile, token);

                        setTranscription(text);

                        // Extract knowledge from transcription (automatic)
                        await extractFromText(text, doorModel || undefined);

                        setIsProcessing(false);
                        resolve();
                    } catch (err) {
                        console.error("Processing error:", err);
                        setError("Failed to process recording");
                        setIsProcessing(false);
                        reject(err);
                    }
                }, 500); // 500ms delay to ensure all data is processed
            };

            // Request one final chunk of data before stopping
            if (mediaRecorder.state === "recording") {
                try {
                    // Force a final dataavailable event
                    mediaRecorder.requestData();

                    // Then stop the recording after a short delay
                    setTimeout(() => {
                        mediaRecorder.stop();
                        setIsRecording(false);

                        // Stop all audio tracks
                        mediaRecorder.stream
                            .getTracks()
                            .forEach((track) => track.stop());
                    }, 100);
                } catch (e) {
                    console.error("Error stopping recorder:", e);
                    mediaRecorder.stop();
                    setIsRecording(false);

                    // Stop all audio tracks
                    mediaRecorder.stream
                        .getTracks()
                        .forEach((track) => track.stop());
                }
            } else {
                setError("Recording was not active");
                reject(new Error("Recording was not active"));
            }
        });
    };

    // Upload mixed audio recording for transcription
    const uploadMixedAudio = async () => {
        console.log("uploadMixedAudio called");
        try {
            setIsProcessing(true);
            setError(null);

            // Get the concatenated recording blob from audioSessionManager
            const concatenatedBlob =
                await audioSessionManager.getConcatenatedRecordingBlob();
            console.log("Concatenated blob:", concatenatedBlob?.size, "bytes");

            if (!concatenatedBlob) {
                // Fallback to current recording blob
                const currentBlob =
                    realtimeAudioHandler.getCurrentRecordingBlob();
                if (!currentBlob) {
                    throw new Error("No mixed recording available");
                }
                console.log(
                    "Using current recording blob as fallback:",
                    currentBlob.size,
                    "bytes"
                );
            }

            const blobToUse =
                concatenatedBlob ||
                realtimeAudioHandler.getCurrentRecordingBlob();
            if (!blobToUse) {
                throw new Error("No recording blob available");
            }

            // Create a file from the blob
            const timestamp = new Date()
                .toISOString()
                .replace(/[:.]/g, "-")
                .slice(0, -5);
            const isWav = concatenatedBlob !== null;
            const filename = `mixed-recording-${
                isWav ? "concatenated-" : ""
            }${timestamp}.${isWav ? "wav" : "webm"}`;
            const mixedFile = new File([blobToUse], filename, {
                type: isWav ? "audio/wav" : "audio/webm",
            });

            console.log(
                `Uploading mixed audio file: ${filename} (${mixedFile.size} bytes)`
            );

            // Upload the mixed audio for transcription
            const token = getAuthToken();
            const { text } = await uploadAudio(mixedFile, token);

            console.log("Transcription result:", text?.length, "characters");
            setTranscription(text);

            // Extract knowledge from transcription
            console.log("Starting knowledge extraction...");
            await extractFromText(text, doorModel || null);

            setIsProcessing(false);
            console.log(
                "Mixed audio transcription and knowledge extraction completed successfully"
            );
        } catch (err) {
            console.error("Failed to process mixed audio:", err);
            setError("Failed to process mixed audio recording");
            setIsProcessing(false);
            throw err; // Re-throw so calling code can handle the error
        }
    };

    const uploadAudioFile = async (file: File) => {
        try {
            setIsProcessing(true);
            setError(null);

            // Check if this is a mixed recording file or regular file upload
            const isMixedRecording =
                file.name.includes("mixed-recording") ||
                file.type === "audio/webm" ||
                file.type === "audio/wav";

            if (isMixedRecording) {
                console.log(
                    `Uploading mixed audio file: ${file.name} (${file.size} bytes)`
                );
            } else {
                console.log(
                    `Uploading regular audio file: ${file.name} (${file.size} bytes)`
                );
            }

            const token = getAuthToken();
            const { text } = await uploadAudio(file, token);

            setTranscription(text);

            // Extract knowledge from transcription (now passing doorModel)
            await extractFromText(text, doorModel || null);

            setIsProcessing(false);
        } catch (err) {
            setError("Failed to process audio file");
            setIsProcessing(false);
        }
    };

    // Updated to accept doorModel parameter
    const extractFromText = async (text: string, doorModel?: string) => {
        try {
            setIsProcessing(true);
            setError(null);

            const token = getAuthToken();
            // Use doorModelName if available, fall back to the ID if necessary
            const modelNameToUse = doorModelName || doorModel || null;

            // Log what's being sent for debugging
            console.log(`Sending door_model: ${modelNameToUse}`);

            const result = await extractKnowledge(text, modelNameToUse, token);

            // Store the extraction results including paths if available
            setExtractedKnowledge(result);

            // Refresh knowledge graph if extraction was successful
            await loadKnowledgeGraph();

            setIsProcessing(false);
        } catch (err) {
            setError("Failed to extract knowledge");
            setIsProcessing(false);
        }
    };

    // Updated to use doorModel but also fallback to doorTypeId
    const processAsQuery = async (text: string, doorModel?: string) => {
        try {
            setIsProcessing(true);
            setError(null);

            const token = getAuthToken();
            // Use doorModelName if available, fall back to the ID if necessary
            const modelNameToUse = doorModelName || doorModel || null;

            // Log what's being sent for debugging
            console.log(`Sending door_model to query: ${modelNameToUse}`);

            const result = await processQuery(
                { text, door_model: modelNameToUse },
                token
            );

            setQueryResult(result);
            setIsProcessing(false);
        } catch (err) {
            setError("Failed to process query");
            setIsProcessing(false);
        }
    };

    const loadKnowledgeGraph = useCallback(async () => {
        // Don't make API calls if not authenticated
        return;
        if (!shouldMakeApiCalls()) {
            console.log("Skipping knowledge graph load - not authenticated");
            return;
        }

        try {
            const token = getAuthToken();

            // Use doorModelName if available, fall back to the ID if necessary
            const modelNameToUse = doorModelName || doorModel || null;

            // Log what's being sent for debugging
            console.log(`Loading graph with door_model: ${modelNameToUse}`);

            const graphData = await fetchKnowledgeGraph(
                token,
                50,
                modelNameToUse || undefined
            );

            setKnowledgeGraph(graphData);
        } catch (err) {
            console.error("Error loading knowledge graph:", err);
        }
    }, [doorModel, doorModelName, doorTypeId, user, isDevMode]);

    const loadDoorTypes = useCallback(async () => {
        // Don't make API calls if not authenticated
        if (!shouldMakeApiCalls()) {
            console.log("Skipping door types load - not authenticated");
            return;
        }

        // Prevent duplicate calls
        if (doorTypes.length > 0) {
            console.log("Door types already loaded, skipping");
            return;
        }

        try {
            console.log("Loading door types...");
            const token = getAuthToken();
            const types = await fetchDoorTypes(token);
            console.log("Door types loaded:", types);
            setDoorTypes(types);
        } catch (err) {
            console.error("Error loading door types:", err);
            // Don't set error state for door types as it's not critical
        }
    }, [user, isDevMode, doorTypes.length]);

    const resetState = () => {
        setTranscription("");
        setExtractedKnowledge(null);
        setQueryResult(null);
        setIsRecording(false);
        setIsProcessing(false);
        setError(null);
        setAudioChunks([]);
        // We don't reset doorModel/doorTypeId or doorTypes as those are user preferences
    };

    const storeKnowledge = async (rawData: string) => {
        try {
            setIsProcessing(true);
            setError(null);

            const token = getAuthToken();
            // Use doorModelName if available, fall back to the ID if necessary
            const modelNameToUse = doorModelName || doorModel || null;

            console.log(`Sending door_model to store: ${modelNameToUse}`);

            await storeExtractedKnowledge(rawData, modelNameToUse, token);

            setIsProcessing(false);
        } catch (err) {
            setError("Failed to store knowledge");
            setIsProcessing(false);
            throw err;
        }
    };

    return (
        <KnowledgeContext.Provider
            value={{
                transcription,
                extractedKnowledge,
                queryResult,
                isRecording,
                isProcessing,
                error,
                doorModel,
                doorModelName,
                doorTypeId, // Provide both properties
                doorTypes,
                knowledgeGraph,
                setDoorModel,
                setDoorTypeId, // Provide both methods
                startRecording,
                stopRecording,
                uploadAudioFile,
                uploadMixedAudio, // Export the new method
                extractFromText,
                processAsQuery,
                loadKnowledgeGraph,
                loadDoorTypes,
                resetState,
                storeKnowledge,
            }}
        >
            {children}
        </KnowledgeContext.Provider>
    );
};

export const useKnowledge = () => {
    const context = useContext(KnowledgeContext);
    if (context === undefined) {
        throw new Error(
            "useKnowledge must be used within a KnowledgeProvider"
        );
    }
    return context;
};
