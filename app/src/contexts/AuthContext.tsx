// contexts/AuthContext.tsx
import React, { createContext, useState, useEffect, useContext } from "react";
import { useRouter } from "next/router";
import { login, getCurrentUser } from "../services/authService";

// Helper to check if we're running in a browser
const isBrowser = typeof window !== "undefined";

interface User {
    id: string;
    name: string;
    email: string;
    role: string;
    reliability_score?: number;
    experience_years?: number;
}

interface AuthContextType {
    user: User | null;
    loading: boolean;
    error: string | null;
    login: (credentials: { email: string; password: string }) => Promise<void>;
    logout: () => void;
    isDevMode: boolean;
    setDevMode: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
    children,
}) => {
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [isDevMode, setIsDevMode] = useState<boolean>(false);

    const router = useRouter();

    const setDevModeFunction = () => {
        const devUser: User = {
            id: "dev-user-001",
            name: "Development User",
            email: "<EMAIL>",
            role: "ADMIN",
            reliability_score: 100,
            experience_years: 10,
        };

        console.log("AuthContext: Setting dev mode user");
        setUser(devUser);
        setIsDevMode(true);
        setError(null);
        setLoading(false);

        // Store dev mode state
        localStorage.setItem("devMode", "true");

        console.log(
            "AuthContext: Dev mode set, current pathname:",
            router.pathname
        );

        // Use replace instead of push to avoid adding to history
        if (router.pathname === "/login") {
            console.log("AuthContext: Redirecting from login to dashboard");
            router.replace("/");
        }
    };

    const initializeAuth = async () => {
        try {
            setLoading(true);
            setError(null);

            console.log("AuthContext: Initializing auth");

            // Check if we're in dev mode first
            const devMode = localStorage.getItem("devMode") === "true";
            console.log("AuthContext: Dev mode check:", devMode);

            if (devMode) {
                console.log(
                    "AuthContext: Restoring dev mode from localStorage"
                );
                const devUser: User = {
                    id: "dev-user-001",
                    name: "Development User",
                    email: "<EMAIL>",
                    role: "ADMIN",
                    reliability_score: 100,
                    experience_years: 10,
                };

                setUser(devUser);
                setIsDevMode(true);
                setLoading(false);
                return;
            }

            // Get stored token
            const token = localStorage.getItem("token");
            console.log("AuthContext: Token exists:", !!token);

            // Only try to get current user if we have a token
            if (token) {
                try {
                    console.log("AuthContext: Fetching current user");
                    const userData = await getCurrentUser(token);
                    setUser(userData);
                    setIsDevMode(false);
                    console.log(
                        "AuthContext: User authenticated:",
                        userData.email
                    );
                } catch (error: any) {
                    console.error("Auth initialization error:", error);

                    // Token exists but is invalid, remove it
                    localStorage.removeItem("token");
                    setUser(null);
                    setIsDevMode(false);
                    console.log("AuthContext: Invalid token removed");
                }
            } else {
                console.log("AuthContext: No token found");
                setUser(null);
                setIsDevMode(false);
            }
        } catch (error: any) {
            console.error("Auth initialization error:", error);
            setError("Authentication initialization failed");
            setUser(null);
            setIsDevMode(false);
        } finally {
            setLoading(false);
            console.log("AuthContext: Auth initialization complete");
        }
    };

    useEffect(() => {
        // Only run in browser
        if (!isBrowser) {
            setLoading(false);
            return;
        }

        initializeAuth();
    }, []); // Empty dependency array to run only once

    const loginUser = async (credentials: {
        email: string;
        password: string;
    }) => {
        try {
            setLoading(true);
            setError(null);

            const { access_token } = await login(credentials);

            if (isBrowser) {
                localStorage.setItem("token", access_token);
                // Clear dev mode when doing real login
                localStorage.removeItem("devMode");
            }

            const userData = await getCurrentUser(access_token);
            setUser(userData);
            setIsDevMode(false);

            if (isBrowser) {
                router.replace("/");
            }
        } catch (error: any) {
            console.error("Login error:", error);
            setError(error.response?.data?.detail || "Invalid credentials");
        } finally {
            setLoading(false);
        }
    };

    const logoutUser = () => {
        if (isBrowser) {
            localStorage.removeItem("token");
            localStorage.removeItem("devMode");
        }
        setUser(null);
        setIsDevMode(false);
        setError(null);
        if (isBrowser) {
            router.replace("/login");
        }
    };

    return (
        <AuthContext.Provider
            value={{
                user,
                loading,
                error,
                login: loginUser,
                logout: logoutUser,
                isDevMode,
                setDevMode: setDevModeFunction,
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error("useAuth must be used within an AuthProvider");
    }
    return context;
};
