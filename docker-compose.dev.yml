version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/conf.d/dev.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - frontend
      - api
    networks:
      - app_network

  api:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./server:/app
    env_file:
      - ./server/.env.dev
    environment:
      - APP_ENV=development
      - APP_NAME=revolving-door-knowledge-system
      - APP_VERSION=0.1.0
      - LOG_LEVEL=DEBUG
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=password
    depends_on:
      - neo4j
    networks:
      - app_network

  frontend:
    build:
      context: ./app
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./app:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
      - WATCHPACK_POLLING=true
    depends_on:
      - api
    networks:
      - app_network

  neo4j:
    image: neo4j:5.18.0
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data_dev:/data
      - neo4j_logs_dev:/logs
      - neo4j_import_dev:/import
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms.memory.heap.initial_size=1G
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=gds.*
    networks:
      - app_network

networks:
  app_network:
    driver: bridge

volumes:
  neo4j_data_dev:
  neo4j_logs_dev:
  neo4j_import_dev: