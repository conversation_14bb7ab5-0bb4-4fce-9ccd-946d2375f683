version: '3.8'

services:
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./certbot/conf:/etc/letsencrypt:ro
    depends_on:
      - frontend
      - api
    networks:
      - app_network

  api:
    build:
      context: ./server
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    expose:
      - "8000"
    env_file:
      - ./server/.env.prod
    environment:
      - APP_ENV=production
      - NEO4J_URI=bolt://neo4j:7687
    depends_on:
      - neo4j
    networks:
      - app_network

  frontend:
    build:
      context: ./app
      dockerfile: Dockerfile.prod
    restart: unless-stopped
    expose:
      - "3000"
    env_file:
      - ./app/.env.prod
    environment:
      - NEXT_PUBLIC_API_URL=/api
      - NODE_ENV=production
    depends_on:
      - api
    networks:
      - app_network

  neo4j:
    image: neo4j:5.18.0
    restart: unless-stopped
    expose:
      - "7474"
      - "7687"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/import
    env_file:
      - ./neo4j.env.prod
    networks:
      - app_network

networks:
  app_network:
    driver: bridge

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import: