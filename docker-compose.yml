version: '3.8'

services:
  api:
    build:
      context: ./server
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./server:/app
    env_file:
      - ./server/.env
    environment:
      - APP_ENV=${APP_ENV:-development}
      - APP_NAME=${APP_NAME:-revolving-door-knowledge-system}
      - APP_VERSION=${APP_VERSION:-0.1.0}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=${NEO4J_USERNAME:-neo4j}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-password}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-key}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
      - JWT_EXPIRATION_MINUTES=${JWT_EXPIRATION_MINUTES:-60}
    depends_on:
      - neo4j

  frontend:
    build:
      context: ./app
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=development
      - WATCHPACK_POLLING=true # Enable polling for Windows compatibility
    depends_on:
      - api
    volumes:
      - ./app:/app
      - /app/node_modules
      - /app/.next

  neo4j:
    image: neo4j:5.18.0
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/import
      - neo4j_plugins:/plugins
    environment:
      - NEO4J_AUTH=${NEO4J_USERNAME:-neo4j}/${NEO4J_PASSWORD:-password}
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms.memory.heap.initial_size=1G
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]  # Add GDS library
      - NEO4J_dbms_security_procedures_unrestricted=gds.*


volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins: